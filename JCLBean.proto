syntax = "proto3";

// Version = 3.0.0.1

import "google/protobuf/any.proto";

package wuhan.jcl.report;			// 避免消息类型的命名冲突

//请求的功能号：功能号集中放在请求的TCP包头和应答的包头
//考虑到对其他协议支持，保持完整性，protobuffer有req请求功能号
enum report_reqno {
	
 ERROR_REQ						= 0;		//通用错误应答
 MONITOR_REQ					= 21;		//主站监控信息
 AUTOGBBQ_REQ					= 22;		//请求股本除权数据
 AUTOBASE_REQ					= 23;		//请求财务数据
 HOSTMORE_REQ					= 31;
 
 PUSH_HQ_SUB					= 134;		//TCP行情推送订阅请求
 PUSH_HQ_UNSUB					= 135;		//TCP行情推送取消订阅请求
 PUSH_STR_HQ_SUB				= 144;
 PUSH_STR_HQ_UNSUB				= 145;
 
 PUSH_DX_SUB					= 150;
 PUSH_DX_UNSUB					= 151;
 PUSH_PZYD_SUB					= 152;
 PUSH_PZYD_UNSUB				= 153;

 CODE_NEW_REQ					= 1111;		//请求对应市场代码链(含中英文名称)
 CODE6_NREQ                     =1110;
 OLDNAME_REQ					= 1120;		//股票曾用名
 ZHSORT_REQ						= 1205;		//请求综合排名数据
 
COMBAVERAGE_NREQ				= 1359;	// 请求Average.csv
COMBBLOCK_NREQ					= 1360; // 请求block.xml

 FJB_REQ						= 1365;		//分价表
 ANALY_RANGE_REQ				= 1367;		//按时间段请求原始K线数据
 MINUTE_REQ						= 1369;		//1-10日分时走势数据
 ANALY_TQ_REQ					= 1370;		//复权K线数据

 SORT_HQ_REQ					= 1727;		//请求分类排序行情
 MULTI_HQ_REQ					= 1728;		//请求指定若干品种行情
 MULTI_EX_HQ_REQ				= 1729;		//请求指定若干品种行情（带指标结果）
 FULL_MMP_REQ					= 1730;		//全档买卖盘口
 L2_TICK_REQ					= 1732;		//逐笔成交明细
 STOCK_GPS_REQ					= 1733;		//全景GPS
 SORT_CODE_REQ					= 1734;		//请求全部排序代码
 MMP_TICK_REQ					= 1735;		//买卖盘口委托队列

 ALL_MONEYFLOW_REQ				= 1801;		//所有股票实时资金流
 ONE_MONEYFLOW_REQ				= 1802;		//单只股票实时资金流
 HIS_MONEYFLOW_REQ				= 1805;		//单只股票历史资金流 DAY
 ALL_MONEYFLOW_XDAYS_REQ		= 1806;		//所有股票多日累积资金流
 HIS_MONEYFLOW_MIN_REQ			= 1807;		//单只股票历史资金流 MIN
 HIS_MONEYFLOW_TIME_REQ			= 1808;		//根据时间段下载资金流
 ONE_MONEYFLOW_XDAY_REQ			= 1818;		//单只股票多日资金数据请求
 CALC_HISDATA_REQ				= 1901;		//计算的数据请求
 CALC_CURDATA_REQ				= 1902;		//当日不停变换的盘中数据

 FUND_HIS_REQ					= 2001;		//基金的历史净值数据
 FUND_ZHSORT_REQ				= 2006;		//基金综合排名
 BLOCK_SORT_HQ_REQ				= 2008;		//板块成分股排序行情
 HSAG_ZDQJ_REQ					= 2009;
 BOND_CODE_REQ					= 2010;		//可转债代码链
 FUND_CODELIST_REQ				= 2011;		//请求分级基金代码信息（带利率规则）
 SORT_EX_HQ_REQ					= 2015;		//请求分类排序行情（支持额外扩展字段）
 SORT_SHQ_EX_REQ				= 2017;
 SORT_STOCK_CONBLOCK_REQ		= 2018;		//个股对应概念板块排序
 SORT_STOCK_BLOCK_REQ			= 2019;
 SORT_NOHQ_EX_REQ				= 2021;
 SORT_NOSHQ_EX_REQ				= 2023;
 SORT_MONEY_RANK_REQ			= 2024;

 HOT_BLOCK_SORT_REQ				= 2030;	//热门股票排序 按积分
 LBDBSB_SORT_REQ				= 2031;	// 连板 首板 断板 股票排序

 FORMULAR_LIST_REQ				= 3030;		//指标列表请求
 FORMULAR_CALC_REQ				= 3031;		//单个指标结果输出（暂时不处理）
 FORMULAR_ONEXG_REQ				= 3032;		//单个指标的选股结果  //一键选股协议
 FORMULAR_ALLXG_REQ				= 3033;		//所有指标的选股结果(新)
 FORMULAR_BKXG_REQ				= 3034;		//单个指标板块日波动结果
 FORMULAR_TPXG_REQ				= 3035;		//指标类型选股结果（for android）
 FORMULAR_ALLXG_HIS_REQ			= 3039;		//所有指标的选股结果(历史)
 FORMULAR_ALLXG_ONE_REQ			= 3040;		//所有指标的选股结果(单个品种)
 FORMULAR_CODELIST_REQ			= 3051;		//指标选股样本空间查询请求
 FORMULAR_CODELIST_MODIFY		= 3052;		//指标选股样本空间修改请求
 FORMULAR_CODELIST_CREATE		= 3053;		//指标选股样本空间分组创建请求
 FORMULAR_CODELIST_DELETE		= 3054;		//指标选股样本空间分组删除请求

 HIS_MINUTE_REQ					= 4068;		//历史分时走势数据
 TICK_REQ						= 4069;		//Tick数据
 HIS_TICK_REQ					= 4070;		//历史Tick数据

 CLOUD_CALC_SORT_REQ			= 5013;		//公式云计算排序
 
// 6000 ~ 6100 预留给公式增值服务 
FORMULARSELFINDEX_REQ			= 6000;	 	// 请求单个公式,应答就是具体 FormularSelfIndex

FORMULARSET_REQ					= 6001;		// 请求整个集合的公式，应答 FormularSet_Ans

// 计算后的公式请求(如果只是提取因子库，不用指定参数，指定也无效，已经计算好了)
RECALCFOMULAR_REQ				= 6002;

 AUCTIONDATA_REQ				= 10029;	//集合竞价带未匹配量
 AFTER_TRADE_REQ				= 10032;	//获取盘后定价交易扩展数据
 AUCTIONDATA_CCALL_REQ			= 10036;	//尾盘集合竞价数据
 ZSCFG_REQ						= 10100;
 COMBBLOCK_EX_NREQ				= 13600; //请求block.xml

 CALCZAFDATA_REQ				= 20000;
 ZDFSECTION_REQ					= 20001;	//涨跌幅区间
 ZDFRESULT_REQ					= 20002;	//涨跌幅结果
 ZDFPROFIT_REQ					= 20003;	//涨跌幅曲线
 ZDFZDTNUM_REQ					= 20004;	//涨跌停个数曲线
 FYZBZTRESULT_REQ				= 20005;	//昨日非一字板涨停品种
 ZDTRESULT_REQ					= 20006;	//涨跌停品种
 ZDFSECTION_TD_REQ				= 20007;	//涨跌幅区间天道

 STRATEGYCENTER_LIST_REQ		= 20100;	//策略中心列表
 STRATEGYCENTER_PROFIT_REQ		= 20101;	//策略中心 收益曲线
 STRATEGYCENTER_SUCCESS_REQ		= 20102;	//策略中心 选股成功率
 STRATEGYONEKEY_REQ				= 20104;	//一键选股
 STRATEGYONEKEY_PROFIT_REQ		= 20105;	//一键选股收益曲线
 STRATEGYONEKEY_RESULT_REQ		= 20106;	//一键选股结果
 STRATEGYPOOL_REQ				= 20107;	//股票池
 BIGDATA_CXQN_REQ				= 20108;	//大数据选股-次新擒牛
 BIGDATA_JHJJ_REQ				= 20109;	//大数据选股-集合竞价
 BIGDATA_ZTZY_REQ				= 20110;	//大数据选股-涨停捉妖
 STRATEGYCENTER_JRTC_REQ		= 20111;	//策略中心 今日调仓
 STRATEGYCENTER_DQCC_REQ		= 20112;	//策略中心 当前持仓
 STRATEGYCENTER_CLNG_REQ		= 20113;	//策略中心 策略牛股
 STRATEGYCENTER_SIGNAL_REQ		= 20114;	//历史信号
 STRATEGY_RMQL_DTXG_REQ			= 20115;	//热门强龙策略请求 动态选股策略请求
 STRATEGY_JZTZ_REQ				= 20116;	//价值投资
 STRATEGY_GSCALCLIST_REQ		= 20117;
 STRATEGY_GSRESULT_REQ			= 20118;
 KSXG_MUL_STRATEGY_NREQ			= 20119;
 SRV_STRATESGZH_ONEKEYCOMMENT_REQ	=	20145;
 STRATEGY_LEVEL_REQ				= 20150;	//评级请求
 
 STRATEGY_SYSTEMPOOL_LIST_REQ		= 20160;	//系统策略池列表
 STRATEGY_SYSTEMPOOL_RESULT_REQ		= 20161;	//系统策略池结果

 RPSDATA_REQ					= 20201;	//RPS数据
 GSKDATA_REQ					= 20202;	//广度深度宽度数据请求

 NEW_PROTOCOL_LBDATA_REQ		= 23041;//连扳数据请求 涨停数据统计
 STATICTISDATA_HIS_ZTLB = 23043;		// 连板天梯信息
 STATICTISDATA_FACTOR = 23044;			// 因子统计数据
 STATICTISDATA_BLOCK = 23045;			// 概念板块成分股涨停数前五统计 
 STATICTISDATA_BLOCK_SCORE = 23046;		// 概念板块积分排序
 STATICTISDATA_JJYZB = 23047;			// 竞价一字板统计
 STATICTISDATA_JTJB = 23048;			// 几天几板
 STATICTISDATA_LBSBDB_STOCKS = 23049;	// 获取昨日连板,昨日首板 昨日短板股票
 STATICTISDATA_ZT_LB_INFO = 23050;		// 获取涨停股票的连板天数和封板资金

 DXJL_REQ						= 20400;	//短线精灵
 HQFD_REQ						= 20401;
 ZTFX_ZDTB_REQ					= 20402;	//涨跌停比
 ZTFX_ZBL_REQ					= 20403;	//炸板率
 ZTFX_JRZF_REQ					= 20404;	//昨日涨停今日涨幅
 BKYD_REQ					= 20405;	//板块(大盘)异动，开和收
 BKFB_REQ					= 20406;	//板块分布
 ZDNUM_REQ					= 20410;	//涨跌平数量
 HQEXDATA_REQ				= 20411;	//停牌家数 非一字板涨停家数 涨停炸板家数
 LTLBDATA_REQ				= 20412;	//龙头连板数据
 LTLBMIN_REQ				= 20413;
 LTLBARR_REQ				= 20414;
 LBTJDATA_REQ				= 20415;

 MONEY_FLOW_REQ					= 20500;	//资金流向
 
 FINANCE_REQ					= 21000;
 FINDOWN_REQ					= 21001;
 FINVALUE_REQ					= 21002;
 FINGPJY_REQ					= 21003;
 FINSCJY_REQ					= 21004;
 FINGPONE_REQ					= 21005;
 SNMONEYFLOW_REQ				= 21006;
 SNMONEYBASE_REQ				= 21007;
 SNMONEYTOP10_REQ				= 21008;
 SNMONEYHOLD_REQ				= 21009;
 HSIZS_REQ						= 21010;
 FINANCE_NREQ					= 21018;
 FINBKJY_NREQ					= 21019;
 FINVALUE_NREQ					= 21020;
 FINGPJY_NREQ					= 21021;
 FINSCJY_NREQ					= 21022;
 FINGPONE_NREQ					= 21023;
 FINANCE_NOW_NREQ				= 21024; //当日专业财务数据 当日全部数据一起返回
 NEWFINANCE_NREQ                = 21025; //最新财务数据请求，按指标函数请求
 NEW_PROTOCOL_KLINE_OFFSET_REQ  = 22010;	//新K线按偏移请求协议
 NEW_PROTOCOL_MINUTE_REQ		= 22020;	//新分时协议
 NEW_PROTOCOL_MANY_MINUTE_REQ	= 22025; //多个品种当日分时
 
 NEW_HQEX_REQ					= 22117;
 NEW_NOHQEX_REQ					= 22123;
 NEW_NOHQ_ZDY_REQ				= 22124;
 NEW_SORT_FILTER_REQ			= 22125; //增加过滤品种逻辑的排序
 NEW_PROTOCOL_EXDATA_REQ		= 22998;
 SRV_STATICTIS_MONEY_DAY_REQ = 23201;	//统计数据(资金流向+..其他统计类数据汇总)日周期
 SRV_STATICTIS_MONEY_MIN_REQ = 23202; 	//统计数据(资金流向+..其他统计类数据汇总)分钟周期
 SRV_STATICTIS_MILTI_MONEY_DAY_REQ = 23203; //统计数据(资金流向+..其他统计类数据汇总) 支持传多个代码 刷新当天资金数据
 
HISCHIP_REQ                     = 24000;        //筹码请求

 JSON_REQ						= 30000;	//请求/应答内容为json格式的协议报文	
 COMBO_REQ						= 30001;	//组合请求，多个请求一个protobuf到服务端，处理完一次性返回给客户端
 BFDAY_DATA_REQ					= 30100;
 KLINE_LABEL_REQ				= 30101;
 KLINE_LABEL_ARR_REQ			= 30102;
 KLINE_MULSTK_ADAY_REQ		= 30103;
 SORT_EXRANK_REQ				= 30200;
 DZLV2_ORDERTICK_REQ			= 30300;
 DZLV2_SIGORTICK_REQ			= 30301;
 DZLV2_MULPK_REQ				= 30302;
 DZLV2_SIGPKINFO_REQ			= 30303;
 DZLV2_KCBPHTICK_REQ			= 30304;
}

enum domain_idx
{
        DI_SHAG = 0;				// 上海A股
        DI_SHBG = 1;				// 上海B股
        DI_SZAG = 2;				// 深圳A股
        DI_SZBG = 3;				// 深圳B股
        DI_SHBOUD = 4;				// 上海债券
        DI_SZBOUD = 5;				// 深圳债券
        DI_AG = 6;					// A股
        DI_BG = 7;					// B股
        DI_BOND = 8;				// 债券
        DI_FUND = 9;				// 基金
        DI_ALLGP = 10;				// 所有品种
        DI_ALLINDEX = 11;				// 所有指数
        DI_ZHONGXIAO = 12;			// 中小企业
        DI_GEM = 13;				// 创业板
        DI_SB = 14;					// 股转系统
        DI_DELISTING = 15;			// 退市整理版

        DI_OPTION = 17;				// 权证
        //DI_GEM = 18;				// 创业板
        DI_BH = 19;					// 渤海商品
        DI_QH_DLSP = 20;			// 大连商品
        DI_QH_ZZSP = 21;			// 郑州商品
        DI_QH_SHSP = 22;			// 上海期货
        DI_QH_GZQH = 23;			// 股指期货
        DI_SJ = 24;					// 上海金
        DI_LDJ = 25;				// 伦敦金
        DI_TJ = 26;					// 天津贵金属
        DI_BTB = 27;						// 比特币
        DI_KZZ=28;						// 可转债
        
        DI_ALL_BLOCK = 30;			// 所有板块
        DI_AREA = 31;				// 地域
        DI_INDUSTRY = 32;			// 行业
        DI_CONCEPT = 33;			// 概念

        DI_RM_BLOCK_ALL_=34;			//热门板块-所有
        DI_RM_BLOCK_AREA_=35;			//热门板块-地域
        DI_RM_BLOCK_INDUSTRY_=36;		//热门板块-行业
        DI_RM_BLOCK_CONCEPT_=37;		//热门板块-概念

        DI_HX=38;						//海峡艺术品交易所-所有品种
        DI_HX_YSZB_=39;					//海峡艺术品交易所-艺术主板
        DI_HX_YSXB_=40;					//海峡艺术品交易所-艺术新版
        DI_HX_YBSC_=41;					//海峡艺术品交易所-邮票收藏
        DI_HX_QBSC_=42;					//海峡艺术品交易所-钱币收藏
        DI_HX_PSJK_=43;					//海峡艺术品交易所-配售缴款
        DI_HX_CCQB_=44;					//海峡艺术品交易所-茶产权版
		
		DI_SZ_BLOCK = 45;
		DI_FG_BLOCK = 46;
		DI_HY_BLOCK = 47;

        DI_QH_QL = 50;				//齐鲁商品
        DI_QH_HQXG=51;					//横琴稀贵
        DI_QH_ALL_LDJ=52;				//伦敦金
        DI_FUND_PENGHUA=53;			//鹏华基金公司发行的17个基金品种
        DI_TEA=54;						//广州茶叶商品期货-所有品种
        DI_TEA_ZS=55;					//广州茶叶商品期货-指数
        DI_TEA_GOODS=56;				//广州茶叶商品期货-商品
        DI_DPT=57;                     //甬交所
        DI_HK=58;						//港股

        DI_AG_HQBJ = 70;				//沪深A股-行情报价
        DI_AG_ZJJC=71;					//沪深A股-资金决策
        DI_AG_CWSJ=72;					//沪深A股-财务数据
        DI_AG_HQTJ=73;					//沪深A股-行情统计
		DI_SCIENCE_TECHNOLOGY=74;		// 科创板
		DI_ETF=75;		                // ETF基金

        DI_OIL = 80;                 //原油
        //DI_BKZJ = 80;				//板块资金
        DI_RMQL = 81;				//热门强龙
        DI_DTXG = 82;				//动态选股

        DI_DDTJ = 90;				//大单统计 同沪深A股资金决策
        DI_JZTZ = 91;				//价值投资

        //板块个股 
        //DI_BLOCK_INDEX_MIN = 100;		//新的板块个数很多，在后面重新定义序号
        DI_FIRST_AREA_BLOCK = 100;
        DI_LAST_AREA_BLOCK = 199;
        DI_FIRST_INDUSTRY_BLOCK = 200;
        DI_LAST_INDUSTRY_BLOCK = 299;
        DI_FIRST_CONCEPT_BLOCK = 300;
        DI_LAST_CONCEPT_BLOCK = 1300;
        //DI_BLOCK_INDEX_MAX = 1300;

        DI_FULLSCREEN_BHGOODS = 1501;			// 渤海商品
        DI_FULLSCREEN_FIVE=1502;				// 5分钟领涨股(A股涨速排名)
        DI_FULLSCREEN_CURRDAY=1503;			// 当日领涨股(A股涨幅排名)
        DI_FULLSCREEN_CURRDAYBK=1504;		// 当日领涨板块(板块指数涨幅排名)
        DI_FULLSCREEN_SHAG=1505;
        DI_FULLSCREEN_SZAG=1506;
        DI_FULLSCREEN_AREA=1507;				// 地域
        DI_FULLSCREEN_INDUSTRY=1508;			// 行业
        DI_FULLSCREEN_CONCEPT=1509;			// 概念
        DI_FULLSCREEN_ZXG=1510;				//自选股
        DI_FULLSCREEN_CURRDAYBK_GG = 1000;	//全景图板块成份股设置表头专用，不做分类使用
        DI_FULLSCREEN_CURRDAYBK_GG_FIRST = 1100;	//DI_FULLSCREEN_CURRDAYBK_GG + DI_BLOCK_INDEX_MIN;
        DI_FULLSCREEN_CURRDAYBK_GG_LAST = 2300;		//DI_FULLSCREEN_CURRDAYBK_GG + DI_BLOCK_INDEX_MAX;

        DI_FUND_HOMEPAGE_ = 4000;		// 分级基金-B基金
        DI_FUND_SUBJECT_=4001;				// 主题基金（挂钩指数、母基金、A基金、B基金）
        DI_FUND_B_=4002;						// B类基金
        DI_FUND_SUBJECT_HOT_=4003;			// 主题基金
        DI_FUND_HOMEPAGE_A_=4004;				// 分级基金-A基金
        DI_FUND_HOMEPAGE_M_=4005;				// 分级基金-母基
        DI_FUND_HOMEPAGE_FJ_T2_=4006;			// 分级基金-分级T+2
        DI_FUND_HOMEPAGE_FJ_T1_=4007;			// 分级基金-分级T+1
        DI_FUND_HOMEPAGE_FJ_T0_=4008;			// 分级基金-分级T+0
        DI_FUND_MONEY_T0_=4009;				// 货币基金T+0
        DI_FUND_MONEY_T1_=4010;				// 货币基金T+1
		
		DI_FIRST_SZ_BLOCK=5500;
		DI_FIRST_FG_BLOCK=6000;

  
        DI_SORT_MAX = 9000;				// 服务器支持排序分界值（以后可以调整）
        DI_SYSTEMBLOCK=9001;					// 系统板块

        DI_CUSTOM=9002;						// 自选股
        DI_CUSTOM_ZJJC=9003;					//自选股-资金决策
        DI_CUSTOM_CWSH=9004;					//自选股-财务数据
        DI_CUSTOM_HQTJ=9005;					//自选股-行情统计

        DI_SHHG = 9007;						// 上证回购
        DI_SZHG = 9008;						// 深圳回购

        DI_ZBXG = 9011;		        	// 指标选股  9011  wangzhuo 	

        DI_FUND_A=9012;						// A类轮动

        DI_IK_UP = 9100;				//智能K线 最可能上涨
        DI_IK_ZY=9101;						//智能K线 匹配涨最优
        DI_IK_XS=9102;						//智能K线 今日最相似
        DI_GZ_HY=9103;						//个股诊断 行业
        DI_GZ_ZS=9104;						//个股诊断 指数
        DI_GZ_CUSTOM=9105;					//个股诊断 自选股

        DI_CUSTOM_HQCAL = 9018;			//行情计算
        DI_CXQN_SCDK = 9200;			//次新擒牛 首次打开
        DI_CXQN_CGHF = 9201;			//次新擒牛 成功回封
        DI_CXQN_YZLB = 9202;			//次新擒牛 一字连板
        DI_CXQN_ZCXG = 9203;			//次新擒牛 再创新高
        DI_CXQN_JDCD = 9204;			//次新擒牛 阶段超跌
        DI_CXQN_DJQL = 9205;			//次新擒牛 低价潜力

        DI_JHJJ_ZTSP = 9300;			//集合竞价 涨停试盘
        DI_JHJJ_DTSP = 9301;			//集合竞价 跌停试盘
        DI_JHJJ_JJKD = 9302;			//集合竞价 集竞看多
        DI_JHJJ_JJKK = 9303;			//集合竞价 集竞看空
        DI_JHJJ_DKHZ = 9304;			//集合竞价 多空混战

        DI_ZTZY = 9400;			//涨停捉妖		

        DI_CLZX_JRTC = 9410;			//策略中心 今日调仓
        DI_CLZX_DQCC = 9411;			//策略中心 当前持仓
        DI_CLZX_CLNG = 9412;			//策略中心 策略牛股

        DI_SCRD = 9450;			//市场热点

        DI_AG_ZSOTHER = 9500;		   //其它指数
        DI_FILTER = 9588;				// 过滤器
        DI_GPCXG = 9600;				// 股票池
        DI_AG_GLPZ = 9700;				// 关联品种

        DI_FIRST_FORMULAR_BLOCK = 9800;	//云选股板块
        DI_LAST_FORMULAR_BLOCK = 9900;

        DI_CONDITION = 9999;			// 条件股		注: 此处DI_CONDITION一定要设置为DI_FIRST_CUSTOM_BLOCK-1
        DI_FIRST_CUSTOM_BLOCK = 10000;	// 自定义板块
}

enum lbsbdb_type
{
	ALL = 0;	// 所有
	SB  = 1;	// 首板
	LB  = 2;	// 连板
	DB  = 3;	// 断板
}
enum coltype_idx
{
	ZQDM = 0;	//代码
	ZQJC = 1;	//证券名
	ZRSP = 2;	//昨收
	JRKP = 3;	//今开
	ZGCJ = 4;	//最高
	ZDCJ = 5;	//最低
	ZJCJ = 6;	//现价
	ZGJM = 7;	//叫买价
	ZDJM = 8;	//叫卖价
	CJL = 9;	//总手
	CJJE = 10;	//总金额
	XS = 11;	//现手
	QRSD = 12;	//日升跌
	QBSD = 13;	//笔升跌
	ZAF = 14;	//涨幅
	ZEF = 15;	//振幅
	JUNJ = 16;	//均价
	SYL = 17;	//市盈率
	WTB = 18;	//委比
	NP = 19;	//内盘
	WP = 20;	//外盘
	LWB = 21;	//内外比
	WLC = 22;	//委量差
	BJL1 = 23;	//买手一
	SJL1 = 24;	//卖手一
	BJ1 = 25;	//买价一
	SJ1 = 26;	//卖价一
	BJ2 = 27;	//买价二
	BJL2 = 28;	//买手二
	SJ2 = 29;	//卖价二
	SJL2 = 30;	//卖手二
	BJ3 = 31;	//买价三
	BJL3 = 32;	//买手三
	SJ3 = 33;	//卖价三
	SJL3 = 34;	//卖手三
	LIANGB = 35;	//量比
	J_HSL = 36;	//换手率
	J_LTGB = 37;	//流通股本
	J_LTSZ = 38;	//流通市值
	J_ZSZ = 39;	//总市值
	DKPH = 40;	//多空平衡
	DTHL = 41;	//多头获利
	DTZS = 42;	//多头止损
	KTHB = 43;	//空头回补
	KTZS = 44;	//空头止损
	QRD = 45;	//强弱度
	ZANGSU = 46;	//涨速
	HYD = 47;	//活路度
	MBZL = 48;	//每笔均量
	MBHSL = 49;	//每笔换手
	J_GXRQ = 50;	//更新日期
	J_START = 51;	//上市日期
	J_ZGB = 52;	//总股本
	J_GJG = 53;	//国家股
	J_FQRFRG = 54;	//发起人法人股
	J_FRG = 55;	//法人股
	J_BG = 56;	//B股
	J_HG = 57;	//H股
	J_ZGG = 58;	//职工股
	J_ZZC = 59;	//总资产(千元)
	J_LDZC = 60;	//流动资产
	J_GDZC = 61;	//固定资产
	J_WXZC = 62;	//无形资产
	J_CQTZ = 63;	//长期投资
	J_LDFZ = 64;	//流动负债
	J_CQFZ = 65;	//长期负债
	J_ZBGJJ = 66;	//资本公积金
	J_JZC = 67;	//股东权益(就是净资产)
	J_ZYSY = 68;	//主营收入
	J_ZYLY = 69;	//主营利益
	J_QTLY = 70;	//其它利益
	J_YYLY = 71;	//营业利益
	J_TZSY = 72;	//投资收益
	J_BTSY = 73;	//补贴收入
	J_YYWSZ = 74;	//营业外收支
	J_SNSYTZ = 75;	//上年损益调整
	J_LYZE = 76;	//利益总额
	J_SHLY = 77;	//税后利益
	J_JLY = 78;	//净利益
	J_WFPLY = 79;	//未分配利益
	J_TZMGJZ = 80;	//调整每股净资产
	J_JYL = 81;	//净益率
	J_MGWFP = 82;	//每股未分配
	J_MGSY = 83;	//每股收益
	J_MGGJJ = 84;	//每股公积金
	J_MGJZC = 85;	//每股净资产
	J_GDQYB = 86;	//股东权益比
	ZBCOL = 87; 	//指标排序栏目
	SPELL_CODE = 88;	//外汇简称
	QH_JSJ = 89;	//期货结算价
	QH_YJSJ = 90;	//期货前结算价
	//大福三方特有
	SPREAD = 91;	//买卖差价
	BSUNIT = 92;	//买卖单位
	CURRENCYNAME = 93;	//货币单位
	AVERPRICE = 94;	//平均价
	YIELDVAL = 95;	//收益率
	HIS_HIGH = 96;	//年最高
	HIS_LOW = 97;	//年最低
	IEP = 98;	//参考价
	IEV = 99;	//参考量
	MRKCAP = 100;	//市值
	//与现有17重复 PE				= 101;	//市盈率
	PREMINUM = 102;	//溢价%
	GEARING = 103;	//贡杆比率%
	EXECPRICE = 104;	//行使价
	CONVRATIO = 105;	//换购比率
	EXPIREDATE = 106;	//到期日
	NOTAXPRCIE = 107;	//交易价（不含税价，下同）
	DEPOSITMONEY = 108;	//订货保证金
	DAYDEPOSITMONEY = 109;	//增仓保证金
	AVGTAXPRICE = 110;	//平均含税价
	ZXGTIME = 117;	//添加日期
	SSBK = 118;	//所属板块
	ZLJZ = 119;	//主力净值
	ZLZB = 120;	//主力占比
	SMJZ = 121;	//私募净值
	SMZB = 122;	//私募占比
	HTMLXX = 129;	//Html信息
	URGENTXX = 130;	//紧急通告(营业部通知)
	NEWSGATE1 = 131;	//新闻搜索器
	NEWSGATE2 = 132;	//新闻搜索器
	I_ZJJLR = 133;	//资金净流入
	I_ZJJLRBL = 134;	//资金净流入占盘比
	I_ZLJLR = 135;	//主力净流入
	I_ZLJLRBL = 136;	//主力净流入占盘比
	I_ZLZJLR = 137;	//主力资金流入
	I_ZLZJLRBL = 138;	//主力资金流入占盘比
	I_ZLZJLC = 139;	//主力资金流出
	I_ZLZJLCBL = 140;	//主力资金流出占盘比
	I_INOUTZJBL = 141;	//内外盘资金比
	I_ZLCJBL = 142;	//主力成交占比
	BH_DHL = 143;	//订货量
	BH_RZC = 144;	//日增仓
	CCL = 145;	//持仓
	ZCC = 146;	//昨持仓,
	CCZJ = 147;	//持仓增减
	S_ZLLD = 148;	//主力雷达
	S_ZLQD = 149;	//主力强度
	S_ZJDL = 150;	//资金动力
	ROWNUM = 151;	//序号
	SEPARATOR = 152;	//分隔
	ZS_LZ = 153;	//领涨成分股
	ZS_LD = 154;	//领跌成分股
	ZS_CFG = 155;	//成分股个数
	ZS_ZDP = 156;	//涨跌平
	ZS_SZJS = 157;	//上涨家数
	ZS_XDJS = 158;	//下跌家数
	ZS_PJS = 159;	//平家数
	FUND_BCODE = 160;	//基金B代码
	FUND_BNAME = 161;	//基金B名称
	FUND_BPRICE = 162;	//基金B现价
	FUND_BZAF = 163;	//基金B涨幅%
	FUND_BJINGZHI = 164;	//基金B净值(估算)
	FUND_BYJL = 165;	//基金B溢价率%
	FUND_BFEBHL = 166;	//基金B份额变化率%
	FUND_SGTLKJ = 167;	//申购套利%
	FUND_SHTLKJ = 168;	//配对套利%
	FUND_ABPRICE = 169;	//基金AB合并价
	FUND_MCODE = 170;	//母级基金代码
	FUND_MNAME = 171;	//母级基金名称
	FUND_MJINGZHI = 172;	//母级基金净值
	FUND_ZSCODE = 173;	//挂钩指数代码
	FUND_ZSNAME = 174;	//挂钩指数名称
	FUND_ZSZAF = 175;	//挂钩指数涨幅%
	FUND_ACODE = 176;	//基金A代码
	FUND_ANAME = 177;	//基金A名称
	FUND_APRICE = 178;	//基金A现价
	FUND_AZAF = 179;	//基金A涨幅%
	FUND_AJINGZHI = 180;	//基金A净值(估算)
	FUND_AYJL = 181;	//基金A溢价率%
	FUND_BFE = 182;	//基金B份额
	FUND_BJZBHL = 183;	//基金B净值增长率%
	FUND_FOLDUP = 184;	//上折阈值
	FUND_FOLDDOWN = 185;	//下折阈值
	FUND_JZFOLDUP = 186;	//上折差距%
	FUND_JZFOLDDOWN = 187;	//下折差距%
	FUND_BNOTICE = 188;	//基金B公告
	FUND_SUBJECT = 189;	//基金主题
	FUND_SGPRICE = 190;	//申购价格
	FUND_SHPRICE = 191;	//赎回价格
	FUND_ABYJL = 192;	//整体溢价率%
	FUND_MJZBHL = 193;	//母级基金净值增长率%
	FUND_MNOTICE = 194;	//母级基金公告
	FUND_FEZB = 195;	//AB份额比率（A:B）
	FUND_AJZBHL = 196;	//基金A净值增长率%
	FUND_AFOLDPRICE = 197;	//基金A折价%
	FUND_APROFIT = 198;	//基金A到期收益%
	FUND_AFE = 199;	//基金A份额
	FUND_AFEBHL = 200;	//基金A份额变化率%
	FUND_ARESTDAY = 201;	//基金A剩余年限
	FUND_AENDDATE = 202;	//基金A定期折算
	FUND_AYDPROFIT = 203;	//基金A约定收益率%
	FUND_ANOTICE = 204;	//基金A公告
	MONEY_ZLJME_1 = 205;	//1日主力净买额
	MONEY_ZLJME_2 = 206;	//2日主力净买额
	MONEY_ZLJME_3 = 207;	//3日主力净买额
	MONEY_ZLJME_4 = 208;	//4日主力净买额
	MONEY_ZLJME_5 = 209;	//5日主力净买额
	MONEY_ZLJZB_1 = 210;	//1日主力净占比
	MONEY_ZLJZB_2 = 211;	//2日主力净占比
	MONEY_ZLJZB_3 = 212;	//3日主力净占比
	MONEY_ZLJZB_4 = 213;	//4日主力净占比
	MONEY_ZLJZB_5 = 214;	//5日主力净占比
	MONEY_SHJME_1 = 215;	//1日散户净买额
	MONEY_SHJME_2 = 216;	//2日散户净买额
	MONEY_SHJME_3 = 217;	//3日散户净买额
	MONEY_SHJME_4 = 218;	//4日散户净买额
	MONEY_SHJME_5 = 219;	//5日散户净买额
	MONEY_SHJZB_1 = 220;	//1日散户净占比
	MONEY_SHJZB_2 = 221;	//2日散户净占比
	MONEY_SHJZB_3 = 222;	//3日散户净占比
	MONEY_SHJZB_4 = 223;	//4日散户净占比
	MONEY_SHJZB_5 = 224;	//5日散户净占比
	FUND_JINGZHI = 225;	//净值(估算)
	FUND_YJL = 226;	//溢价率%
	FUND_UPDATEDATE = 227;	//净值更新日期
	FUND_POSITION = 228;	//基金仓位系数
	FUND_B_REAL_JZ = 229;	//B基金公布净值
	FUND_PRICELEVEL = 230;	//B基金价格杠杆
	FUND_LEVEL = 231;	//B基金净值杠杆
	FUND_REAL_LEVEL = 232;	//B基金实际杠杆
	FUND_FINANCE = 233;	//B基金融资成本
	FUND_ABYJL_T1 = 234;	//T-1溢价率
	FUND_ABYJL_T2 = 235;	//T-2溢价率
	FUND_AFE_INC = 236;	//A基金份额新增(万份)
	FUND_RATE_RULE = 237;	//利率规则
	FUND_RATE = 238;	//本期利率
	FUND_RATE_NEXT = 239;	//下期利率
	FUND_M_REL_JZ = 240;	//母基公布净值
	FUND_CREATE = 241;	//基金成立日期
	FUND_A_CJJE = 242;	//A基金成交额(亿)
	FUND_B_CJJE = 243;	//B基金成交额(亿)
	FUND_A_HSL = 244;	//A基金换手率
	FUND_B_HSL = 245;	//B基金换手率
	FUND_BFE_INC = 246;	//B基金份额新增(万份)
	FUND_MPRICE = 247;	//母基价格
	FUND_MZAF = 248;	//母基涨幅
	FUND_MYJL = 249;	//母基溢价率
	FUND_MABYJL = 250;	//母子溢价率
	FUND_ENDDATE = 251;	//基金到期日期
	FUND_M_TYPE = 252;  //基金类型
	//FUND_END = 252;	//基金排序终止列
	TIPDIS = 253;	//
	ZGCODE = 254;	//正股代码
	ZGNAME = 255;	//正股名称
	ZGPRICE_0 = 256;	//正股价
	ZGZAF = 257;	//正股涨幅
	ZGASSETS = 258;	//正股净值产
	ZGRATE = 259;	//正股市净率
	ZGQSR = 260;	//转股起始日
	ZGPRICE_1 = 261;	//转股价
	ZGEVALUE = 262;	//转股价值
	YJRATE = 263;	//溢价率
	HSCFB = 264;	//回售触发比
	HSCFPRICE = 265;	//回售触发价
	HSPRICE = 266;	//回售价
	HSQSR = 267;	//回售起始日
	QSCFB = 268;	//强赎触发比
	QSCFPRICE = 269;	//强赎触发价
	QSPRICE = 270;	//强赎价
	QSQSR = 271;	//强赎起始日
	BONDAMT = 272;	//债券规模
	RESTAMT = 273;	//剩余规模
	ZZRATE = 274;	//转债占比
	ZZSTARTDATE = 275;	//转债发行日期
	ZZENDDATE = 276;	//转债到期日期
	RESTDAY = 277;	//剩余年限
	INTEREST = 278;	//利息
	SQSY = 279;	//税前收益
	SHSY = 280;	//税后收益
	ZTJG = 281;	//行情涨停价
	DTJG = 282;	//行情跌停价
	GG_F10 = 283;	//个股资讯
	GPCTIME = 284;	//股票池入选时间
	GPCPRICE = 285;	//股票池入选价格
	GPCZAF = 286;	//股票池入选涨幅
	GPC5DAYZAF = 287;	//股票池入选后5日最大涨幅
	SECZFCOL = 288;	//区域涨跌幅
	IKSZGL = 289;	//智能K线上涨概率
	IKPPGG = 290;	//智能K线匹配个股
	IKPPTIME = 291;	//智能K线匹配时间段
	IKPPD = 292;	//智能K线匹配度
	ZAF_5MIN = 293;	//5分钟涨幅
	ZAF_3DAYS = 294;	//3日涨幅
	ZAF_5DAYS = 295;	//5日涨幅
	ZAF_20DAYS = 296;	//20日涨幅
	ZANG_DAYS = 297;	//连涨天数
	HIGH_20DAYS = 298;	//20日最高价
	LOW_20DAYS = 299;	//20日最低价
	HIGH_HIS = 300;	//历史最高价
	LOW_HIS = 301;	//历史最低价
	ZAF_90DAYS = 302;	//3个月涨幅
	ZAF_180DAYS = 303;	//半年涨幅
	ZAF_365DAYS = 304;	//一年涨幅
	MONEY_BIGIN = 305;	//资金大单流入
	MONEY_BIGOUT = 306;	//资金大单流出
	MONEY_PUREBIG = 307;	//资金大单净额
	MONEY_MIDIN = 308;	//资金中单流入
	MONEY_MIDOUT = 309;	//资金中单流出
	MONEY_PUREMID = 310;	//资金中单净额
	MONEY_SMALLIN = 311;	//资金小单流入
	MONEY_SMALLOUT = 312;	//资金小单流出
	MONEY_PURESMALL = 313;	//资金小单净额
	DIE_DAYS = 315;	//连跌天数
	ZB_STATE0 = 316;	//指标输出0
	ZB_STATE1 = 317;	//指标输出1
	JHJJ_SPTIME = 318;	//集合竞价-试盘时间
	JHJJ_JJDK = 319;	//集合竞价-集竞多空
	ZTZY_PRICE = 320;	//涨停捉妖-入选价格
	ZTZY_ZF = 321;	//涨停捉妖-入选涨幅
	ZTZY_5DAYZF = 322;	//涨停捉妖-入选后五日涨幅
	ZTZY_TIME = 323;	//涨停捉妖-入选时间
	JRTC_TC = 324;	//策略中心-今日调仓-调仓
	JRTC_TIME = 325;	//策略中心-今日调仓-时间
	JRTC_PRICE = 326;	//策略中心-今日调仓-价格
	JRTC_ZYPRICE = 327;	//策略中心-今日调仓-止盈
	JRTC_ZSPRICE = 328;	//策略中心-今日调仓-止损
	DQCC_TIME = 329;	//策略中心-当前持仓-时间
	DQCC_PRICE = 330;	//策略中心-当前持仓-价格
	DQCC_DQSY = 331;	//策略中心-当前持仓-当前收益
	DQCC_ZYPRICE = 332;	//策略中心-当前持仓-止盈价格
	DQCC_ZSPRICE = 333;	//策略中心-当前持仓-止损价格
	CLNG_INTIME = 334;	//策略中心-策略牛股-调入时间
	CLNG_PRICE = 335;	//策略中心-策略牛股-调入价格
	CLNG_OUTTIME = 336;	//策略中心-策略牛股-调出时间
	CLNG_OUTPRICE = 337;	//策略中心-策略牛股-调出价格
	CLNG_ZAF = 338;	//策略中心-策略牛股-涨幅
	CLNG_INZAF = 339;	//策略中心-策略牛股-次日涨幅
	IK_ZAF = 340;	//智能K线上涨幅度
	CXQN_LBS = 341;	//次新擒牛-连板数
	CXQN_LJZF = 342;	//次新擒牛-累计涨幅
	CXQN_SSJG = 343;	//次新擒牛-上市价格
	BIGDATA_GPDM = 344;	//大数据选股-股票代码
	ZB_STATE2 = 345;	//指标输出2
	ZB_STATE3 = 346;	//指标输出3
	ZB_STATE4 = 347;	//指标输出4

	JDTJ_QSPJ = 352;  //阶段区间统计 -前收盘价
	JDTJ_KPJ = 353;	//阶段区间统计 -开盘价
	JDTJ_ZGJ = 354;	//阶段区间统计-最高价
	JDTJ_ZDJ = 355;	//阶段区间统计-最低价
	JDTJ_SPJ = 356;	//阶段区间统计-收盘价
	JDTJ_CJL = 357;	//阶段区间统计-成交量
	JDTJ_CJE = 358;  //阶段区间统计-成交额
	JDTJ_JQJJ = 359;	//阶段区间统计-加权均价
	JDTJ_QJZF = 360;	//阶段区间统计-区间涨幅
	JDTJ_QJZHENGF = 361;	//阶段区间统计- 区间振幅
	JDTJ_QJHSL = 362;	//阶段区间统计-换手率
	JDTJ_QJLBFD = 363;	//阶段区间统计-量变幅度
	LevelGain = 365;	//盈利能力
	LevelGrow = 368;	//成长能力
	LevelSafe = 369;	//安全性
	LevelMain = 370;	//主力动向
	LevelCost = 371;	//价值评估
	LevelBonus = 372;	//分红能力
	PJ_SHORT = 387;		//评级短期强弱 (对应排序的定义不要修改)
	PJ_MIDDLE = 388;	//评级中期强弱
	PJ_ALL = 390;		//评级综合
	BLOCK_ZTNUM		= 391;	// 板块涨停家数
	BLOCK_DTNUM		= 392;	// 板块跌停家数
	ZAF_10DAYS		= 393;// 10日涨幅
	ENGLISH_NAME = 394;	//英文名称
	ENGLISH_ABBR = 395;	//英文简称
	STK_NKEY = 396;		//NKEY
	J_SJL = 397;	//市净率
	ZAF_60DAYS = 398; //60日涨幅
	//增加看盘复盘有用字段：
	SSHYNAME = 399;	//所属行业名称
	OPEN_P = 400;			// 开盘% ：（开盘 - 昨收） / 昨收 * 100
	HIGH_P = 401;			// 最高% ：（最高 - 昨收） / 昨收 * 100
	LOW_P = 402;			// 最低% ：（最低 - 昨收） / 昨收 * 100
	ZF_New = 403;			// 实体涨幅：（现价 - 今开） / 今开 * 100
	UNDERTOW = 404;			// 回头波:（现价 - 最高） / 最高 * 100
	ATTACKWAVE = 405;		// 攻击波 : （现价 - 最低） / 最低 * 100
	ZXG_PRICE = 406;		// 自选价 : 当时记录自选日字段时候的现价
	ZXG_PROFIT = 407;		// 自选收益：（现价 - 自选价） / 自选价 * 100 * /
	CLOSE_LAST = 416; //最近收盘价
	MONEYJLR_5DAY = 420;	// 5日资金净流入
	MONEYJLR_20DAY = 421;	// 20日资金净流入
	MONEYJLR_60DAY = 422;	// 60日资金净流入
	MONEY_5DAYRATE = 423;	// 5日资金净流入占成交额比例
	MONEY_20DAYRATE = 424;  // 20日资金净流入占成交额比例
	MONEY_60DAYRATE = 425;	// 60日资金净流入占成交额比例
	MONEYJLR_3DAY = 426;	// 3日资金净流入
	MONEY_3DAYRATE = 427;	// 3日资金净流入占成交额比例
	VOLJLR_DAY = 428;	//成交量净流入
	VOLJLR_3DAY = 429;	//3日成交量净流入
	VOLJLR_5DAY = 430;	//5日成交量净流入
	VOLJLR_20DAY = 431; //20日成交量净流入
	VOLJLR_60DAY = 432; //60日成交量净流入
	MONEYJLR_10DAY = 433; //10日资金净流入
	ZAF_CURYEAR = 434; //年初至今涨幅

	SSDYNAME = 435;

	MONEY_PUREIN_3DAY = 450;	// 3日多空资金净流入
	MONEY_SUPER_3DAY = 451;		// 3日净超大
	MONEY_BIG_3DAY = 452;		// 3日净大单
	MONEY_MID_3DAY = 453;		// 3日净中单
	MONEY_SMALL_3DAY = 454;		// 3日净小单
	MONEY_PUREIN_5DAY = 455;	// 5日多空资金净流入
	MONEY_SUPER_5DAY = 456;		// 5日净超大
	MONEY_BIG_5DAY = 457;		// 5日净大单
	MONEY_MID_5DAY = 458;		// 5日净中单
	MONEY_SMALL_5DAY = 459;		// 5日净小单
	MONEY_PUREIN_10DAY = 460;	// 10日多空资金净流入
	MONEY_SUPER_10DAY = 461;	// 10日净超大
	MONEY_BIG_10DAY = 462;		// 10日净大单
	MONEY_MID_10DAY = 463;		// 10日净中单
	MONEY_SMALL_10DAY = 464;	// 10日净小单
	MONEY_SHPUERIN_10DAY = 465;	// 10日游资资金净流入
	MONEY_RATE_10DAY = 466;		// 10日主力净比

	MONEY_ZL_ZC = 467;			// 今日主力增仓
	MONEY_ZL_ZC_3DAY = 468;		// 3日主力增仓
	MONEY_ZL_ZC_5DAY = 469;		// 5日主力增仓
	MONEY_ZL_ZC_10DAY = 470;	// 10日主力增仓
	MONEY_DK_ZC = 471;			// 今日多空增仓
	MONEY_DK_ZC_3DAY = 472;		// 3日多空增仓
	MONEY_DK_ZC_5DAY = 473;		// 5日多空增仓
	MONEY_DK_ZC_10DAY = 474;	// 10日多空增仓
	MONEY_SH_ZC = 475;			// 今日游资增仓
	MONEY_SH_ZC_3DAY = 476;		// 3日游资增仓
	MONEY_SH_ZC_5DAY = 477;		// 5日游资增仓
	MONEY_SH_ZC_10DAY = 478;	// 10日游资增仓
	MONEY_SUPER_PURIN = 479;	// 今日超大净流入
	MONEY_BIG_PURIN = 480;		// 今日大单净流入

	NDAYS_NZT = 481;	//几天几板

	KZZ_ZGZAF = 482;
	KZZ_ZGPRICE = 483;
	KZZ_ZGNKEY = 484;
	BLOCK_JQZAF = 485;
	KZZ_ZGJZ = 486;

	EXDATA_FDE = 563;				//封单额
	BLOCK_SCORE = 700;				// 板块积分
}

//天道 因子类型定义
enum enum_factor_type
{
	ENUM_FACTOR_TYPE_START= 0;
	ENUM_FACTOR_TYPE_INDEX_AMOUNT = 1;		//指数成交金额
	ENUM_FACTOR_TYPE_ZT_COUNTS =2 ;			//涨停个股数 用于涨跌统计柱状图显示
	ENUM_FACTOR_TYPE_LB_COUNTS = 3;			//连板家数
	ENUM_FACTOR_TYPE_GREEN5 = 4;			// -5%以下个股
	ENUM_FACTOR_TYPE_KQXYB = 5;				//亏钱效应比
	ENUM_FACTOR_TYPE_SB_RED = 6;			//首板红盘比
	ENUM_FACTOR_TYPE_JRDM = 7;				//今日首板大面比<0.3
	ENUM_FACTOR_TYPE_LB_RED = 8;			//连板红盘比
	ENUM_FACTOR_TYPE_LB_PERCET = 9;			//连板比例>0.8看羊群效应
	ENUM_FACTOR_TYPE_LB_DM = 10;			//今日连板大面比<0.3看接力意愿
	ENUM_FACTOR_TYPE_LB_GREEN = 11;			//昨日连板断板绿盘比<0.5看容错
	ENUM_FACTOR_TYPE_ZT_NOST_COUNTS = 12;	//实际涨停家数(过滤st）
	ENUM_FACTOR_TYPE_DT_NOST_COUNTS = 13;	//实际跌停家数(过滤st）

};

enum	Period_idx
{
	PERIOD_MIN5    = 0;	// 5分钟k线
	PERIOD_MIN15   = 1;
	PERIOD_MIN30   = 2;
	PERIOD_HOUR    = 3; 
	PERIOD_DAY     = 4;
	PERIOD_WEEK    = 5;
	PERIOD_MONTH   = 6; 
	/////////////
	PERIOD_MIN1    = 7; 
	PERIOD_MINN	= 8;
	PERIOD_DAYN    = 9;
	
	PERIOD_SEASON	= 10;		//季线,需要下载日线数据
	PERIOD_YEAR	= 11;		//年线,需要下载日线数据
}


// 包头 + 包体
message	report_package 
{
	uint32		Version=1;
	uint32		cookie=2;
	uint32		MainID=3;
	uint32		AssisID=4;
	uint32		req=5;
	google.protobuf.Any			packdata=6;		// 下面是具体的包体
}

//包体：

	
message tagReportPackageArray
{
	repeated	report_package		reportpack=1;	// 组合包，可以多个包一次性请求
}
	
			
message	tagInt32
{
	uint32	i = 1;
}
message	tagInt64
{
	uint64	i = 1;
}
message	tagFloat
{
	float	f = 1;
}
message	tagDouble
{
	double	d = 1;
}
message	tagInt32Array
{
	repeated	uint32	i = 1;
}
message	tagInt64Array
{
	repeated	uint64	i = 1;
}
message	tagFloatArray
{
	repeated	float	f = 1;
}
message	tagDoubleArray
{
	repeated	double	d = 1;
}
message tagString
{
	string		s=1;
}

message tagCodeWithNkey
{
	uint64	nkey=1;		// 品种内部编码,如果提供内部编码，优先识别内部编码，code不用设置；如果不用内部编码，要设置为0
    int32	setcode=2;
    string	code=3;
}

message	tagCodeWithNkeyArray
{
	repeated	tagCodeWithNkey	code=1;
}
/**
 * @brief 最优价50档委托队列数据结构（需与客户端统一）
*/
message BSQueue
{
	uint32	total =1;		//实际总委托笔数
	uint32	count =2;		//发布的笔数
	uint32	add = 3;
	uint32	complete=4;
	uint32	update=5;
	uint32	cancel=6;
	repeated double	volumes=7;
	repeated uint32 state=8;
}

message NewBroker
{
	uint32 pos = 1;
	uint64 broker = 2;
}

message NewMDHKBrokerQueue
{
	int64 receive_time = 1;
	uint32 broker_cnt = 2;
	repeated NewBroker broker = 3;
}


/**
 * @brief 支持Level2格式的行情数据结构（需与客户端统一）
*/
message CurrStockDataBEx
{
	uint64		nkey=1;						// 品种内部编码
	uint32		Source=2;						// 行情来源
	uint32		Status=3;						// 品种交易状态

	uint64		QuoteTime=4;					// 行情时间
	uint32		InOutFlag=5;					// 内外盘标志
	uint32		TickCount=6;					// TICK笔数
	uint32		HqCount=7;					// 分比行情笔数
	uint64		TradeCount=8;					// 成交笔数
	double		PreClosePrice=9;				// 前收盘价
	double		OpenPrice=10;					// 今开盘价
	double		HighPrice=11;					// 最高价
	double		LowPrice=12;					// 最低价
	double		NowPrice=13;					// 最新价
	double		zangsu=14;						// 涨速
	double		AveragePrice=15;				// 平均成交价格
	double		PriceDiff=16;					// 笔升跌(价位差)
	double		LimitUpPrice=17;				// 涨停价
	double		LimitDownPrice=18;				// 跌停价
	double		TaxPrice=19;					// 渤海现价含税价
	double		AverageTaxPrice=20;			// 渤海平均含税价
	double		PreSettlePrice=21;				// 期货昨结算价
	double		SettlePrice=22;				// 期货结算价
	double		PERatio=23;					// 市盈率
	double		Volume=24;						// 总成交量(手)
	double		NowVol=25;						// 最近一笔成交量(手)
	double		Amount=26;						// 总成交金额(元)
	double		NowAmount=27;					// 最近一笔成交金额(元)
	double		Inside=28;						// 委买总成交量(手),内盘
	double		Outside=29;					// 委卖总成交量(手),外盘
	double		PreVolInStock=30;				// 期货昨持仓量
	double		VolInStock=31;					// 持仓量
	double		VolInStockDiff=32;				// 持仓差 
	double		TotalBuyVolume=33;				// 委买申报总量(手)
	double		TotalSellVolume=34;			// 委卖申报总量(手)
	double		BuyAveragePrice=35;			// 委买平均申报价格
	double		SellAveragePrice=36;			// 委卖平均申报价格
	uint32		AllBuyPriceCount=37;				// 委买申报价位总数
	uint32		BuyTickCount=38;				// 委买申报总笔数
	uint32		AllSellPriceCount=39;				// 委卖申报价位总数
	uint32		SellTickCount=40;				// 委卖申报总笔数
	repeated	double		BuyPrice=41;		// 最优十档委买申报价
	repeated	double		BuyVolume=42;		// 最优十档委买申报量
	repeated	double		SellPrice=43;		// 最优十档委卖申报价
	repeated	double		SellVolume=44;		// 最优十档委卖申报量
	BSQueue		BuyQueue=45;					// 最优买价50档买卖队列
	BSQueue		SellQueue=46;					// 最优卖价50档买卖队列
	double		PreNetValue=47;				// 基金T-1日净值
	double		NetValue=48;					// 基金实时参考净值(包括ETF的IOPV)
	uint32		ETFBuyNumber=49;				// ETF申购笔数
	uint32		ETFSellNumber=50;				// ETF赎回笔数
	double		ETFBuyVolume=51;				// ETF申购数量
	double		ETFSellVolume=52;				// ETF赎回数量
	double		ETFBuyAmount=53;				// ETF申购金额
	double		ETFSellAmount=54;				// ETF赎回金额
	double		PreYield=55;					// 指数昨不含加权指数
	double		Yield=56;						// 不含加权的指数
	double		Lead=57;						// 领先指标(指数)
	uint32		IndexUpCount=58;				// 指数成分股上涨数
	uint32		IndexLevelCount=59;			// 指数成分股持平数
	uint32		IndexDownCount=60;				// 指数成分股下跌数
	uint32		WarnCount=61;					// (活跃度)报警次数
	NewMDHKBrokerQueue BuyBrokerQueue = 62;		//港股经纪商队列(买)
	NewMDHKBrokerQueue SellBrokerQueue = 63;	//港股经纪商队列(卖)
	repeated	uint32 BuyBrokerNum = 64;
	repeated	uint32 SellBrokerNum = 65;
}

/**
 * @brief 分价表数据（用分笔/逐笔数据统计）
*/
message PriceVolTable
{
	double	Price=1;		//价格
	double	Volume=2;		//成交量
	double	Outside=3;	//外盘
	double	Inside=4;		//内盘
	uint32	Count=5;		//笔数
}

/**
 * @brief 分钟数据信息
 * 可以与K线共用结构
*/
message MinuteInfo
{
	uint64 jclTime=1;	///>时间
	double preClose=2;	///>昨收价
	double openPrice=3;	///>开盘价
	double highPrice=4;	///>最高价
	double nowPrice=5;	///>现价
	double lowPrice=6;	///>最低价
	double avePrice=7;	///>均价
	double amount=8;		///>成交额
	double volume=9;		///>成交量
	uint32 up=10;	///>上涨加数
	uint32 down=11;	///>下跌家数
	uint32 equ=12;	///>平盘家数
	double lead=13;	///>领先指标
	double buyvol=14;
	double sellvol=15;
	double preVolInStock=16;	///>昨持仓量
	double volInStock=17;	///>持仓量
	double preSettlePrice=18;///>昨结算价 只在日K以上存在
	double settlePrice=19;	///> 结算价 只在日K以上存在
}

message AnalyDataB  // 在.IFZ文件中读入的历史数据记录
{
	uint32			dwItemNum=1;               // 采样点数 : 0 表示这个周期内，没有行情变动
	uint64			jclTime=2;				 // 时间精确到毫秒
											 // 存盘数据是long，保证精度；展示可以用float(如有特殊精度要求，用double)
	double			fOpen=3;	// 单位开盘价
	double			fHigh=4;	// 单位最高价
	double			fLow=5;		// 单位最低价
	double			fClose=6;	// 单位收盘价
	double			fAmount=7;                          // 单位均价(分钟线/期货)			
													  // 单位成交金额(日线/指数)
	double			dVolume=8; // 单位成交量,单位成交金额(指数,100元)
	double			CCL=9;							// 持仓量（订货量)  VolInStock
	double			JSJ=10;		// 昨日结算价
	double			YClose=11;
	// 指数,涨跌家数
	uint32			up=12;                  // 上涨家数
	uint32			down=13;                // 下跌家数
}

/**
 * @brief 全档盘口单条详细数据结构
*/
message PKData
{
	double price=1;		// 价格
	double vol=2;			// 数量
	uint32 numTrades=3;	// 笔数
}

/**
 * @brief 分笔数据结构
*/
message TickInfo
{
	uint64 quoteTime=1;	///>tick 行情时间
	double price=2;		///>价格,如果是集合竞价,则是匹配价格
	double vol=3;			///>成交量,如果是集合竞价,则是匹配量
	double unSuitVol=4;	///>集合竞价未匹配量
	double volDiff=5;		///>期货持仓量增减
	uint32 flag=6;			///>tick标识
	uint64 buy_num=7;	///>买方逐笔委托编号
	uint64 sell_num=8;	///>卖方逐笔委托编号
	uint32 numtrades=9; ///>成交笔数
}
message CDPInfo
{
	double       cdp=1;                   // 昨日中价
	double       nh=2;                    // 卖点
	double       nl=3;                    // 买点
	double       ah=4;                    // 高价突破点
	double       al=5;                    // 低价突破点
	uint32		 dkflag=6;                // 适合作多或作空
	double       tbp=7;                   // 多空平衡点
	double       stoplost=8;              // 止损点
	double       leave=9;                 // 了结点
}

//ERROR_REQ					= 0;		//通用错误应答
message error_ans
{
	uint32 errreqno=1;		//发生错误的请求功能号
	string errinfo=2;		//错误描述
}

// monitorinfo_req 发个pb头即可

//MONITOR_REQ				= 21;		//主站监控信息
message monitorinfo_ans
{
	uint32		ClientNum=1;				//当前在线人数
	uint32		MaxConnectNum=2;			//最大连接人数
	uint32		PackageNum=3;				//当前业务处理数量
	uint32		HasStatus=4;
	uint32		HasLog=5;
	uint32		bHQ=6;
	uint32		bWT=7;
	repeated 	uint32		starttime=8;	// [25];
	string		HostVer=9;		//[30];
	uint32		ProtocolVer=10;
	uint32		TotalClientNum=11;			//当前总在线人数
	uint32		UsedClientNum=12;			//从开启主站到现在有多少人登陆过
	uint32		bAutoBase=13;
	string		HomePath=14;
	string		NetCardStr=15;
	uint32		InfDate=16;
	uint32		InfHms=17;
	uint32		HostType=18;				//服务器类型 独立，子，主
	uint32		ProcType=19;				//0:批作业方式 1:多线程方式
	uint32		CompressType=20;			//压缩方式 0:自动优化 1:完全不压缩 2:最大限度压缩
	uint32		bDayToMem=21;
	uint32		bWeekToMem=22;
	uint32		bMonToMem=23;
	uint32		bMinToMem=24;
	uint32		bKExplain=25;				//是不是成功地启动了选股等服务
	uint32		bAlarm=26;
	uint32		bHqStat=27;

	uint32		ProcessNum=28;				//进程数
	uint32		ThreadNum=29;				//线程数
	
	repeated	uint32		unused2=30;
}

/**
 * @brief 除权除息数据结构（需与客户端统一）
*/
message CqcxCWInfo
{
	tagCodeWithNkey		code=1;		// 唯一ID号(唯一硬编码)
	repeated	CWInfo	cw=2;
}
message CWInfo
{
	uint32		Date=1;		// 日期
	uint32		Type=2;
	double		V01=3;
	double		V02=4;
	double		V03=5;
	double		V04=6;
}

/**
 * @brief 基本财务数据结构（需与客户端统一）
*/
message BaseCWInfo
{
	tagCodeWithNkey		code=1;		
	double		Yield=2;			//保留
	double		ActiveCapital=3;	//流通股本
	double		GrossCapital=4;	//总股本
	uint32		shzt=5;			//上市状态
	uint32		startrq=6;		//上市日期
	uint32		gxrq=7;			//更新日期
	uint32		lastrq=8;			//退市日期
	double		fxj=9;			//发行价
	double		tsspj=10;		//退市收盘价
	double		yycb=11;			//营业总成本
	double		zgb=12;			//总股本
	double		gjg=13;			//国家股
	double		fqrfrg=14;		//发起人法人股
	double		frg=15;			//法人股
	double		bg=16;			//B股
	double		hg=17;			//H股
	double		zgg=18;			//职工股
	double		zzc=19;			//总资产(千元)
	double		ldzc=20;			//流动资产
	double		gdzc=21;			//固定资产
	double		wxzc=22;			//无形资产
	double		cqtz=23;			//长期投资
	double		ldfz=24;			//流动负债
	double		cqfz=25;			//长期负债
	double		zbgjj=26;		//资本公积金
	double		jzc=27;			//股东权益(就是净资产)
	double		zysy=28;			//主营总收入
	double		zyly=29;			//主营利益（实际为营业成本数据）
	double		qtly=30;			//其它利益
	double		yyly=31;			//营业利益
	double		tzsy=32;			//投资收益
	double		btsy=33;			//补贴收入
	double		yywsz=34;		//营业外收支
	double		snsytz=35;		//上年损益调整
	double		lyze=36;			//利益总额
	double		shly=37;			//税后利益
	double		jly=38;			//净利益
	double		wfply=39;		//未分配利益
	double		tzmgjz=40;		//调整每股净资产 物理意义:  净资产/调整后的总股本
	double		HalfYearFlag=41;	//全部更改为以月为单位
	uint32      prov=42;         //所属省份
	uint32      hy=43;           //所属行业
	string		provstr=44;	 //所属省份—新版
	string		hangye=45;	 //所属行业—新版
	double		srkpj=46;		 //首日开盘价		-pt  2017.11.3  14:30
	uint64		start=47;		 //开市时间
}

//AUTOGBBQ_REQ				= 22;		//请求股本除权数据
message autogbbq_req
{
	repeated tagCodeWithNkey	 codes=1;
}
message autogbbq_ans
{
	repeated CqcxCWInfo cqcx=1;
}

//AUTOBASE_REQ				= 23;		//请求财务数据
message autobase_req
{
	repeated tagCodeWithNkey	codes=1;
}


message autobase_ans
{
	repeated BaseCWInfo basep=1;
};

//HOSTMORE_REQ				= 31;		//主站信息，主要包括每个市场的开盘时间，股票数量等
message hostmore_req
{
	int32  setcode=1;		// 市场分开检测
	uint32 verflag=2;
};
message hostmore_info
{
	uint32	errflag=1;	//errflag为0表示可用，其它则表示有错
	int32	setcode=2;
	uint64	epoch_time=3;	//1970以来的时间（纳秒）
	uint32  da_year=4; // Year - 1980
	uint32  da_day=5;  // Day of the month
	uint32  da_mon=6;  // Month (1 = Jan)
	uint32  ti_min=7;  // Minutes
	uint32  ti_hour=8; // Hours
	uint32  ti_hund=9; // Hundredths of seconds
	uint32  ti_sec=10;  // Seconds
	repeated	uint32	common=11;	//市场共性的开盘时间
	repeated	uint32	qt=12;		//其他信息（下标0中存放股票数量）
	uint32	byesterday=13;
	uint64  codelisthash=14;
	uint32  infcodedate=15;
	uint32  infcodehms=16;
	uint32  bserv1=17;
	uint32  bserv2=18;
	uint32  bserv3=19;
	uint32	nUrgentNum=20;
	uint32	linuxcheck=21;
	uint32	bbigjbm=22;
	uint32	b5mmp=23;
	uint32	bcanuserurgent=24;
	uint32	hasBakHostFile=25;
	uint32	webpage=26;
	repeated uint32	hostname=27;
	repeated uint32	byesterdays=28;
	repeated uint32	other=29;
}
message hostmore_ans
{
	repeated	hostmore_info	hosts=1;
}
//PUSH_HQ_SUB				= 134;		//TCP行情推送订阅请求，成功后直接返回1728协议应答
//PUSH_HQ_UNSUB				= 135;		//TCP行情推送取消订阅请求
// 订阅行情请求
message sub_unsub_hq_req	{
	// 订阅几个品种; -1 表示取消全部订阅
	repeated	uint64	aKey = 1;	// 唯一key
}
message unsub_hq_ans
{
	uint32 subnum=1;		//剩余订阅数量
}

message str_unsub_hq_req
{
	repeated string aKey = 1;
}

message stknum_req
{
	int32   setcode=1;
	uint32  rq=2;
}

message stknum_ans
{
	uint32 stknum=1;
}


message StkInfoNew
{
	uint64		nkey=1;		// 品种内部编码,如果提供内部编码，优先识别内部编码，code不用设置；如果不用内部编码，要设置为0
    int32		setcode=2;
	string		Code=3;	//证券代码
	string		Name=4;		//证券名称
	double		Unit=5;				//交易单位
	double		VolBase=6;			//量比的基量
	uint32		precise=7;			//停牌
	uint32		main=8;				//是否主力合约
	double		Close=9;				//昨收
	double		Settle=10;				//昨结
	double		Tick=11;				//最小变动价
	repeated	uint32		nFZ=12;				//开收盘时间段
	uint32		OpenDate=13;			//代码链生成日期
	uint32		BaseFreshCount=14;		//基本资料的更新次新
	uint32		GbbqFreshCount=15;		//股本变迁的更新次新

	string 		EnglishName=16;			//证券英文名称
	string 		EnglishAbbr=17;			//证券英文简称
	repeated uint64			typeOfStock=18;	// 股票分类信息
	
}


message code_req
{
	int32  setcode=1;
	uint32 startxh=2;
}
message code_ans
{
	repeated	StkInfoNew code=1;
}


//ZHSORT_REQ				= 1205;		//综合排名数据
message zhsort_item
{
	tagCodeWithNkey code=1;
	repeated	double val=2;
}
message zhsort_req
{
	uint32 domain=1;
	uint32 num=2;
}
message zhsort_ans
{
	repeated	zhsort_item result=1;		//num*9
}


//FJB_REQ					= 1365;		//分价表
message fjb_req
{
	tagCodeWithNkey code=1;
}
message fjb_ans
{
	CurrStockDataBEx			hq=1;
	repeated	PriceVolTable	fjb=2;			// uint8_t diffdata[0];		//CurrStockDataBEx+PriceVolTable[N]
}

//ANALY_RANGE_REQ			= 1367;		//按时间段请求原始K线数据
message analy_range_req
{
	tagCodeWithNkey code=1;
	uint32 linetype=2;
	uint64 start=3;	// 起始时间
	uint64 end=4;		// 结束时间
}
message analy_range_ans
{
	// 原样返回请求的信息，方便客户端查下
	analy_range_req req=1;

	uint32 status=2;	// -1:表示数据超过800个，需要客户端分批请求（用最后一根k线的年月日作为起始时间，继续请求）  0：正常
	repeated	AnalyDataB	ak=3;		//AnalyData[N]
}
//ANALY_OFFSET_REQ
message analy_offset_req
{
	tagCodeWithNkey	code=1;
	uint32 period=2;
	uint32 offset=3;
	uint32 num=4;
	uint32 mulnum=5;
}
message analy_offset_ans
{
	uint64		nkey=1;						// 品种内部编码
	repeated	AnalyDataB	aK=2;
}
//MINUTE_REQ				= 1369;		//1-10日分时走势数据
message minute_req
{
	tagCodeWithNkey code=1;
	uint32	days=2;
}
message minute_ans
{
	uint32 count=1;
	double close=2;
	CurrStockDataBEx	hq=3;
	repeated MinuteInfo			minute=4;
}

//ANALY_TQ_REQ				= 1370;		//复权K线数据
message analy_tq_req
{
	tagCodeWithNkey	code=1;
	uint32	period=2;
	uint32	offset=3;
	uint32	num=4;
	uint32	mulnum=5;
}
message analy_tq_ans
{
	uint64		nkey=1;						// 品种内部编码
	uint32		num=2;	// 实际第一维度个数
	repeated	AnalyDataB	aK=3;	//AnalyData[N]*3
}

//SORT_HQ_REQ				= 1727;		//请求分类排序行情
//应答采用  multi_hq_ans 
message sort_hq_req
{
	uint32 setDomain=1;
	uint32 coltype=2;
	uint32 startxh=3;
	uint32 wantnum=4;
	uint32 sorttype=5;
}


//MULTI_HQ_REQ				= 1728;		//请求指定若干品种行情
message multi_hq_req
{
	repeated	tagCodeWithNkey		codes=1;
}
message multi_hq_ans
{
	repeated	CurrStockDataBEx ahq=1;
}

//MULTI_EX_HQ_REQ			= 1729;		//请求指定若干品种行情（支持额外扩展字段）
message multi_ex_hq_req
{
	uint32 num=1;
	uint32 fieldnum=2;
	repeated	tagCodeWithNkey codes=3;
}
message multi_ex_hq_ans
{
	uint32 total=1;
	uint32 count=2;
	uint32 ex_len=3;				//行情+扩展字段总长度
	repeated	CurrStockDataBEx data=4;	//CurrStockDataBEx[N];
	//二进制扩展字段数据
	repeated	uint32		exdata=5;
}

//FULL_MMP_REQ				= 1730;		//全档买卖盘口
message full_mmp_req // 全速千档盘口请求
{
	tagCodeWithNkey code=1;
}
message full_mmp_ans // 全速千档盘口应答
{
	uint64 nkey=1;						// 品种内部编码
	uint32 buyNum=2;		// 买盘数量
	uint32 SellNum=3;	// 卖盘数量
	repeated	PKData data=4;	// 先买盘1档 2档 ... N档 再卖盘1档 2档 ... N档
}

//L2_TICK_REQ				= 1732;		//逐笔成交明细
message lv2tick_req // 逐笔成交明细
{
	tagCodeWithNkey code=1;
	uint32 offset=2;// 请求起始偏移位置
	uint32	num=3;
}
message lv2tick_ans
{
	uint64		nkey=1;						// 品种内部编码
	repeated	TickInfo data=2;
}



//SORT_CODE_REQ				= 1734;		//请求全部排序代码
message sort_code_req
{
	uint32 setDomain=1;
	uint32 coltype=2;
	uint32 sorttype=3;
};
message sort_code_ans
{
	repeated	uint64 nkeys=1;
}

//MMP_TICK_REQ				= 1735;		//买卖盘口委托队列
message mmp_tick_req // 买卖队列请求
{
	tagCodeWithNkey code=1;
	double	price=2; // 价格
	uint32	bs=3;
}
message mmp_tick_ans // 买卖队列应答
{
	uint64		nkey=1;						// 品种内部编码
	repeated	double volumes=2;
}

//ALL_MONEYFLOW_REQ			= 1801;		//所有股票实时资金流
//ONE_MONEYFLOW_REQ			= 1802;		//单只股票实时资金流
//HIS_MONEYFLOW_REQ			= 1805;		//单只股票历史资金流 DAY
//ALL_MONEYFLOW_XDAYS_REQ	= 1806;		//所有股票多日累积资金流
//HIS_MONEYFLOW_MIN_REQ		= 1807;		//单只股票历史资金流 MIN
//HIS_MONEYFLOW_TIME_REQ	= 1808;		//根据时间段下载资金流
//ONE_MONEYFLOW_XDAY_REQ	= 1818;		//单只股票多日资金数据请求

//CALC_HISDATA_REQ			= 1901;		//计算的数据请求
//CALC_CURDATA_REQ			= 1902;		//当日不停变换的盘中数据

//FUND_HIS_REQ				= 2001;		//基金的历史净值数据
//FUND_ZHSORT_REQ			= 2006;		//基金综合排名

//BLOCK_SORT_HQ_REQ			= 2008;		//板块成分股排序行情
message block_sort_hq_req
{
	tagCodeWithNkey		code=1;
	uint32		coltype=2;
	uint32		startxh=3;
	uint32		wantnum=4;
	uint32		sorttype=5;
	uint32		fieldnum=6;
	repeated	uint32		fieldid=7;
}
message block_sort_hq_ans
{
	uint32 total=1;
	uint32 count=2;
	uint32 ex_len=3;		//行情+扩展字段总长度
	repeated	CurrStockDataBEx	ahq=4;
	repeated	uint32 exdata=5;	//CurrStockDataBEx[N]+//二进制扩展字段数据
}

//BOND_CODE_REQ				= 2010;		//可转债代码链
//FUND_CODELIST_REQ			= 2011;		//请求分级基金代码信息（带利率规则）

//SORT_EX_HQ_REQ			= 2015;		//请求分类排序行情（支持额外扩展字段）
message sort_ex_hq_req
{
	uint32 setDomain=1;
	uint32 coltype=2;
	uint32 startxh=3;
	uint32 wantnum=4;
	uint32 sorttype=5;
	uint32 drate=6;
	repeated	uint32 fieldids=7;
}
message	hq_with_fields
{
	CurrStockDataBEx	hq=1;
	repeated	google.protobuf.Any			exdata=2;		// 下面是具体的包体
}
message sort_ex_hq_ans
{
	uint32 total=1;
	uint32 count=2;
	repeated	uint32 fieldids=3;	//直接告知客户端，避免重新查询ID
	uint32 ex_len=4;				//行情+扩展字段总长度
	repeated	hq_with_fields fields=5;	
	repeated rank_arr rankinfo = 6;
}

message sort_block_hq_req
{
	tagCodeWithNkey code = 1;
	uint32 coltype=2;
	uint32 startxh=3;
	uint32 wantnum=4;
	uint32 sorttype=5;
	repeated uint32 fieldids=6;
}

message sort_conblock_req
{
	tagCodeWithNkey code = 1;
	uint32 coltype = 2;
	uint32 sorttype = 3;
}
//连板首板断板数据排序
message sort_lbsbdb_hq_req
{
	uint32 type=1;		// 1: 首板  2：连板  3:断板
	uint32 coltype=2;
	uint32 startxh=3;
	uint32 wantnum=4;
	uint32 sorttype=5;
	repeated uint32 fieldids=6;
}
//连板首板断板数据排序
message sort_lbsbdb_hq_ans
{
	uint32 total=1;
	uint32 count=2;
	repeated	uint32 fieldids=3;	//直接告知客户端，避免重新查询ID
	uint32 ex_len=4;				//行情+扩展字段总长度
	repeated	hq_with_fields fields=5;	
}

message stock_block_fields
{
	uint32 blocktype=1;
	CurrStockDataBEx blockhq=2;
	CurrStockDataBEx lzghq=3;
}

message sort_stock_block_ans
{
	repeated stock_block_fields fields = 1;
}

message sort_shq_ex_req
{
	repeated uint64 keys=1;
	repeated uint32 fieldids = 2;
	repeated string szkeys=3;
}

message sort_new_shq_ex_req
{
	uint32 setcode = 1;
	repeated uint64 keys=2;
	repeated uint32 fieldids = 3;
	repeated string szkeys=4;
}

message sort_zdy_req
{
	uint32 setcode = 1;
	uint32 coltype=2;
	uint32 startxh=3;
	uint32 wantnum=4;
	uint32 sorttype=5;
	repeated uint64 keys=6;
	repeated uint32 fieldids = 7;
	repeated string szkeys=8;
}

//FORMULAR_LIST_REQ			= 3030;		//指标列表请求
//FORMULAR_CALC_REQ			= 3031;		//单个指标结果输出（暂时不处理）
//FORMULAR_ONEXG_REQ		= 3032;		//单个指标的选股结果  //一键选股协议
//FORMULAR_ALLXG_REQ		= 3033;		//所有指标的选股结果(新)
//FORMULAR_BKXG_REQ			= 3034;		//单个指标板块日波动结果
//FORMULAR_TPXG_REQ			= 3035;		//指标类型选股结果（for android）
//FORMULAR_ALLXG_HIS_REQ	= 3039;		//所有指标的选股结果(历史)
//FORMULAR_ALLXG_ONE_REQ	= 3040;		//所有指标的选股结果(单个品种)
//FORMULAR_CODELIST_REQ		= 3051;		//指标选股样本空间查询请求
//FORMULAR_CODELIST_MODIFY	= 3052;		//指标选股样本空间修改请求
//FORMULAR_CODELIST_CREATE	= 3053;		//指标选股样本空间分组创建请求
//FORMULAR_CODELIST_DELETE	= 3054;		//指标选股样本空间分组删除请求

//HIS_MINUTE_REQ			= 4068;		//历史分时走势数据
message his_minute_req
{
	uint32	date=1;
	tagCodeWithNkey code=2;
};
message his_minute_ans
{
	uint64		nkey=1;						// 品种内部编码
	double		close=2;
	repeated	MinuteInfo minutes=3;	//MinuteInfo[N]
};

//TICK_REQ					= 4069;		//Tick数据
message tick_req
{
	tagCodeWithNkey code=1;
	uint32	type=2;
	uint32	startxh=3;
	uint32	num=4;
};
message tick_ans
{
	uint64		nkey=1;						// 品种内部编码
	repeated	TickInfo ticks=2;		//TickInfo[N]
}

//HIS_TICK_REQ				= 4070;		//历史Tick数据
message his_tick_req
{
	uint32 ldate=1;
	tagCodeWithNkey code=2;
	uint32 type=3;
	uint32 startxh=4;
	uint32 num=5;
}
message his_tick_ans
{
	uint64		nkey=1;						// 品种内部编码
	double 		close=2;
	repeated	TickInfo	ticks=3;		//TickInfo[N]
}

//CLOUD_CALC_SORT_REQ		= 5013;		//公式云计算排序
message gsResult
{
	string gsName=1;	// 公式名称
	uint32 count=2;			// 公式计算结果的数量
	bool isLast=3;		// 最后公式
}
message cloudCalcResult
{
	tagCodeWithNkey code=1;
	uint64 calcTime=2; // 公式计算的时间
	double Close=3; // 昨收
	double Now=4; // 现价
	double MaxZaf5Days=5; // 5日最大涨幅
}
message cloud_calc_sort_req	// 请求包
{
	uint32 date=1;
}
message cloud_calc_sort_ans // 应答包
{
	uint32 date=1;	// 日期
	repeated	gsResult gsInfo=2; // 4个公式返回的信息，前3个公式为识庄公式，最后一个公式猎庄公式，具体合并方式由客户端处理
	repeated	cloudCalcResult result=3; // 返回结果，四个公式计算的结果依次存入
}

//AUCTIONDATA_REQ			= 10029;	//集合竞价带未匹配量
message auctiondata_req
{
	tagCodeWithNkey code=1;
	uint32 type=2;			//1:开盘集合竞价 4:尾盘集合竞价
}
message auctiondata_ans
{
	uint64		nkey=1;						// 品种内部编码
	repeated	TickInfo ticks=2;
}

//AFTER_TRADE_REQ			= 10032;	//获取盘后定价交易扩展数据
message after_trade_req
{
	tagCodeWithNkey code=1;
}
message after_trade_ans
{
	CurrStockDataBEx data=1;
}

//ZDFSECTION_REQ			= 20001;	//涨跌幅区间 
//ZDFRESULT_REQ				= 20002;	//涨跌幅结果
//ZDFPROFIT_REQ				= 20003;	//涨跌幅曲线
//ZDFZDTNUM_REQ				= 20004;	//涨跌停个数曲线
//FYZBZTRESULT_REQ			= 20005;	//昨日非一字板涨停品种
//ZDTRESULT_REQ				= 20006;	//涨跌停品种
//STRATEGYCENTER_LIST_REQ	= 20100;	//策略中心列表
//STRATEGYCENTER_PROFIT_REQ	= 20101;	//策略中心 收益曲线
//STRATEGYCENTER_SUCCESS_REQ= 20102;	//策略中心 选股成功率
//STRATEGYONEKEY_REQ		= 20104;	//一键选股
//STRATEGYONEKEY_PROFIT_REQ	= 20105;	//一键选股收益曲线
//STRATEGYONEKEY_RESULT_REQ	= 20106;	//一键选股结果
//STRATEGYPOOL_REQ			= 20107;	//股票池
//BIGDATA_CXQN_REQ			= 20108;	//大数据选股-次新擒牛
//BIGDATA_JHJJ_REQ			= 20109;	//大数据选股-集合竞价
//BIGDATA_ZTZY_REQ			= 20110;	//大数据选股-涨停捉妖
//STRATEGYCENTER_JRTC_REQ	= 20111;	//策略中心 今日调仓
//STRATEGYCENTER_DQCC_REQ	= 20112;	//策略中心 当前持仓
//STRATEGYCENTER_CLNG_REQ	= 20113;	//策略中心 策略牛股
//STRATEGYCENTER_SIGNAL_REQ	= 20114;	//历史信号
//STRATEGY_RMQL_DTXG_REQ	= 20115;	//热门强龙策略请求 动态选股策略请求
//STRATEGY_JZTZ_REQ			= 20116;	//价值投资
//LEVEL_REQ					= 20150;	//评级请求
//RPSDATA_REQ				= 20201;	//RPS数据	
//GSKDATA_REQ				= 20202;	//广度深度宽度数据请求
//MONEY_FLOW_REQ			= 20500;	//资金流向

//JSON_REQ					= 30000;	//请求/应答内容为JSon格式的协议报文
message	json_req_ans
{
	string json=1;	//json请求/应答串  一定要0结束符
}

//策略中心 获取列表		
// 共用请求包头
// STRATEGYCENTER_LIST_REQ	
// STRATEGYONEKEY_REQ  
// SRV_STRATEGYPOOL_REQ
// 策略中心共用包头，不同协议，填写不同的包头，不需要全部填满
message StrategyCenterList_Req
{
	string 			typecode=1; // ID   StrategyBS
	string 			groupid=2;
	tagCodeWithNkey	code=3;
	uint32 			date=4; 	// 日期
	uint32			maxnum=5;
	uint32 			offset=6;
	uint32 			num=7;	
	uint32 			period=8; // 0:全部 1:一周 2:一月 3:半年 4:一年
	repeated string	keytypes=9;
}

message StrategyMMOnekey_Req
{
	uint32 date = 1;
	repeated string groupid = 2;
}

message StrategyGS_Result
{
	uint32 date=1;
	repeated double result=2;
}

message StrategyGS_AnsInfo
{
	repeated StrategyGS_Result field = 1;
}

message CalcStatics_Agfd_AnsInfo
{
	repeated uint32 field=1;
	repeated uint32 fieldsh=2;
	repeated uint32 fieldsz=3;
	repeated uint32 fieldcy=4;
}

message StrategyBS_List_AnsInfo
{
	string groupid=1; 
	string name=2;
	double outwin=3; // 止盈
	double outlose=4; // 止损
	double profit=5; // 策略涨幅
	double dayzaf=6; // 当日涨幅
	double success=7; // 胜率
	uint32 date=8; // 日期
	uint32 count=9; // 入选只数
	uint32 setcode=10;	//品种市场
	string code = 11;	//品种代码
	double maxfit = 12;		//历史战绩
}
message StrategyOnekey_Result
{
	string groupid=1;
	string name=2;
	string typecode=3;
	double profit=4; // 日收益
	uint32 up=5;	// 上涨数
	uint32 down=6; // 下跌数
	uint32 total=7; // 总数
	uint32 date=8;	// 日期
}

message StrategyImg
{
	string groupid = 1;
	string img = 2;
}

// StrategyCenterList_Req  选择字段:
//{
//	string groupid=1;
//	uint32 offset=2;
//	uint32 num=3;
//}

message StrategyOnekey_Profit
{
	uint32 date=1;
	double profit=2;
}
// 合并到 StrategyAny_Ans 通用应答包
//message StrategyOnekey_Profit_Ans
//{
//	repeated 	StrategyOnekey_Profit profits=1;
//}
//message StrategyOnekeySelect_Ans
//{
//	repeated 	tagCodeWithNkey codes=1;
//}

//message StrategyCenterList_Ans
//{
//	repeated 	StrategyBS_List_AnsInfo infos=1;
//}
//message StrategyOnekeyResult_Ans
//{
//	repeated 	StrategyOnekey_Result results=1;
//}
//策略中心，收益曲线
// StrategyCenterList_Req  选择字段:
//{
//	string groupid=1;
//	uint32 date=2;
//	uint32 period=3; // 0:全部 1:一周 2:一月 3:半年 4:一年
//}

message StrategyBS_Profit
{
	uint32 date=1;
	double profit=2; // 收益
	double zsprofit=3; // 指数收益
}
// 合并到 StrategyAny_Ans 通用应答包
//message StrategyBS_Profit_Ans
//{
//	repeated	StrategyBS_Profit	profits=1;
//}

//策略中心 策略成功率
// StrategyCenterList_Req  选择字段:
//{
//	string groupid=1;
//}

message	StrategyBS_Result
{
	uint32 avedays=1; // 最高涨幅平均天数
	double profit=2; // 平均收益率
	double sucrate=3; // 选股成功率
}
// 合并到 StrategyAny_Ans 通用应答包
//message StrategyBS_Success_Ans
//{
//	repeated	StrategyBS_Result	results=1;
//	// result[4]; // 0:5日 1:50日 2:90日 3:至今
//}

//今日调仓 
// StrategyCenterList_Req  选择字段:
//{
//	string	groupid=1;
//	uint32  date=2;
//}
// 策略选股今日调仓 20111	// 策略选股当前持仓 20112		// 策略选股策略牛股 20113
// StrategyBS_Jrtc_Data	StrategyBS_Dqcc_Data	StrategyBS_Clng_Data
// 合并数据: 没有的数据不填写
message	StrategyBS_XG_Data
{
	tagCodeWithNkey		code=1;
	uint32 bIn=2;	// true：调入 false: 调出
    uint64 dotime=3; // 调入调出时间	intime 
	double price=4;	// 调入调出价格		inprice
	double maxprice=5; // 止盈价
	double minprice=6; // 止损价
	double tqprice=7; // 调入价格到今天的前复权价格
	uint64 outtime=8; // 调出时间
	double outprice=9;// 调出价格
	double zaf=10;	// 涨幅
	double inzaf=11; // 次日涨幅	
}
// 策略选股当前持仓 20112
//message StrategyBS_Dqcc_Data
//{
//    uint64 dotime=1; // 调入时间
//	double price=2;	// 调入价格
//	double tqprice=3; // 调入价格到今天的前复权价格
//	double maxprice=4; // 止盈价
//	double minprice=5; // 止损价
//}
// 策略选股策略牛股 20113
//message StrategyBS_Clng_Data
//{
 //   uint64 intime=1; // 调入时间
//	double inprice=2;	// 调入价格
//    uint64 outtime=3; // 调出时间
//	double outprice=4;// 调出价格
//	double zaf=5;	// 涨幅
//	double inzaf=6; // 次日涨幅
//}

// 合并应答 StrategyBS_Jrtc_Ans    StrategyBS_Dqcc_Ans StrategyBS_Clng_Ans 
// 合并到 StrategyAny_Ans 通用应答包
//message StrategyCenterList_XG_Ans
//{
//	repeated	StrategyBS_XG_Data results=1;
//}

//当前持仓
// StrategyCenterList_Req  选择字段:{
//	string groupid=1;
//}

//message StrategyBS_Dqcc_Ans 
//{
//	tagCodeWithNkey		code=1;
//	StrategyBS_Dqcc_Data ansData=2;
//}

//message StrategyCenterList_DQCC_Ans
//{
//	repeated	StrategyBS_Dqcc_Ans results=1;
//}

//当前持仓
// StrategyCenterList_Req  选择字段://{
//	string groupid=1;
//	uint32 maxnum=2; // 返回的最大条数
//}

//message StrategyBS_Clng_Ans	
//{
//	tagCodeWithNkey		code=1;
//	StrategyBS_Clng_Data ansData=2;
//}

//message StrategyCenterList_CLNG_Ans
//{
//	repeated	StrategyBS_Clng_Ans	results=1;
//}


// pool  次新擒牛，集合竞价 涨停强度 
message StrategyPoolCxqnJhjjZtqd_List 
{
	tagCodeWithNkey	code=1;
	uint64 selecttime=2;	// 入选时间
	double selectprice=3;	// 入选价格
	double selectzaf=4;		// 入选涨幅
	double fivedayszaf=5;	//入选后5日涨幅
	double 	fState=6;
	uint32 	days=7;
	double 	fex=8;
}

message StrategyPoolCxqnJhjjZtqd_Result
{
	string 	groupid=1;
	string	typecode=2;
	string 	name=3;
	repeated	StrategyPoolCxqnJhjjZtqd_List	poollists=4;
}


// 合并到 StrategyAny_Ans 通用应答包
//message StrategyPool_Result_Ans
//{
//	repeated	StrategyPoolCxqnJhjjZtqd_Result		results=1;
//}
//message StrategyCxqnJhjjZtqd_Result_Ans
//{
//	repeated	StrategyPoolCxqnJhjjZtqd_List	poollists=1;
//}

// 个股 策略历史信号 20114
message Strategy_HisSignal_Data
{
	uint32 	date=1;
	uint32	hms=2;
	uint32 	bs=3;
}

message Strategy_HisSignal_Ans
{
	tagCodeWithNkey	code=1;
	repeated	Strategy_HisSignal_Data signals=2;
}

// 股票池交集请求 20115
message StrategyMixPool_Req
{
	uint32 	date=1;
	string	typecode=2;
	repeated string keytypes=3;
}
// 合并 stock_select  pool_select  poolsh50_select   onekey_select bigdata_select  t0_select
message	StrategyMixPool_Data
{
	tagCodeWithNkey	code=1;
	string		groupid=2;
	uint64		selecttime=3;
	double 		selectprice=4;
	double 		zaf_select=5;	// 入选涨幅
	double 		zaf_5days=6;	// 入选后5日最大涨幅
	double 		zaf_open=7; // 相对于开盘涨幅
	double 		zaf=8;	// 当日涨幅
	uint32 		days=9;	// 连涨天数
	double 		exdata=10;	// 累计涨幅/打开累计跌幅/开市价格
	uint32 		date=11;
	repeated	double 	dparam=12;
}
// 合并到 StrategyAny_Ans 通用应答包
//message	StrategyMixPool_Ans
//{
//	repeated	StrategyMixPool_Data	data=1;
//}
message StrategyAny_Ans
{
	repeated	google.protobuf.Any			cldata=1;		// 下面是具体的包体
}

// 导入策略列表 20130
// 导入策略收益曲线 20131
// 导入策略持仓股票 20132
// 导入策略实时调仓指令 20133
// 导入策略评分 20134
// 导入策略交易统计 20135
// message StrategyCenterList_Req

message Strategy_SGZH
{
	string type = 1;
	string name = 2;
	string groupid = 3;
	string gssrc = 4;
	uint32 time = 5;
}

message JobImport_Data
{
	string groupid=1;
	string name=2;
}
// 合并到 StrategyAny_Ans 通用应答包
//message JobImport_Data_Ans
//{
//	repeated	JobImport_Data	data=1;
//}

message IncomeLine // 收益曲线
{
	uint32 date=1;	// 日期
	string state=2; // 择时
	double zstotalrate=3; // 沪深300累计收益率
	double totalrate=4; // 策略累计收益率
	double zsdayrate=5;	// 沪深300每日收益率
	double dayrate=6; // 策略每日收益率
}
message IncomeStatistics // 收益统计
{
	uint32 type=1; // 2本策略，3沪深300，4相对收益
	double total=2; // 总收益
	double year=3;  // 年化收益
	double sharprate=4; // 夏普比率
	double backset=5;  // 最大回撤率
	double baudrate=6; // 收益波动率
	double inforate=7; // 信息比率
	double beta=8; 
	double alpha=9;
}
// 慧眼评级 20150
//struct Hypj_Zhpf
//{
//	UINT64 nkey;
//	double score;
//};
//盈利能力
//struct Earn_Ans
//{
//	UINT64 nkey;
//	double value;
//};
// 个股评级，盈利等计算结果
message	Stock_Calc_Info
{
	uint64 nkey = 1;	
	double score=2;	// 评级
	double earnvalue=3;	// 盈利能力
	uint32 rank=4;
}
// 导入策略注释说明
message StrategyRemark // 策略注释说明
{
	string author=1;	//[VAR_NAME_LEN];	// 作者
	uint32 createdate=2;	// 创建日期
	uint32 nextdate=3; // 下个调仓日期
	uint32 backdates=4; // 回撤日期开始日
	uint32 backdatee=5; // 回测日期结束日
	string potimer=6; // 调仓时点
	uint32 realtime=7; // 是否实时策略
	uint32 period=8; // 调仓周期
	string model=9;	// [32]; // 交易模型
	uint32 stocknum=10; // 持仓股票
	uint32 chose=11; // 大盘择时
	uint32 staticpool=12; // 静态股票池
	string base=13;		// [VAR_NAME_LEN]; // 收益基准
	string fee=14;		// [VAR_NAME_LEN]; // 交易费用
	string remark=15;		//[256]; // 策略描述
}
message StrategyScore // 策略评分
{
	double earn=1; // 收益
	double kfx=2; // 抗风险
	double ldx=3; // 流动性
	double wdx=4; // 稳定性
	double sp=5; // 实盘
	double score=6; // 总评分
}
// HisTrader   PositionStock 共用
// 历史交易记录
// 持仓股票
message StockTradeInfo // 历史交易记录
{
	tagCodeWithNkey	code=1;
	string name=2; // 股票名称
	string intrutype=3; // 行业分类
	string intrusecond=4; // 二级行业
	uint32 buydate=5; // 买入日期
	uint32 selldate=6; // 卖出日期
	double buyprice=7; // 买入价格(前复权)
	double sellprice=8; // 卖出价格(前复权)
	double zaf=9; // 涨幅
	double closeprice=10; // 最近收盘价
	double position=11; // 当前仓位
}

message ChangePosition // 实时调仓指令
{
	tagCodeWithNkey	code=1;
	string name=2; // 股票名称
	string intrutype=3; // 行业分类
	string intrusecond=4; // 二级行业
	uint32 signal=5; // 信号 正数：买入股数 如 24700 代表买入24700股；负数：卖出股数 如 -5400 代表卖出5400股；0 : 表示全部卖出
	double position=6; // 目标仓位
	double price=7; // 参考价
}

message TransStatistics // 交易统计
{
	double year=1; // 年换手率%
	double avehold=2; // 平均持仓股票数
	double aveday=3; // 平均持有天数
	double aveincome=4; // 平均交易收益%
	double avepoincome=5; // 正收益平均%
	double aveneincome=6; // 负收益平均%
	double winrate=7;	// 交易赢率%
	double closerate=8; // 持仓停牌股票比例%
	double dorate=9; // 调仓指令可执行比例%
	uint32 times=10; // 换股次数
	double yearincome=11; // 去极值年化收益%
	double month=12;	// 月赢率%
	double zsdiss=13; // 指数跟踪误差%
}

message KCBDATA{
  uint64 nkey = 1;	
  int64 jclTime = 2; // 时间
  int32 status = 3;  // 状态
  int32 tickcount = 4; // 成交笔数
  double fPrice = 5; // 今收盘价
  double fAmt = 6; // 总成交额
  double fVol = 7; // 总成交量
  double fNowAmt = 8; // 当前成交额
  double fNowVol = 9; // 当前成交量
  double fBuyvol = 10; // 买量
  double fSellvol = 11; // 卖量
}

message KCBDataEx{
  KCBDATA p_kcbdate = 1;
  int32 bsDirect = 2;	// 买卖方向 0:买 1:卖 -1:无
  double dVolume = 3;	// 申买(卖)量
  int32	orderNumer = 4;	// 实际委托笔数
  int32 orderNo = 5;	// 公布的委托笔数
  repeated double orderVol = 6; // 委托队列
}

message KCBDataAns
{
	repeated KCBDataEx field = 1;
}

message AuctionDataB{
  int64			jclTime = 1;			// 时间精确到毫秒
  double			fNow = 2;                   // 现价*1000
  double			fNowVol = 3;                // 现手
}

message AuctionDataBEx{
  AuctionDataB p_auctiondatab = 1;
  double	fUnsuitVol = 2;	// 未匹配量
  int32		nUnsuitBS = 3;	// 未匹配方向 0:卖 1:买 2:未匹配量为0，没有方向
}


//COMBAVERAGE_NREQ				= 1359;	// 请求Average.csv
//COMBBLOCK_NREQ					= 1360; // 请求block.xml
message		Binary_Data_Ans
{
	bytes	data=1;
}

message moneyflow_req
{
	tagCodeWithNkey code = 1;
	uint32 offset = 2;
	uint32 num = 3;
}

message moneyflow_data
{
	int64 jcltime = 1;
	double superIn = 2;
	double superOut = 3;
	double bigIn = 4;
	double bigOut = 5;
	double midIn = 6;
	double midOut = 7;
	double smallIn = 8;
	double smallOut = 9;
}

message moneyflow_ans
{
	repeated moneyflow_data amt = 1;
	repeated moneyflow_data vol = 2;
	repeated moneyflow_data cnt = 3;
}

message dxjl_data
{
	tagCodeWithNkey code = 1;
	uint32 type = 2;
	string name = 3;
	int64 jcltime = 4;
	double price = 5;
	double vol = 6;
	double zaf = 7;
	double rate = 8;
	double buyprice = 9;
	double buyvol = 10;
	double sellprice = 11;
	double sellvol = 12;
}

message dxjl_push
{
	repeated dxjl_data data = 1;
}

message Dxjl_Req
{
	uint32 offset = 1;
	uint32 num = 2;
	uint32 type = 3;
}

message RSPDATA
{
	uint32 type = 1;
	tagCodeWithNkey code = 2;
	int64 jcltime = 3;
	repeated double date = 4;
}

message Dxjl_ans
{
	repeated RSPDATA field = 1;
}

message ztfx_data
{
	uint64 jcltime = 1;
	double rate = 2;
	uint32 first = 3;
	uint32 second = 4; 
}

message zafx_ans
{
	repeated ztfx_data field = 1;
}

message bkyd_top_data
{
	uint64 nkey = 1;
	double ratio = 2;
}

message bkyd_single_data
{
	uint64 nkey = 1;
	double ratio = 2;	// ratio为正则涨，为负则跌
	uint32 index = 3;
	string time = 4;
	double business = 5;		// 成交额
	repeated bkyd_top_data tops = 6;
}

// 板块异动响应
message bkyd_all_ans
{
	repeated bkyd_single_data field = 1;
}
// 板块异动请求
message bkyd_date_req
{
	int32 date = 1;
}

message bkfb_stock
{
	uint64 nkey = 1;	
	double ratio = 2;
}

message bkfb_area
{
	int32 count = 1;		// 板块下个股数量
	repeated bkfb_stock stock = 2;
}
// 板块分布响应
message bkfb_all_ans
{
	uint32 areaCount = 1;
	repeated bkfb_area data = 2;
}
// 板块分布请求
message bkfb_nkey_req
{
	uint64 nkey = 1;
}
// 上涨下跌数请求
message zd_number_ans
{
	uint32 nUp = 1; //上涨家数
	uint32 nFlat= 2; //平盘家数
	uint32 nDown = 3; //下跌家数
}

message finance_down_req
{
	tagCodeWithNkey code = 1;
	uint32 sdate = 2;
	uint32 edate = 3;
}

message finance_down_data
{
	int64 nkey = 1;
	int64 Date = 2;
	double      CurrentTotalCapital = 3;   
	double      CurrentActiveCapital = 4;  
	double      TotalCapital = 5;          
	double      Market = 6;                
	double      StockType = 7;             
	double      IndustryCode = 8;          
	double      B_Stock = 9;               
	double      H_Stock = 10;               
	double      ActiveCapital = 11;         
	double      ShareHolders = 12;          
	double      AssetLiabilityRatio = 13;   
	double      TotalAsset = 14;            
	double      CurrentAsset = 15;          
	double      FixedAsset = 16;            
	double      IntangibleAsset = 17;       
	double      CurrentLiability = 18;      
	double      MinoritySHInterest = 19;    
	double      CapitalFund = 20;           
	double      PerShareFund = 21;          
	double      SHInterest = 22;            
	double      BusinessIncome = 23;        
	double      BusinessCost = 24;          
	double      Receivables = 25;           
	double      BusinessProfit = 26;        
	double      InvestmentIncome = 27;      
	double      BusinessCashFlow = 28;      
	double      TotalCashFlow = 29;         
	double      Inventory = 30;             
	double      TotalProfit = 31;           
	double      AfterTaxProfit = 32;        
	double      NetProfit = 33;             
	double      UndistributedProfit = 34;   
	double      PerShareProfit_wfp = 35;    
	double      PerShareProfit_qnzs = 36;   
	double      PerShareNetAsset = 37;      
	double      SesonallyNetAsset = 38;     
	double      SHInterestRatio = 39;       
	double      QuarterlyReportNum = 40;    
	double      PerSharEarningEnterpris = 41;
	double      TimeToMarket = 42;          
	double      ProfitRatio = 43;           
	double      IncomeRatio = 44;           
	double      DividendYieldRatio = 45;    
	double      FreeCurrentCapital = 46;    
	double      TodayQXMark = 47;           
	double      BelongToHS300 = 48;         
	double      ContainsKZZ = 49;           
	double      IsRZRQBD = 50;              
	double      PerShareDividend = 51;      
	double      KFNetProfit = 52;           
	double      DevelopCost = 53;           
	double      EmployeeNum = 54;           
	double      CurrencyCapital = 55;       
	double      Receivables_InAdvance = 56; 
	double      ExerciseRatio = 57;         
	double      ExercisePrice = 58;         
	double      LeverRatio = 59;            
	double      IntrinsicValue = 60;        
	double      PremiumRate = 61;           
	double      TimeValue = 62;             
	double      DaysLeft = 63;              
	double      ContractMultiplier = 64;    
	double      Delta = 65;                 
	double      Subscribe = 66; 
	double		Finance_65 = 67;
	double		Finance_66 = 68;
	double		Finance_67 = 69;
	double		Finance_68 = 70;
	double		Finance_69 = 71;
	double		Finance_70 = 72;
	double		Finance_71 = 73;
	double		Finance_72 = 74;
	double		Finance_73 = 75;
	double		Finance_74 = 76;
	double		Finance_75 = 77;
	double		Finance_76 = 78;
	double		Finance_77 = 79;
	double		Finance_78 = 80;
	double		Finance_79 = 81;
	double		Finance_80 = 82;
	double		Finance_81 = 83;
	double		Finance_82 = 84;
	double		Finance_83 = 85;
	double		Finance_84 = 86;
	double		Finance_85 = 87;
	double		Finance_86 = 88;
	double		Finance_87 = 89;
	double		Finance_88 = 90;
	double		Finance_89 = 91;
	double		Finance_90 = 92;
	double		Finance_91 = 93;
	double		Finance_92 = 94;
	double		Finance_93 = 95;
	double		Finance_94 = 96;
	double		Finance_95 = 97;
	double		Finance_96 = 98;
	double		Finance_97 = 99;
	double		Finance_98 = 100;
	double		Finance_99 = 101;
	double		Finance_100 = 102;
}

message finvalue_down_data
{
	int64     nkey=1;                           //股票nkey
    int64     Date=2;                           //日期

    double      ReportTime=3;                     //返回报告期(YYMMDD格式),150930表示为2015年第三季

    //每股指标
    double      PerShareProfitBasic=4;                    //基本每股收益
    double      PerShareProfit_kcfjcxsy=5;                //扣除非经常性损益每股收益
    double      PerShareProfit_wfp=6;                     //每股未分配利润
    double      PerShareNetAsset=7;                       //每股净资产
    double      PerShareCapitalFund=8;                    //每股资本公积金
    double      NetAssetYieldRate=9;                      //净资产收益率
    double      PerShareBusinessCashFlow=10;               //每股经营现金流量

    //资产负债表
    double      CurrencyCapital=11;                        //货币资金
    double      TradableFinancialAsset=12;                 //交易性金融资产
    double      ReceivablesBill=13;                        //应收票据
    double      Receivables=14;                            //应收账款
    double      PrePayment=15;                             //预付款项
    double      OtherReceivables=16;                       //其他应收款
    double      RelatedCompanyReceivables=17;              //应收关联公司款
    double      InterestReceivables=18;                    //应收利息
    double      DividendReceivables=19;                    //应收股利
    double      Inventory=20;                              //存货
    double      Include_BiologicalAsset_xhx=21;            //其中:消耗性生物资产
    double      OtherCurrentAsset=22;                      //其他流动资产
    double      TotalCurrentAsset=23;                      //流动资产合计
    double      SalableFinancialAsset=24;                  //可供出售金融资产
    double      OnTimeInvestment=25;                       //持有至到期投资
    double      LongTermReceivables=26;                    //长期应收款
    double      LongTermEquityInvestment=27;               //长期股权投资
    double      RealEstateInvestment=28;                   //投资性房地产
    double      FixedAsset=29;                             //固定资产
    double      ConstructionInProcess=30;                  //在建工程
    double      EngineeringMaterial=31;                    //工程物资
    double      FixedAssetClear=32;                        //固定资产清理
    double      BiologicalAsset_scx=33;                    //生产性生物资产
    double      GasAsset=34;                               //油气资产
    double      IntangibleAsset=35;                        //无形资产
    double      DevelopmentExpenditure=36;                 //开发支出
    double      BusinessReputation=37;                     //商誉
    double      LongTermUnamortizedExpense=38;             //长期待摊费用
    double      DeferredIncomeTaxAsset=39;                 //递延所得税资产
    double      OtherNonCurrentAsset=40;                   //其他非流动资产
    double      TotalIlliquidAsset=41;                     //非流动资产合计
    double      TotalAsset=42;                             //资产总计
    double      ShortTermBorrow=43;                        //短期借款
    double      TradableFinancialLiability=44;             //交易性金融负债
    double      PayableBill=45;                            //应付票据
    double      Payables=46;                               //应付账款
    double      AdvancedReceived=47;                       //预收款项
    double      PayrollPayable=48;                         //应付职工薪酬
    double      TaxPayable=49;                             //应交税费
    double      InterestPayable=50;                        //应付利息
    double      DividendPayable=51;                        //应付股利
    double      OtherPayable=52;                           //其他应付款
    double      RelatedCompanyPayable=53;                  //应付关联公司款
    double      NonCurrentLiabilityInOneYear=54;           //一年内到期的非流动负债
    double      OtherCurrentLiability=55;                  //其他流动负债
    double      TotalCurrentLiability=56;                  //流动负债合计
    double      LongTermBorrow=57;                         //长期借款
    double      PayablesLoan=58;                           //应付债款
    double      LongTermPayables=59;                       //长期应付款
    double      SpecialPayables=60;                        //专项应付款
    double      AnticipationLiability=61;                  //预计负债
    double      DeferredIncomTaxLiability=62;              //递延所得税负债
    double      OtherNonCurrentLiability=63;               //其他非流动负债
    double      TotalNonCurrentLiability=64;               //非流动负债合计
    double      TotalLiability=65;                         //负债合计
    double      PaidUpCapital=66;                          //实收资本(或股本)
    double      CapitalReserve=67;                         //资本公积
    double      SurplusReserve=68;                         //盈余公积
    double      Sub_InventoryShare=69;                     //减:库存股
    double      UndistributedProfit=70;                    //未分配利润
    double      MinoritySHInterest=71;                     //少数股东权益
    double      ForeignCurrency_zsjc=72;                   //外币报表折算价差
    double      AbnormalBusinessEarning=73;                //非正常经营项目收益调整
    double      OwnerTotalRights=74;                       //所有者权益(或股东权益)合计
    double      OwnerTotalLiability=75;                    //负债和所有者(或股东权益)合计

    //利润表
    double      Include_BusinessIncome=76;                 //其中:营业收入
    double      Include_BusinessCost=77;                   //其中:营业成本
    double      BusinessTaxPlus=78;                        //营业税金及附加
    double      SellingExpense=79;                         //销售费用
    double      ManageExpense=80;                          //管理费用
    double      ExplorationExpense=81;                     //勘探费用
    double      FinancialExpense=82;                       //财务费用
    double      AssetsLoss=83;                             //资产减值损失
    double      Add_NetProfit_gyjzbd=84;                   //加:公允价值变动净收益
    double      InvestmentIncome=85;                       //投资收益
    double      Include_InvestmentProfit=86;               //其中:对联营企业和合营企业的投资收益
    double      BusinessProfit_qtkm=87;                    //影响营业利润的其他科目
    double      Third_BusinessProfit=88;                   //三、营业利润
    double      Add_SubsidyIncome=89;                      //加:补贴收入
    double      NonbusinessIncome=90;                      //营业外收入
    double      Sub_NonbusinessExpenditure=91;             //减:营业外支出
    double      Include_NonCurrentAssetNetLoss=92;         //其中:非流动资产处置净损失
    double      Add_TotalProfit_qtkm=93;                   //加:影响利润总额的其他科目
    double      Fourth_TotalProfit=94;                     //四、利润总额
    double      Sub_IncomeTax=95;                          //减:所得税
    double      Add_NetProfit_qtkm=96;                     //加:影响净利润的其他科目
    double      Fifth_NetProfit=97;                        //五、净利润
    double      NetProfit_mgssyz=98;                       //归属于母公司所有者的净利润
    double      MinorityInterestsLoss=99;                  //少数股东损益

    //现金流量表
    double      Cash_xxsp_tglw=100;                         //销售商品、提供劳务收到的现金
    double      sffh=101;                                   //收到的税费返还
    double      CashGet_Other_jjhd=102;                     //收到其他与经营活动相关的现金
    double      CashFlowInSubtotal_jjhd=103;                //经营活动现金流入小计
    double      CashPaid_gmsh_jslw=104;                     //购买商品、接受劳务支付的现金
    double      CashPaid_zg=105;                            //支付给职工以及未职工支付的现金
    double      TaxPaid_=106;                               //支付的各项税费
    double      CashPaid_jjhd=107;                          //支付的其他与经营活动有关的现金
    double      CashFlowOutSubtotal_jjhd=108;               //经营活动现金流出小计
    double      CashNetAmount_jjhd=109;                     //经营活动产生的现金流量净额
    double      CashGet_Investment=110;                     //收回投资收到的现金
    double      CashGet_InvestmentProfit=111;               //取得投资收益收到的现金
    double      CashNetAmountGet_zc=112;                    //处置固定资产、无形资产和其他长期资产收回的现金净额
    double      CashNetAmountGet_zgs_yydw_cz=113;           //处置子公司及其他营业单位收到的现金净额
    double      CashGet_OtherInvestment=114;                //收到其他与投资活动相关的现金
    double      CashFlowInSubtotal_Investment=115;          //投资活动现金流入小计
    double      CashPaid_zc=116;                            //购建固定资产、无形资产和其他长期资产支付的现金   
    double      CashPaid_Investment=117;                    //投资支付的现金
    double      CashNetAmountGet_zgs_yydw=118;              //取得子公司及其他营业单位支付的现金净额
    double      CashPaid_OtherInvestment=119;               //支付其他与投资活动有关的现金
    double      CashFlowOutSubtotal_Investment=120;         //投资活动现金流出小计
    double      CashNetAmount_Investment=121;               //投资活动产生的现金流量净额
    double      CashAbsorb_Investment=122;                  //吸收投资收到的现金
    double      CashGet_Borrow=123;                         //取得借款收到的现金
    double      CashGet_Other_czhd=124;                     //收到其他与筹资活动有关的现金
    double      CashFlowInSubtotal_czhd=125;                //筹资活动现金流入小计    
    double      CashPaid_Debt=126;                          //偿还债务支付的现金
    double      CashPaid_fpgl_lr_cflx=127;                  //分配股利、利润或偿付利息支付的现金
    double      CashPaid_Other_czhd=128;                    //支付其他与筹资活动相关的现金
    double      CashFlowOutSubtotal_czhd=129;               //筹资活动现金流出小计
    double      CashFlowNetAmount_czhd=130;                 //筹资活动产生的现金流量净额
    double      Fourth_CashInfluence_hlbd=131;              //四、汇率变动对现金的影响
    double      Fourth_2_CashInfluence_Other=132;           //四(2)、其他原因对现金的影响
    double      Fifth_CashNetAmount_Increase=133;           //五、现金及现金等价物净额增加
    double      CashBalance_Equav_Begin=134;                //期初现金及现金等价物余额
    double      CashBalance_Equav_End=135;                  //期末现金及现金等价物余额
    double      NetProfit=136;                              //净利润
    double      Add_zcjzzb=137;                             //加:资产减值准备
    double      Depreciation_gdzc_yqzc_scxswzc=138;         //固定资产折旧、油气资产折旧、生产性生物资产折旧
    double      Amortization_wxzc=139;                      //无形资产摊销
    double      Amortization_cqdtfy=140;                    //长期待摊费用摊销
    double      Loss_gdzc_wxzc_cqzc=141;                    //处置固定资产、无形资产和其他长期资产的损失
    double      Loss_gdzcbf=142;                            //固定资产报废损失
    double      Loss_gyjzbd=143;                            //公允价值标动损失
    double      FinancialCost=144;                          //财务费用
    double      Loss_Investment=145;                        //投资损失
    double      Decrease_DeferredIncomTaxAsset=146;         //递延所得税资产减少
    double      Increase_DeferredIncomeTaxDebt=147;         //递延所得税负债增加
    double      Decrease_Inventory=148;                     //存货的减少
    double      Decrease_jyxysxm=149;                       //经营性应收项目的减少
    double      Increase_jyxyfxm=150;                       //经营性应付项目的增加
    double      Other=151;                                  //其他
    double      CashFlowNetAmount_jyhd=152;                 //经营活动产生的现金流量净额2
    double      DebtConvertToAsset=153;                     //债务转为资本
    double      ConvertibleBonds_ynndq=154;                 //一年内到期的可转换公司债券
    double      FixedAsset_rzzr=155;                        //融资租入固定资产
    double      CashBalance_End=156;                        //现金的期末余额
    double      Sub_CashBalance_End=157;                    //减:现金的期末余额
    double      Add_xjdjw_End=158;                          //加:现金等价物的期末余额
    double      Sub_xjdjw_Begin=159;                        //减:现金等价物的期初余额
    double      CashIncrease_NetAmount=160;                 //现金及现金等价物净增加额

    //偿债能力分析
    double      Ratio_Current=161;                          //流动比率(非金融类指标)
    double      Ration_Quick=162;                           //速动比率(非金融类指标)
    double      Ratio_Cash=163;                             //现金比率(%)(非金融类指标)
    double      Ratio_InterestCoverage=164;                 //利息保障倍数(非金融类指标)
    double      Ratio_NonCurrentDebt=165;                   //非流动负债比率(%)(非金融类指标)
    double      Ratio_CurrentDebt=166;                      //流动负债比率(%)(非金融类指标)
    double      Ratio_Debt_xjdq=167;                        //现金到期债务比率(%)(非金融类指标)
    double      Ratio_Debt_yxzcjz=168;                      //有形资产净值债务率(%)
    double      EquityMultiplier=169;                       //权益乘数(%)
    double      gdqy_TotalDebt=170;                         //股东的权益/负债合计(%)
    double      yxzc_TotalDebt=171;                         //有形资产/负债合计(%)
    double      CashFlowNetAmount_TotalDebt_jyhd=172;       //经营活动产生的现金流量净额/负债合计(%)(非金融类指标)
    double      EBITDA_TotalDebt=173;                       //EBITDA/负债合计(%)(非金融类指标)

    //经营效率分析
    double      TurnoverRate_yszk=174;                      //应收账款周转率(非金融类指标)
    double      TurnoverRate_Inventory=175;                 //存货周转率(非金融类指标)
    double      TurnoverRate_yyzj=176;                      //运营资金周转率(非金融类指标)
    double      TurnoverRate_TotalAsset=177;                //总资产周转率(非金融类指标)
    double      TurnoverRate_gdzc=178;                      //固定资产周转率(非金融类指标)
    double      TurnoverDays_yszk=179;                      //应收账款周转天数(非金融类指标)
    double      TurnoverDays_Inventory=180;                 //存货周转天数(非金融类指标)
    double      TurnoverRate_ldzc=181;                      //流动资产周转率(非金融类指标)
    double      TurnoverDays_ldzc=182;                      //流动资产周转天数(非金融类指标)
    double      TurnoverDays_TotalAssets=183;               //总资产周转天数(非金融类指标)
    double      TurnoverRate_ShareholdersEquity=184;        //股东权益周转率(非金融类指标)

    //发展能力分析
    double      GrowthRate_Operatingincome=185;             //营业收入增长率(%)
    double      GrowthRate_NetProfit=186;                   //净利润增长率(%)
    double      GrowthRate_NetaAssets=187;                  //净资产增长率(%)
    double      GrowthRate_FixedAssets=188;                 //固定资产增长率(%)
    double      GrowthRate_TotalAssets=189;                 //总资产增长率(%)
    double      GrowthRate_InvestmentIncome=190;            //投资收益增长率(%)
    double      GrowthRate_OperatingProfit=191;             //营业利润增长率(%)
    double      kfmgsytb=192;                               //扣非每股收益同比(%)
    double      kfjlrtb=193;                                //扣费净利润同比(%)
    double      None=194;                                   //暂无

    //获利能力分析
    double      ProfitRate_CostCosts=195;                   //成本费用利润率(%)
    double      ProfitRate_Business=196;                    //营业利润率(非金融类指标)
    double      TaxRate_Business=197;                       //营业税金率(非金融类指标)
    double      CostRate_Business=198;                      //营业成本率(非金融类指标)
    double      YieldRate_NetAssets=199;                    //净资产收益率
    double      YieldRat_Investment=200;                    //投资收益率
    double      NetInterestRate_Sales=201;                  //销售净利率(%)
    double      PayRate_TotalAssets=202;                    //总资产报酬率
    double      NetProfitRate=203;                          //净利润率(非金融类指标)
    double      GrossProfitRate_Sales=204;                  //销售毛利率(%)(非金融类指标)
    double      sfbz=205;                                   //三费比重(非金融类指标)
    double      CostRate_Management=206;                    //管理费用率(非金融类指标)
    double      CostRate_Finance=207;                       //财务费用率(非金融类指标)
    double      NetProfit_kcfjcxsy=208;                     //扣除非经常性损益后的净利润
    double      EBIT=209;                                   //息税前利润(EBIT)
    double      EBITDA=210;                                 //息税折旧摊销前利润(EBITDA)
    double      EBITDA_TotalIncome=211;                     //EBITDA/营业总收入(%)(非金融类指标)

    //资本结构分析
    double      DebtRatio_Assets=212;                       //资产负债率(%)
    double      Ratio_CurrentAssets=213;                    //流动资产比率(非金融类指标)
    double      Ratio_MonetaryFunds=214;                    //货币资金比率(非金融类指标)
    double      Ratio_Inventory=215;                        //存货比率(非金融类指标)
    double      Ratio_FixedAssets=216;                      //固定资产比率
    double      Ratio_fzjg=217;                             //负债结构比(非金融类指标)
    double      gsymgsgdqy_qbtrzb=218;                      //归属于母公司股东权益/全部投入资本(%)
    double      gdqy_dxzw=219;                              //股东权益/带息债务(%)
    double      TangibleAssets_NetDebt=220;                 //有形资产/净债务(%)

    //现金流量分析
    double      CashFlow_mgjyx=221;                         //每股经营性现金流(元)
    double      CashContent_yysr=222;                       //营业收入现金含量(%)(非金融类指标)
    double      NetCashFlow_NetIncome_jjhd=223;             //经营活动产生的现金流量净额/经营活动净收益(%)
    double      Cash_yysr_xssptglw=224;                     //销售商品提供劳务收到的现金/营业收入(%)
    double      NetCashFlow_yysr_jjhd=225;                  //经营活动产生的现金流量净额/营业收入
    double      zbcc_zktx=226;                              //资本支出/折扣和摊销
    double      CashFlowPerShareNetAmount=227;              //每股现金流量净额(元)
    double      Ratio_NetOperatingCashShort=228;            //经营净现金比率(短期债务)(非金融类指标)
    double      Ratio_NetOperatingCashFull=229;             //经营净现金比率(全部债务)
    double      NetCashFlow_NetProfitRatio_jjhd=230;        //经营活动现金净流量与净利润比率
    double      AllAssetCashRecoveryRate=231;               //全部资产现金回收率

    //单季度财务指标
    double      OperatingIncome=232;                        //营业收入
    double      OperatingProfit=233;                        //营业利润
    double      NetProfit_gsymgssyz=234;                    //归属于母公司所有者的净利润
    double      NetProfit_kcfjjxsy=235;                     //扣除非经常性损益后的净利润
    double      NetCashFlow_jyhd=236;                       //经营活动产生的现金流量净额
    double      NetCashFlow_tzhd=237;                       //投资活动产生的现金流量净额
    double      NetCashFlow_czhd=238;                       //筹资活动产生的现金流量净额
    double      NetIncreaseAmount_Cash_xjdjw=239;           //现金及现金等价物净增加额

    //股本股东
    double      TotalShareCapital=240;                      //总股本
    double      AShares_ysh=241;                            //已上市流通A股
    double      BShares_ysh=242;                            //已上市流通B股
    double      HShares_ysh=243;                            //已上市流通H股
    double      ShareHolders_=244;                           //股东人数(户)
    double      HoldingsNumber_dydgd=245;                   //第一大股东的持股数量
    double      TotalNumber_sdltgd=246;                     //十大流通股东持股数量合计(股)
    double      TotalNumber_sdgd=247;                       //十大股东持股数量合计(股)

    //机构持股
    double      jg_Total=248;                               //机构总量(家)
    double      TotalHoldings_jg=249;                       //机构持股总量(股)
    double      jg_QFII=250;                                //QFII机构数
    double      Holdings_QFII_jg=251;                       //QFII持股量
    double      jg_qs=252;                                  //券商机构数
    double      Holdings_jg_qs=253;                         //券商持股量
    double      jg_Insurance=254;                           //保险机构数
    double      Holdings_jg_Insurance=255;                  //保险持股量
    double      jg_Fund=256;                                //基金机构数
    double      Holdings_jg_Fund=257;                       //基金持股量
    double      jg_sb=258;                                  //社保机构数
    double      Holdings_jg_sb=259;                         //社保持股量
    double      jg_sm=260;                                  //私募机构数
    double      Holdings_jg_sm=261;                         //私募持股量
    double      jg_cwgs=262;                                //财务公司机构数
    double      Holdings_jg_cwgs=263;                       //财务公司持股量
    double      jg_nj=264;                                  //年金机构数
    double      Holdings_jg_nj=265;                         //年金持股量

    //新增指标
    double      Holdings_AStock_sdltgd=266;                 //十大流通股东中持有A股合计(股)[注：季度报告中，若股东同时持有非流通A股性质的股份(如同时持有流通A股和流通B股），指标264取的是包含同时持有非流通A股性质的流通股数]
    double      Holdings_dydltgd=267;                       //第一大流通股东持股量
    double      FreeTradableShares=268;                     //自由流通股(股)[注：1.自由流通股=已流通A股-十大流通股东5%以上的A股；2.季度报告中，若股东同时持有非流通A股性质的股份(如同时持有流通A股和流通H股），5%以上的持股取的是不包含同时持有非流通A股性质的流通股数，结果可能偏大； 3.指标按报告期展示，新股在上市日的下个报告期才有数据]
    double      AStock_sxlt=269;                            //受限流通A股(股)
    double      GeneralRiskPreparation=270;                 //一般风险准备(金融类)
    double      Income_qtzhsy=271;                          //其他综合收益(利润表)
    double      TotalIncome=272;                            //综合收益总额(利润表)
    double      gsymgsgdqy=273;                             //归属于母公司股东权益(资产负债表)
    double      jg_Bank=274;                                //银行机构数(家)(机构持股)
    double      Holdings_jg_Bank=275;                       //银行持股量(股)(机构持股)
    double      jg_ybfr=276;                                //一般法人机构数(家)(机构持股)
    double      Holdings_jg_ybfr=277;                       //一般法人持股量(股)(机构持股)
    double      NetProfitYear=278;                          //近一年净利润(元)
    double      jg_xt=279;                                  //信托机构数(家)(机构持股)
    double      Holdings_jg_xt=280;                         //信托持股量(股)(机构持股)
    double      jg_tsfr=281;                                //特殊法人机构数(家)(机构持股)
    double      Holdings_jg_tsfr=282;                       //特殊法人持股量(股)(机构持股)
    double      NetProfit_jq=283;                           //甲醛净资产收益率(每股指标)
    double      PerShareProfit_kf=284;                      //扣非每股收益(但季度财务指标)
    double      zjyn_yysr=285;                              //最近一年营业收入(万元)
    double      Holdings_gjd=286;                           //国家队持股数量(万股)[注：本指标统计包含汇金公司、证金公司、外汇管理局旗下投资平台、国家队基金、国开、养老金以及中科汇通等国家队机构持股数量]
    double      ywyg_jlrtbzfxx=287;                         //业务预告-本期净利润同比增幅下限%[注：指标285至294展示未来一个报告期的数据。例，3月31日至6月29日这段时间内展示的是中报的数据；如果最新的财务报告后面有多个报告期的业绩预告/快报，只能展示最新的财务报告后面的一个报告期的业绩预告/快报]
    double      yjyg_jlrtbzfsx=288;                         //业绩预告-本期净利润同比增幅上限%
    double      yjkb_gmjlr=289;                             //业绩快报-归母净利润
    double      yjkb_kfjlr=290;                             //业绩快报-扣费净利润
    double      yjkb_zzc=291;                               //业绩快报-总资产
    double      yjkb_jzc=292;                               //业绩快报-净资产
    double      yjkb_mgsy=293;                              //业绩快报-每股收益
    double      yjkb_tbjzcsyl=294;                          //业绩快报-摊薄净资产收益率
    double      yjkb_jqjzcsyl=295;                          //业绩快报-加权净资产收益率
    double      yjkb_mgjzc=296;                             //业绩快报-每股净资产
    double      yfpj_yfzk=297;                              //应付票据及应付账款(资产负债表)
    double      yspj_yszk=298;                              //应收票据及应收账款(资产负债表)
    double      DeferredIncome_zcfz=299;                    //递延收益(资产负债表-非流动负债)
    double      qtzhsy=300;                                 //其他综合收益(资产负债表)
    double      qtyqgj=301;                                 //其他权益工具(资产负债表)
    double      OtherGains=302;                             //其他收益(利润表)
    double      zcczsy=303;                                 //资产处置收益(利润表)
    double      NetProfit_cxjy=304;                         //持续经营净利润(利润表)
    double      NetProfit_zzjy=305;                         //终止经营净利润(利润表)
    double      Costs_Develop=306;                          //研发费用(利润表)
    double      Include_lxfy=307;                           //其中:利息费用(利润表-财务费用)
    double      Include_lxsr=308;                           //其中:利息收入
    double      NetCash_jynjjhd=309;                        //近一年经营活动现金流净额
    double      NetProfit_jyngm=310;                        //近一年归母净利润(万元)
    double      NetProfit_jynkf=311;                        //近一年扣非净利润(万元)
    double      NetCashFlow_jyn=312;                        //近一年现金净流量(万元)
    double      PerShareProfit_djd=313;                     //基本每股收益(单季度)
    double      yyzsr_djd=314;                              //营业总收入(单季度)(万元)
    double      yjyg_ggrq=315;                              //业绩预告公告日期[注：本指标展示未来一个报告期的数据。例,3月31日至6月29日这段时间内展示的是中报的数据；如果最新的财务报告后面有多个报告期的业绩预告/快报，只能展示最新的财务报告后面的一个报告期的业绩预告/快报的数据；公告日期格式为YYMMDD，例：190101代表2019年1月1日]
    double      cw_ggrq=316;                                //财务公告日期 [注：日期格式为YYMMDD,例：190101代表2019年1月1日]
    double      yjkb_ggrq=317;                              //业绩快报公告日期 [注：本指标展示未来一个报告期的数据。例,3月31日至6月29日这段时间内展示的是中报的数据；如果最新的财务报告后面有多个报告期的业绩预告/快报，只能展示最新的财务报告后面的一个报告期的业绩预告/快报的数据；公告日期格式为YYMMDD，例：190101代表2019年1月1日]
    double      NetCashFlow_jyntzhd=318;                    //近一年投资活动现金流净额(万元)
    double      yjyg_bqjlrxx=319;                           //业绩预告-本期净利润下限(万元)[注：指标317至318展示未来一个报告期的数据。例，3月31日至6月29日这段时间内展示的是中报的数据；如果最新的财务报告后面有多个报告期的业绩预告/快报，只能展示最新的财务报告后面的一个报告期的业绩预告/快报]
    double      yjyg_bqjlrsx=320;                           //业绩预告-本期净利润上限(万元)
    double      yyzsr_TTM=321;                              //营业总收入TTM(万元)
    double      TotalNumber_Employees=322;                  //员工总数(人)
    double      FreCashFlowPerShareEnp=323;                 //每股企业自由现金流
    double      FreCashFlowPerShareHol=324;                 //每股股东自由现金流
    double      FinValue_323=325;                           //近一年营业利润(万元)
    double      FinValue_324=326;                           //净利润(单季度)(万元)
    double      FinValue_325=327;                           //北上资金数(家)(机构持股)
    double      FinValue_326=328;                           //悲伤资金持股量(股)(机构持股)
    double      FinValue_327=329;
    double      FinValue_328=330;
    double      FinValue_329=331;
    double      FinValue_330=332;
    double      FinValue_331=333;
    double      FinValue_332=334;
    double      FinValue_333=335;
    double      FinValue_334=336;
    double      FinValue_335=337;
    double      FinValue_336=338;
    double      FinValue_337=339;
    double      FinValue_338=340;
    double      FinValue_339=341;
    double      FinValue_340=342;
    double      FinValue_341=343;
    double      FinValue_342=344;
    double      FinValue_343=345;
    double      FinValue_344=346;
    double      FinValue_345=347;
    double      FinValue_346=348;
    double      FinValue_347=349;
    double      FinValue_348=350;
    double      FinValue_349=351;
    double      FinValue_350=352;
    double      FinValue_351=353;
    double      FinValue_352=354;
    double      FinValue_353=355;
    double      FinValue_354=356;
    double      FinValue_355=357;
    double      FinValue_356=358;
    double      FinValue_357=359;
    double      FinValue_358=360;
    double      FinValue_359=361;
    double      FinValue_360=362;
    double      FinValue_361=363;
    double      FinValue_362=364;
    double      FinValue_363=365;
    double      FinValue_364=366;
    double      FinValue_365=367;
    double      FinValue_366=368;
    double      FinValue_367=369;
    double      FinValue_368=370;
    double      FinValue_369=371;
    double      FinValue_370=372;
    double      FinValue_371=373;
    double      FinValue_372=374;
    double      FinValue_373=375;
    double      FinValue_374=376;
    double      FinValue_375=377;
    double      FinValue_376=378;
    double      FinValue_377=379;
    double      FinValue_378=380;
    double      FinValue_379=381;
    double      FinValue_380=382;
    double      FinValue_381=383;
    double      FinValue_382=384;
    double      FinValue_383=385;
    double      FinValue_384=386;
    double      FinValue_385=387;
    double      FinValue_386=388;
    double      FinValue_387=389;
    double      FinValue_388=390;
    double      FinValue_389=391;
    double      FinValue_390=392;
    double      FinValue_391=393;
    double      FinValue_392=394;
    double      FinValue_393=395;
    double      FinValue_394=396;
    double      FinValue_395=397;
    double      FinValue_396=398;
    double      FinValue_397=399;
    double      FinValue_398=400;
    double      FinValue_399=401;
    double      FinValue_400=402;

    //资产负债表新增指标
    double      SpecialReserves=403;                        //专项储备(万元)
    double      SettlementReserves=404;                     //结算备付金(万元)
    double      DisfundFunds=405;                           //拆出资金(万元)
    double      MakeLoans_Advances=406;                     //发放贷款及垫款(万元)(流动资产科目)
    double      DerivativeFinancialAssets=407;              //衍生金融资产(万元)
    double      PremiumReceivable=408;                      //应收保费(万元)
    double      AccountsReceivable=409;                     //应收分保账款(万元)
    double      ysfbhtzbj=410;                              //应收分保合同准备金(万元)
    double      mrfsjrzc=411;                               //买入返售金融资产(万元)
    double      cydszc=412;                                 //划分为持有待售的资产(万元)
    double      ffdkdk=413;                                 //发放贷款及垫款(万元)(非流动资产科目)
    double      BorrowingMoney_zyyh=414;                    //向中央银行借款(万元)
    double      xsck_tycf=415;                              //吸收存款及同业存放
    double      crzj=416;                                   //拆入资金(万元)
    double      ysjrfz=417;                                 //衍生金融负债(万元)
    double      mchgjrzck=418;                              //卖出回购金融资产款(万元)
    double      yfsxf_yfyj=419;                             //应付手续费及佣金(万元)
    double      yffbzk=420;                                 //应付分保账款(万元)
    double      InsuranceReserve=421;                       //保险合同准备金(万元)
    double      dlmmzqk=422;                                //代理买卖证券款(万元)
    double      dlcxzqk=423;                                //代理承销证券款(万元)
    double      wcydsfz=424;                                //划分未持有待售的负债(万元)
    double      yjfz=425;                                   //预计负债(万元)
    double      DeferredIncome=426;                         //递延收益(万元)
    double      Include_yxg_fldfz=427;                      //其中:优先股(万元)(非流动负债科目)
    double      SustainedDebt_fldfz=428;                    //永续债(万元)(非流动负债科目)
    double      cqyfzgxc=429;                               //长期应付职工薪酬(万元)
    double      Include_yxg_syzqy=430;                      //其中:优先股(万元)(所有者权益科目)
    double      SustainedDebt_syzqy=431;                    //永续债(万元)(所有者权益科目)
    double      DebtInvestment=432;                         //债权投资(万元)
    double      OtherDebtInvestments=433;                   //其他债权投资(万元)
    double      qtqygjtz=434;                               //其他权益工具投资(万元)
    double      qtfldjrzc=435;                              //其他非流动金融资产(万元)
    double      ContractDebt=436;                           //合同负债(万元)
    double      ContractAssets=437;                         //合同资产(万元)
    double      OtherAssets=438;                            //其他资产(万元)
    double      ReceivablesFinancing=439;                   //应收款项融资(万元)
    double      UseRightAssets=440;                         //使用权资产(万元)
    double      LeasingLiabilities=441;                     //租赁负债(万元)
    double      FinValue_440=442;
    double      FinValue_441=443;
    double      FinValue_442=444;
    double      FinValue_443=445;
    double      FinValue_444=446;
    double      FinValue_445=447;
    double      FinValue_446=448;
    double      FinValue_447=449;
    double      FinValue_448=450;
    double      FinValue_449=451;
    double      FinValue_450=452;
    double      FinValue_451=453;
    double      FinValue_452=454;
    double      FinValue_453=455;
    double      FinValue_454=456;
    double      FinValue_455=457;
    double      FinValue_456=458;
    double      FinValue_457=459;
    double      FinValue_458=460;
    double      FinValue_459=461;
    double      FinValue_460=462;
    double      FinValue_461=463;
    double      FinValue_462=464;
    double      FinValue_463=465;
    double      FinValue_464=466;
    double      FinValue_465=467;
    double      FinValue_466=468;
    double      FinValue_467=469;
    double      FinValue_468=470;
    double      FinValue_469=471;
    double      FinValue_470=472;
    double      FinValue_471=473;
    double      FinValue_472=474;
    double      FinValue_473=475;
    double      FinValue_474=476;
    double      FinValue_475=477;
    double      FinValue_476=478;
    double      FinValue_477=479;
    double      FinValue_478=480;
    double      FinValue_479=481;
    double      FinValue_480=482;
    double      FinValue_481=483;
    double      FinValue_482=484;
    double      FinValue_483=485;
    double      FinValue_484=486;
    double      FinValue_485=487;
    double      FinValue_486=488;
    double      FinValue_487=489;
    double      FinValue_488=490;
    double      FinValue_489=491;
    double      FinValue_490=492;
    double      FinValue_491=493;
    double      FinValue_492=494;
    double      FinValue_493=495;
    double      FinValue_494=496;
    double      FinValue_495=497;
    double      FinValue_496=498;
    double      FinValue_497=499;
    double      FinValue_498=500;
    double      FinValue_499=501;
    double      FinValue_500=502;

    //利润表新增指标
    double      PerShareProfit_xs=503;                      //稀释每股收益(元)
    double      yyzsr=504;                                  //营业总收入(万元)
    double      ExchangProfit=505;                          //汇兑收益(万元)
    double      Include_gsymgszhsy=506;                     //其中:归属于母公司综合收益(万元)
    double      Include_gsyssgdzhsy=507;                    //其中:归属于少数股东综合收益(万元)
    double      lxsr=508;                                   //利息收入(万元)
    double      EarnedPremium=509;                          //已赚保费(万元)
    double      sxf_yjsr=510;                               //手续费及佣金收入(万元)
    double      lxzc=511;                                   //利息支出(万元)
    double      sxf_yjzc=512;                               //手续费及佣金支出(万元)
    double      Surrender=513;                              //退保金(万元)
    double      pfzcje=514;                                 //赔付支出净额(万元)
    double      tqbxhtzbjje=515;                            //提取保险合同准备金净额(万元)
    double      bdhlzc=516;                                 //保单红利支出(万元)
    double      SubinsuranceCost=517;                       //分保费用(万元)
    double      Include_fldzcczld=518;                      //其中:非流动资产处置利得(万元)
    double      CreditLoss=519;                             //信用减值损失(万元)
    double      jcktqsy=520;                                //净敞口套期收益(万元)
    double      yyzcb=521;                                  //营业总成本(万元)
    double      CreditLoss_old=522;                         //信用减值损失(万元、2019格式)
    double      AssetsLoss_old=523;                         //资产减值损失(万元、2019格式)
    double      FinValue_522=524;
    double      FinValue_523=525;
    double      FinValue_524=526;
    double      FinValue_525=527;
    double      FinValue_526=528;
    double      FinValue_527=529;
    double      FinValue_528=530;
    double      FinValue_529=531;
    double      FinValue_530=532;
    double      FinValue_531=533;
    double      FinValue_532=534;
    double      FinValue_533=535;
    double      FinValue_534=536;
    double      FinValue_535=537;
    double      FinValue_536=538;
    double      FinValue_537=539;
    double      FinValue_538=540;
    double      FinValue_539=541;
    double      FinValue_540=542;
    double      FinValue_541=543;
    double      FinValue_542=544;
    double      FinValue_543=545;
    double      FinValue_544=546;
    double      FinValue_545=547;
    double      FinValue_546=548;
    double      FinValue_547=549;
    double      FinValue_548=550;
    double      FinValue_549=551;
    double      FinValue_550=552;
    double      FinValue_551=553;
    double      FinValue_552=554;
    double      FinValue_553=555;
    double      FinValue_554=556;
    double      FinValue_555=557;
    double      FinValue_556=558;
    double      FinValue_557=559;
    double      FinValue_558=560;
    double      FinValue_559=561;
    double      FinValue_560=562;

    //现金流量表新增指标
    double      Add_qtyyyxxj=563;                           //加:其他原因对现金的影响2(万元)(现金的期末余额科目)
    double      NetIncrease_khck_tycfk=564;                 //客户存款和同业存放款项净增加额(万元)
    double      NetIncrease_zyyhjk=565;                     //向中央银行借款净增加额(万元)
    double      NetIncrease_jrjgcrzj=566;                   //向其他金融机构拆入资金净增加额(万元)
    double      CashReceived_ybxhtbf=567;                   //收到原保险合同保费取得的现金(万元)
    double      NetCashReceived_zbx=568;                    //收到再保险业务现金净额(万元)
    double      NetIncrease_bhcj_tzk=569;                   //保户储金及投资款净增加额(万元)
    double      NetIncrease_gyjz_dqsy_jrzc=570;             //处置以公允价值计量且其变动计入当期损益的金融资产净增加额(万元)
    double      CashReceived_lx_sxf_yj=571;                 //收取利息、手续费及佣金的现金(万元)
    double      NetIncrease_crzj=572;                       //拆入资金净增加额(万元)
    double      NetIncrease_hgywzj=573;                     //回购业务资金净增加额(万元)
    double      NetIncrease_khdkdk=574;                     //客户贷款及垫款净增加额(万元)
    double      NetIncrease_cfzyyh_tyk=575;                 //存放中央银行和同业款项净增加额(万元)
    double      CashPaid_ybxhtphk=576;                      //支付原保险合同赔付款项的现金(万元)
    double      CashPaid_lx_sxf_yj=577;                     //支付利息、手续费及佣金的现金(万元)
    double      CashPaid_bdhl=578;                          //支付保单红利的现金(万元)
    double      Include_CashReceived_zgsxsssgdtz=579;       //其中:子公司吸收少数股东投资收到的现金(万元)
    double      Include_zgszfgssgd_gllr=580;                //其中:子公司支付给少数股东的股利、利润(万元)
    double      tzxfdczjtx=581;                             //投资性房地产的折旧及摊销(万元)
    double      CreditLoss_New=582;                             //信用减值损失(万元)
    double      FinValue_581=583;
    double      FinValue_582=584;
    double      FinValue_583=585;
    double      FinValue_584=586;
    double      FinValue_585=587;
    double      FinValue_586=588;
    double      FinValue_587=589;
    double      FinValue_588=590;
    double      FinValue_589=591;
    double      FinValue_590=592;
    double      FinValue_591=593;
    double      FinValue_592=594;
    double      FinValue_593=595;
    double      FinValue_594=596;
    double      FinValue_595=597;
    double      FinValue_596=598;
    double      FinValue_597=599;
    double      FinValue_598=600;
    double      FinValue_599=601;
    double      FinValue_600=602;
}

message fingpjy_down_data
{
	int64     nkey=1;                                   //股票nkey
    int64     Date=2;                                   //日期

    double      Shareholders=3;                           //股东户数(户)               股东人数
    double      TotalBuyIn=4;                             //买入总计(万元)             龙虎榜[注：该指标展示最近一年的数据]
    double      TotalSoldOut=5;                           //卖出总计(万元)             龙虎榜[注：该指标展示最近一年的数据]
    double      rzye_rzrq1=6;                             //融资余额(万元)             融资融券1
    double      rqyl_rzrq1=7;                             //融券余量(股)               融资融券1
    double      rzrqye=8;                                 //融资融券余额
    double      cjjj_dzjy=9;                              //成交均价(元)               大宗交易
    double      cje_dzjy=10;                               //成交额(万元)               大宗交易
    double      cjjj_zjc=11;                               //成交均价(元)               增减持
    double      ShareChanged_zjc=12;                       //变动股数(股)               增减持
    double      ShareHoldings_lgt=13;                      //持股数量(股)               陆股通持股量
    double      NetBuyIn_lgt=14;                           //陆股通市场净买入(万元)     陆股通市场成交净额[注：官方只公布了每日的前十名数据]
    double      jg_Seller=15;                              //卖方机构个数               龙虎榜机构(卖方)数据
    double      SoldOut_jg_Seller=16;                      //机构卖出金额(万元)         龙虎榜机构(卖方)数据
    double      jg_Buyer=17;                               //买方机构个数               龙虎榜机构(买方)数据
    double      BuyIn_jg_Buyer=18;                         //机构买入金额(万元)         龙虎榜机构(买方)数据
    double      jsyjgdycs=19;                              //近3月机构调研次数          近3月机构调研情况
    double      jsydyjgsl=20;                              //近3月调研机构数量          近3月机构调研情况
    double      BuyIn_rz=21;                               //融资买入额(万元)           融资融券2
    double      rzche=22;                                  //融资偿还额(万元)           融资融券2
    double      SoldOut_rq=23;                             //融券卖出量(股)             融资融券3
    double      rqchl=24;                                  //融券偿还量(股)             融资融券3
    double      NetBuy_rz=25;                              //融资净买入(万元)           融资融券4
    double      NetSale_rq=26;                             //融券净卖出(股)             融资融券4
    double      None=27;                                   //暂无                       原 发行价 指标移至 股票的单个数据(非序列)
    double      ZDTStatus=28;                              //涨跌停状态                 涨停2，曾涨停1，跌停-2，曾跌停-1
    double      ZDTFDNum=29;                               //涨跌停封单金额
    double      zsz=30;                                    //总市值                       
    double      lhbyybData_BuyIN=31;                       //买入金额                   龙虎榜营业部数据
    double      lhbyybData_SellOut=32;                     //卖出金额                   龙虎榜营业部数据
    double      lhbhsgtData_BuyIN=33;                      //买入金额                   龙虎榜沪深股通数据
    double      lhbhsgtData_SellOut=34;                    //卖出金额                   龙虎榜沪深股通数据
    double      wx_StockPerWeek=35;                        //无限售股份质押数           每周股票质押数量
    double      yx_StockPerWeek=36;                        //有限售股份质押数           每周股票质押数量
    double      zy_Ratio=37;                               //质押比例                   每周股票质押比例
    double      GPJYValue_21=38;
    double      GPJYValue_22=39;
    double      GPJYValue_23=40;
    double      GPJYValue_24=41;
    double      GPJYValue_25=42;
    double      GPJYValue_26=43;
    double      GPJYValue_27=44;
    double      GPJYValue_28=45;
    double      GPJYValue_29=46;
    double      GPJYValue_30=47;
}

message finscjy_down_data
{
	int64     nkey=1;                                   //股票nkey
    int64     Date=2;                                   //日期

    double      hsrzye=3;                                 //沪深融资余额(万元)               融资融券
    double      hsrqye=4;                                 //沪深融券余额(万元)               融资融券
    double      FlowIn_hgt=5;                             //沪股通流入金额(亿元)             陆股通资金流入
    double      FlowIn_sgt=6;                             //深股通流入金额(亿元)             陆股通资金流入
    double      Stock_zt=7;                               //涨停股个数                       沪深涨停股个数[注：该指标展示20160926日之后的数据]
    double      Stock_zzt=8;                              //曾涨停股个数                     沪深涨停股个数[注：该指标展示20160926日之后的数据]
    double      Stock_dt=9;                               //跌停股个数                       沪深跌停股个数
    double      Stock_zdt=10;                              //曾跌停股个数                     沪深跌停股个数
    double      NetHolding_sz50=11;                        //净持仓(手)                       上证50股指期货[注：该指标展示20171009日之后的数据]
    double      NetHolding_hs300=12;                       //净持仓(手)                       沪深300股指期货[注：该指标展示20171009日之后的数据]
    double      NetHolding_zz500=13;                       //净持仓(手)                       中证500股指期货[注：该指标展示20171009日之后的数据]
    double      ETF_FundSize=14;                           //ETF基金规模(亿)                  ETF基金规模数据 
    double      ETF_jss=15;                                //ETF净申赎(亿)                    ETF基金规模数据
    double      NewInvestors=16;                           //新增投资者(万户)                 每周新增投资者
    double      IncreaseHold=17;                           //增持额(万元)                     增减持统计[注：部分公司公告滞后, 造成每天查看的数据可能会不一样]
    double      DecreaseHold=18;                           //减持额(万元)                     增减持统计[注：部分公司公告滞后, 造成每天查看的数据可能会不一样]
    double      yjdzjye=19;                                //溢价的大宗交易额(万元)           大宗交易
    double      zjdzjye=20;                                //折价的大宗交易额(万元)           大宗交易
    double      xsjjjhe=21;                                //限售解禁计划额(亿元)             限售解禁[注：该指标展示201802月之后的数据=22; 部分股票的解禁日期延后，造成不同日期提取的某天的计划额可能不同]
    double      xsjjgfsjssje=22;                           //限售解禁股份实际上市金额(亿元)   限售解禁[注：该指标展示201802月之后的数据=24; 部分股票的解禁日期延后，造成不同日期提取的某天的计划额可能不同]
    double      sczfhe=23;                                 //市场总分红额(亿元)               分红[注：除权派息日的A股市场总分红额]
    double      sczmze=24;                                 //市场总募资额(亿元)               募资[注：发行日期 / 除权日期的首发、配股和增发的总募资额]
    double      fbcgzj=25;                                 //封板成功资金(亿元)               打板资金[注：该指标展示20160926日之后的数据]
    double      fbsbzj=26;                                 //封板失败资金(亿元)               打板资金[注：该指标展示20160926日之后的数据]
    double      BuyInTotal_lhb=27;                         //买入总金额(亿元)                 龙虎榜
    double      SellOutTotal_lhb=28;                       //卖出总金额(亿元)                 龙虎榜
    double      BuyInTotal_lhbjg=29;                       //买入总金额(亿元)                 龙虎榜机构
    double      SellOutTotal_lhbjg=30;                     //卖出总金额(亿元)                 龙虎榜机构
    double      BuyInTotal_lhbyyb=31;                      //买入总金额(亿元)                 龙虎榜营业部
    double      SellOutTotal_lhbyyb=32;                    //卖出总金额(亿元)                 龙虎榜营业部
    double      BuyInTotal_lhbhsgt=33;                     //买入总金额(亿元)                 龙虎榜沪深股通
    double      SellOutTotal_lhbhsgt=34;                   //卖出总金额(亿元)                 龙虎榜沪深股通
    double      BuyInTotal_lhblgt=35;                      //买入总金额(亿元)                 龙虎榜陆股通
    double      SellOutTotal_lhblgt=36;                    //卖出总金额(亿元)                 龙虎榜陆股通
    double      sszyl_wx=37;                               //深市质押率(%)                    每周无限售质押率
    double      hszyl_wx=38;                               //沪市质押率(%)                    每周无限售质押率
    double      sszyl_yx=39;                               //深市质押率(%)                    每周有限售质押率
    double      hszyl_yx=40;                               //沪市质押率(%)                    每周有限售质押率
    double      lbg_WithST=41;                             //连板股个数(包含ST和未开板新股)   连板家数
    double      lbg_WithOutST=42;                          //连板股个数(不包含ST和未开板新股) 连板家数
    double      ztg_WithOutST=43;                          //涨停股个数(不包含ST和未开板新股) 沪深涨跌停股个数
    double      dtg_WithOutST=44;                          //跌停股个数(不包含ST和未开板新股) 沪深涨跌停股个数
    double      BuyIn_hsrz=45;                             //沪深融资买入额(万元)             融资融券
    double      SellOut_hsrz=46;                           //沪深融资卖出量(万股)             融资融券
    double      zyRatio_mzsc=47;                           //每周市场质押比例(%)              每周市场质押比
    double      SCJYValue_27=48;
    double      SCJYValue_28=49;
    double      SCJYValue_29=50;
    double      SCJYValue_30=51;
    double      SCJYValue_31=52;
    double      SCJYValue_32=53;
    double      SCJYValue_33=54;
    double      SCJYValue_34=55;
    double      SCJYValue_35=56;
    double      SCJYValue_36=57;
    double      SCJYValue_37=58;
    double      SCJYValue_38=59;
    double      SCJYValue_39=60;
    double      SCJYValue_40=61;
}

message fingponedate_down_data
{
	int64     nkey=1;                                   //股票nkey
    int64     Date=2;                                   //日期

    double      fxj=3;                                    //发行价(元)
    double      zfxsl=4;                                  //总发行数量(万股)
    double      TargetPrice_yzyq=5;                       //一致预期目标价(元)
    double      TYear_yzyq=6;                             //一致预期T年度
    double      PerShareProfit_TYear_yzyq=7;              //一致预期T年度每股收益
    double      PerShareProfit_T1Year_yzyq=8;             //一致预期T+1年度每股收益
    double      PerShareProfit_T2Year_yzyq=9;             //一致预期T+2年度每股收益
    double      NetProfit_TYear_yzyq=10;                   //一致预期T年净利润
    double      NetProfit_T1Year_yzyq=11;                  //一致预期T+1年净利润
    double      NetProfit_T2Year_yzyq=12;                  //一致预期T+2年净利润
    double      Receipt_TYear_yzyq=13;                     //一致预期T年营业收入
    double      Receipt_T1Year_yzyq=14;                    //一致预期T+1年营业收入
    double      Receipt_T2Year_yzyq=15;                    //一致预期T+2年营业收入
    double      Profit_TYear_yzyq=16;                      //一致预期T年营业利润
    double      Profit_T1Year_yzyq=17;                     //一致预期T+1年营业利润
    double      Profit_T2Year_yzyq=18;                     //一致预期T+2年营业利润
    double      NetAssetPerShare_TYear_yzyq=19;            //一致预期T年每股净资产
    double      NetAssetPerShare_T1Year_yzyq=20;           //一致预期T+1年每股净资产
    double      NetAssetPerShare_T2Year_yzyq=21;           //一致预期T+2年每股净资产
    double      RateYield_TYear_yzyq=22;                   //一致预期T年净资产收益率
    double      RateYield_T1Year_yzyq=23;                  //一致预期T+1年净资产收益率
    double      RateYield_T2Year_yzyq=24;                  //一致预期T+2年净资产收益率
    double      PE_TYear_yzyq=25;                          //一致预期T年PE
    double      PE_T1Year_yzyq=26;                         //一致预期T+1年PE
    double      PE_T2Year_yzyq=27;                         //一致预期T+2年PE
    double      jjr_Latest=28;                             //最新解禁日
    double      jjsl_Latest=29;                            //解禁数量
    double      GPONEDat_28=30;
    double      GPONEDat_29=31;
    double      GPONEDat_30=32;
    double      GPONEDat_31=33;
    double      GPONEDat_32=34;
    double      GPONEDat_33=35;
    double      GPONEDat_34=36;
    double      GPONEDat_35=37;
    double      GPONEDat_36=38;
    double      GPONEDat_37=39;
    double      GPONEDat_38=40;
    double      GPONEDat_39=41;
    double      GPONEDat_40=42;
}

message finance_down_ans
{
	repeated finance_down_data field = 1;
}

message finvalue_down_ans
{
	repeated finvalue_down_data field = 1;
}

message fingpjy_down_ans
{
	repeated fingpjy_down_data field = 1;
}

message finscjy_down_ans
{
	repeated finscjy_down_data field = 1;
}

message fingponedate_down_ans
{
	repeated fingponedate_down_data field = 1;
}

message finance_zip_req
{
	uint32 sdate = 1;
	uint32 edate = 2;
}

message finance_zip_data
{
	uint32 date = 1;
	string data = 2;
}

message finance_zip_ans
{
	repeated finance_zip_data zipdata = 1;
}

message pzyd_ans
{
	repeated dxjl_data data = 1;
}

message oldname_data
{
	uint64 nkey = 1;
	uint32 date = 2;
	string name = 3;
}

message oldname_ans
{
	repeated oldname_data field = 1;
}

message snmoneyflow_req
{
	uint32 type = 1;
	uint32 period = 2;
	uint32 offset = 3;
	uint32 num = 4;
}

message snmoney_req
{
	uint32 type = 1;
	uint32 date = 2;
}

message snmoneyhold_req
{
	uint32 type = 1;
	uint32 date = 2;
	uint32 period = 3;
	uint32 offset = 4;
	uint32 num = 5;
}

message hsizs_req
{
	uint32 period = 1;
	uint32 offset = 2;
	uint32 num = 3;
}

message hsizs_data
{
	uint32 date = 1;
	double price = 2;
}

message hsizs_ans
{
	repeated hsizs_data data = 1;
}

message snmoneyflow_data
{
	uint32 date = 1;
	double amt = 2;
}

message snmoneyflow_ans
{
	repeated snmoneyflow_data data = 1;
}

message snmoneybase_ans
{
	uint32 date = 1;
	double buyamt = 2;
	double sellamt = 3;
	double totalquota = 4;
	double balancequota = 5;
	double userate = 6;
	double flow5day = 7;
	double flow20days = 8;
	double flow60days = 9;
}

message snrankinfo_data
{
	uint32 setcode = 1;
	string code = 2;
	double buyamt = 3;
	double sellamt = 4;
	double totalamt = 5;
	string name = 6;
}

message snrankinfo_ans
{
	repeated snrankinfo_data data = 1;
}

message snmoneyhold_data
{
	uint32 setcode = 1;
	string code = 2;
	int64 hold = 3;
	double rate = 4;
	string name = 5;
}

message snmoneyhold_ans
{
	repeated snmoneyhold_data data = 1;
}

message hqexdata_ans
{
	uint32 suspend = 1; //停牌数量
	uint32 nflimitup = 2; //非一字板涨停家数
	uint32 limitupopen = 3; //涨停炸板家数
}

message ltlbdata_ans
{
	uint32 yzbzt = 1; //一字板涨停数(日)
	uint32 zt = 2; //涨停数(日)
	double fbl = 3; //封板率(日)
	double lbl = 4; //连板率(日)
	uint32 lb = 5; //连板数(日)
	uint32 zb = 6; //炸板数(日)
	uint32 tp = 7; //停牌数(日)
}

message minltlb
{
	int64 jcltime = 1;
	ltlbdata_ans data = 2;
}

message minltlb_ans
{
	repeated minltlb data = 1;
}

message ltlbarr
{
	uint64 nkey = 1;
	uint32 state = 2; //0:普通状态 1:一字板(涨停) 2:一字板(跌停) 3:回封 4:炸板 5:停牌
	double volrate = 3; //开盘量比
}

message ltlbarr_ans
{
	repeated ltlbarr data = 1;
}

message ltbj_ans
{
	ltlbdata_ans yesdata = 1;
	ltlbdata_ans curdata = 2;
}

message bdqr_req
{
	uint32 type = 1; // 0-板块 1-个股
	uint64 blknkey = 2; // type=1时， 需要发送此字段， 板块的nkey
	repeated uint64 nkeys = 3; // 此字段不发送的话，type=0是请求全部板块，type=1是请求指定板块下的全部个股; 发送的话，则是请求指定的品种
}

message bdqr_data
{
	uint64 nkey = 1;
	uint32 qr = 2; //0:绝对强于市场 1:相对强于市场 2:相对弱于市场 3:绝对弱于市场 4:同步于市场
}

message bdqr_ans
{
	repeated bdqr_data data = 1;
}

//////////////////////////////////////////
/// 公式部分
//////////////////////////////////////////
enum	RECAL_FORMU_TYPE
{
    P_F_ZB = 0;	//指标类型公式
    P_F_TJ = 1;	//条件选股类型公式
    P_F_JY = 2;	//交易系统公式
    P_F_WC = 3;	//五彩K线公式
    P_F_FC = 4;	//自定义函数公式
    P_F_VB = 5;	//VB
    P_F_BIN= 6;	//二进制函数类型，保存分组的时候用得上，因为用户函数也要分组
    P_F_AL = 7;   //全部
    P_SEPCLINE_TYPE = 8;
    P_F_BZB= 9;	//二进制方式的指标公式
    P_F_COMBO = 10; // 组合
}

//参数信息
message  ReCalcParamInfoEx	
{
    string	szName=1;			//参数名  (参数中文名)
    string	szParamInfo=2;	//	参数介绍，这些可以直接在数据库操作
    double	fMin=3;			//最小
    double	fMax=4;
    // 计算总是根据dval来 ,fDef保留
    double	dVal=5;
	double	fStep=6;	// 步长
}

//自定义指标
message	RFormularSelfIndex
{
    uint32	nSet=1;		//指标集合
    string	szKey=2;	//指标英文名，关键字
    string	szName=3;	//指标中文名
	uint64	dwKey=4;
	uint32	dwGroup=5;	
	uint32	nDrawMode=6;	//表示是否主图,副图,缺省副图
	repeated	double	dGridInfos=7;
 	repeated	ReCalcParamInfoEx	params=8;
}

message	RFormularGroupList
{
	string		group=1;
	repeated	RFormularSelfIndex	formulars=2;
}
// 请求单个公式,应答就是具体 RFormularSelfIndex
message	RFormularSelfIndex_Req
{
    uint32	nSet=1;		//指标集合
    string	szKey=2;	//指标英文名，关键字
}
// 请求整个集合的公式，应答 FormularSet_Ans
message	RFormularSet_Req
{
    uint32	nSet=1;		//指标集合
}

message	RFormularSet_Ans
{
    uint32	nSet=1;		//指标集合
	repeated	RFormularGroupList	groupformulars=2;
}
// 计算后的公式请求(如果只是提取因子库，不用指定参数，指定也无效，已经计算好了)
message	ReCalcFomular_Req
{
    uint32	nSet=1;		//指标集合
    string	szKey=2;	//指标英文名，关键字
	tagCodeWithNkey	code=3;
	uint32	period=4;
	uint32	tqflag=5;		// 复权标志
 	repeated	ReCalcParamInfoEx	params=6;	// 设置参数，只需要填写参数名称和dval即可
}
// 计算公式的其中一个输出信息
message	ReCalcOutVarInfo
{
    uint32		nType=1; //数据类型'0' '1' '2' '3'(直接输出指定函数，函数怎么实现公式体不管)
    string		szWord=2;
    // 特别输出: 空 没有 ； 1=强制输出，例如画图输出；
    string		szSpecial=3;		// 特别画线名
	uint32		nColor=4;		// 0 忽略
	uint32		nDrawMode=5;	// 输出线的画法，样式指定
	uint32		nLineThick=6;	// 线的宽度
	uint32		nPrecision=7;	// 精度
	uint32		lineno=8;
	repeated	double	externdat=9;	// 扩展输出，通常不用
	double		dVal=10;
	repeated	double	lpArray=11;
	string		szVal=12;
	repeated	string	szValArray=13;
}
// 计算公式应答(或者因子，输出结果，和对应参数，股票品种，周期)
message	ReCalcFomular_Ans
{
	uint32	nSet=1;		//指标集合
	string	szKey=2;	//指标英文名，关键字
	tagCodeWithNkey	code=3;
	uint32	period=4;
	repeated	ReCalcParamInfoEx	params=5;	// 设置参数，只需要填写参数名称和dval即可
	repeated	ReCalcOutVarInfo	outinfos=6;	// 公式有多个输出
}

//每日复盘数据
message Mrfp_hqinfo
{
	tagCodeWithNkey code=1;
	double yclose=2;
	double nowprice=3;
	uint32 upnum=4;
	uint32 downnum=5;
	uint32 equnum=6;
	double amt=7;
	double vol=8;
	double openprice=9;
	double highPrice=10;
	double lowprice=11;
}

message Mrfp_hqinfo_ans
{
	repeated Mrfp_hqinfo data=1;
}

message Mrfp_agtj_ans
{
	uint32 upnum=1;
	uint32 downnum=2;
	uint32 equnum=3;
	uint32 ztnum=4;
	uint32 dtnum=5;
	uint32 hztnum=6;
	uint32 ldtnum=7;
}

message Qsjg_sort_req
{
	repeated tagCodeWithNkey code=1;
	uint32 startxh = 2;
	uint32 wantnum = 3;
	uint32 sorttype = 4;
}

message Qsjg_strategy
{
	uint32 type = 1;
	string s=2;
}

message Qsjg_strategy_ans
{
	repeated Qsjg_strategy data = 1;
}

// GS计算
message _ployline
{
	tagDoubleArray condition = 1;
	tagDoubleArray price = 2;
}

message _polyline
{
	tagDoubleArray condition = 1;
	tagDoubleArray price = 2;
}

message _drawline
{
	tagDoubleArray condition1 = 1;
	tagDoubleArray price1 = 2;
	tagDoubleArray condition2 = 3;
	tagDoubleArray price2 = 4;
	tagDoubleArray expand = 5;
}

message _drawkline
{
	tagDoubleArray high = 1;
	tagDoubleArray open = 2;
	tagDoubleArray low = 3;
	tagDoubleArray close = 4;
}

message _drawnumber
{
	tagDoubleArray condition = 1;
	tagDoubleArray price = 2;
	tagDoubleArray number = 3;
	tagDoubleArray precision = 4;
}

message _drawnumber_fix
{
	tagDoubleArray condition = 1;
	double x = 2;
	double y = 3;
	double type = 4;
	tagDoubleArray number = 5;
}

message _drawtextex
{
	tagDoubleArray condition = 1;
	double type = 2;
	double x = 3;
	double y = 4;
	string text = 5;
}

message _drawtext
{
	tagDoubleArray condition = 1;
	tagDoubleArray price = 2;
	string text = 3;
}

message _drawtext_fix
{
	tagDoubleArray condition = 1;
	double x = 2;
	double y = 3;
	double type = 4;
	string text = 5;
}

message _drawbmp
{
	tagDoubleArray condition = 1;
	tagDoubleArray price = 2;
	string bmpfile = 3;
}

message _drawband
{
	tagDoubleArray var1 = 1;
	double color1 = 2;
	tagDoubleArray var2 = 3;
	double color2 = 4;
}

message _drawicon
{
	tagDoubleArray condition = 1;
	tagDoubleArray price = 2;
	tagDoubleArray type = 3;
	tagDoubleArray up = 4;
}

message _drawtextabs
{
	double x = 1;
	double y = 2;
	string text = 3;
}

message _drawtextrel
{
	double x = 1;
	double y = 2;
	string text = 3;
}

message _fillrgn
{
	tagDoubleArray price1 = 1;
	tagDoubleArray price2 = 2;
	repeated tagDoubleArray condition = 3;
	tagDoubleArray color = 4;
}

message _floatrgn
{
	tagDoubleArray price1 = 1;
	tagDoubleArray price2 = 2;
	repeated tagDoubleArray condition = 3;
	tagDoubleArray color = 4;
}

message _fillback
{
	tagDoubleArray condition1 = 1;
	tagDoubleArray condition2 = 2;
	tagDoubleArray type = 3;
	string color1 = 4;
	string color2 = 5;
}

message _fillallback
{
	tagDoubleArray type = 1;
	tagDoubleArray color1 = 2;
	tagDoubleArray color2 = 3;
}

message _partline
{
	tagDoubleArray price = 1;
	repeated tagDoubleArray condition = 2;
	tagDoubleArray color = 3;
}

message _drawgbk
{
	tagDoubleArray condition = 1;
	tagDoubleArray color1 = 2;
	tagDoubleArray color2 = 3;
	tagDoubleArray bhorizontal = 4;
	string bmpfile = 5;
	tagDoubleArray bstretch = 6;
}

message _drawsl
{
	tagDoubleArray condition = 1;
	tagDoubleArray price = 2;
	tagDoubleArray slope = 3;
	tagDoubleArray len = 4;
	tagDoubleArray direct = 5;
}

message _stickline
{
	tagDoubleArray condition = 1;
	tagDoubleArray price1 = 2;
	tagDoubleArray price2 = 3;
	tagDoubleArray width = 4;
	tagDoubleArray empty = 5;
}

message _stickline3d
{
	tagDoubleArray condition = 1;
	tagDoubleArray price1 = 2;
	tagDoubleArray price2 = 3;
	tagDoubleArray width = 4;
	tagDoubleArray empty = 5;
}

message _stickline3dex
{
	tagDoubleArray condition = 1;
	tagDoubleArray price1 = 2;
	tagDoubleArray price2 = 3;
	tagDoubleArray width = 4;
	tagDoubleArray empty = 5;
	string color = 6;
}

message _drawox
{
	tagDoubleArray condition = 1;
	tagDoubleArray price = 2;
	tagDoubleArray width = 3;
	tagDoubleArray empty = 4;
}

message _strip
{
	double rgb1 = 1;
	double rgb2 = 2;
	double dir = 3;
}

message _drawrectrel
{
	double left = 1;
	double top = 2;
	double right = 3;
	double bottom = 4;
	double color = 5;
}

message _explain
{
	tagDoubleArray condition = 1;
	string text = 2;
}

message _explainex
{
	tagDoubleArray condition = 1;
	string text = 2;
	tagDoubleArray num = 3;
	tagDoubleArray precision = 4;
	tagDoubleArray skiplines = 5;
}

message _explainicon
{
	tagDoubleArray condition = 1;
	tagDoubleArray type = 2;
}

message _drawfuncs
{
	oneof draw
	{
		_ployline ployline = 1;
		_polyline polyline = 2;
		_drawline drawline = 3;
		_drawkline drawkline = 4;
		_drawnumber drawnumber = 5;
		_drawnumber_fix drawnumber_fix = 6;
		_drawtextex drawtextex = 7;
		_drawtext drawtext = 8;
		_drawtext_fix drawtext_fix = 9;
		_drawbmp drawbmp = 10;
		_drawband drawband = 11;
		_drawicon drawicon = 12;
		_drawtextabs drawtextabs = 13;
		_drawtextrel drawtextrel = 14;
		_fillrgn fillrgn = 15;
		_floatrgn floatrgn = 16;
		_fillback fillback = 17;
		_fillallback fillallback = 18;
		_partline partline = 19;
		_drawgbk drawgbk = 20;
		_drawsl drawsl = 21;
		_stickline stickline = 22;
		_stickline3d stickline3d = 23;
		_stickline3dex stickline3dex = 24;
		_drawox drawox = 25;
		_strip strip = 26;
		_drawrectrel drawrectrel = 27;
		_explain explain = 28;
		_explainex explainex = 29;
		_explainicon explainicon = 30;
	}
}

message gs_outvarinfo
{
	uint32 nType = 1;
	string szWord = 2;
	uint32 nSpecialOut = 3;
	string szSpecial = 4;
	string nPType = 5;
	repeated uint32 nProperty = 6;
	uint32 nColor = 7;
	uint32 nDrawMode = 8;
	uint32 nLineThick = 9;
	uint32 nPrecision = 10;
	uint32 isFloatUnit = 11;
	string szUnit = 12;
	uint32 nPlaySound = 13;
	string aReturn = 14;
	double dVal = 15;
	repeated double lpArray = 16;
	string szVal = 17;
	int32 lineno = 18;
	_drawfuncs drawfuncs = 19;
}

message gs_param
{
	string name = 1;
	double value = 2;
}

message gscalc_req
{
	int32 setcode = 1;
    string code = 2;
	int32 period = 3;
	string gskey = 4;
	repeated gs_param param = 5;
	int32 wantnum = 6;
	int32 tqtype = 7;
	int32 offset = 8;
}

message gscalc_ans
{
	repeated gs_outvarinfo outvar = 1;
}

message calczafdata
{
	int32 setcode = 1;
	uint64 nkey = 2;
	double zaf_5min = 3;
	double zaf_3days = 4;
	double zaf_5days = 5;
	double zaf_20days = 6;
	double zang_days = 7;
	double die_days = 8;
	double high_20days = 9;	
	double low_20days = 10;	
	double high_his = 11;
	double low_his = 12;
	double zaf_90days = 13;
	double zaf_180days = 14;
	double zaf_365days = 15;
	double zaf_10days = 16;
	double zaf_60days = 17;
	double zaf_curyear = 18;
}

message calczafdata_req
{
	int32 setcode = 1;
}

message calczafdata_ans
{
	repeated calczafdata  data= 1;
}

message calczdfaread_ans
{
	repeated int32 stocknum = 1;
}

message calczdfresult_req
{
	int32 idx = 1;
}

message calczdfresult_ans
{
	repeated tagCodeWithNkey code = 1;
}

message zdfprofit
{
	int64 jcltime = 1;
	double profit = 2;
}

message calczdfprofit_ans
{
	repeated zdfprofit zdf = 1;
}

message zdfzdt
{
	int64 jcltime = 1;
	int32 num_up = 2;
	int32 num_down = 3;
}

message calczdfzdtnum_ans
{
	repeated zdfzdt zdt = 1;
}

message calczdtdata_req
{
	int32 type = 1;
}

message calczdtdata_ans
{
	repeated tagCodeWithNkey code = 1;
}

message calcyesonelinecode_ans
{
	repeated tagCodeWithNkey code = 1;
}

message HisChip_Req
{
	int64 nkey 	 = 1;			//品种唯一key
	uint64 sDate = 2;			//起始日期 20230101
	uint64 eDate = 3;			//结束日期 20230606
	uint32 chipPeriod = 4;		//筹码周期 取值使用枚举chipPeriod
}

message HisChipData
{
        uint64 date = 1;                //日期
        double avg = 2;                        //平均成本
        double winner = 3;                //收盘获利
        double cr_70 = 4;                //70%集中度
        double cost_70_min = 5;        //70%成本最小价格
        double cost_70_max = 6;        //70%成本最大价格
        double cr_90 = 7;                //90%集中度
        double cost_90_min = 8;        //90%成本最小价格
        double cost_90_max = 9;        //90%成本最大价格
        double low = 10;                                //区间最低价
        double high = 11;                            //区间最高价
        double step = 12;                                //区间步长
        repeated uint32 chipData = 13;  //筹码数据 
}

message HisChip_Ans
{
        repeated HisChipData field = 1;
}

message moneyrank_data
{
	uint32 rank = 1;
	double posrate = 2;
	uint32 days = 3;
}

message moneyrank_ans
{
	repeated moneyrank_data data = 1;
}

message lbdata_ans
{
	tagCodeWithNkey code = 1;
	int32 lbnum = 2; //几连板
}

message lbdatadata_ans
{
	repeated lbdata_ans data = 1;
}

message FinanceValue
{
	string FuncName		= 1;//财务函数字段，如：FINANCE(1) FINANCE(7)
	double Value1		= 2;//数据值
	double Value2		= 3;//额外数据值
	string String3      = 4;//字符串数据
}

message FinanceDateValue
{
	int64 Date							= 1;//财务数据日期
	repeated FinanceValue finance		= 2;//关联函数字段，如：FINANCE(1) FINANCE(7)
	repeated FinanceValue finvalue		= 3;//专业财务字段，如：FINVALUE(1)
	repeated FinanceValue gpjyvalue		= 4;//股票交易类数据，如：GPJYVALUE(3,1,0)
	repeated FinanceValue bkjyvalue		= 5;//板块交易类数据，如：BKJYVALUE(5,1,1)
	repeated FinanceValue scjyvalue		= 6;//市场交易类数据，如：SCJYVALUE(1,1,1)
	repeated FinanceValue gponedat		= 7;//股票单个数据，如：GPONEDAT(1)
}

message Finance
{
	int64 nkey								= 1;//股票nkey
	repeated FinanceDateValue value			= 2;//财务数据属性
}

message StrategySystempoolResult
{
	string groupid = 1;
	string jsonstr = 2;
}

message StrategySystempoolReq 
{
	string groupid = 1;
}

message StrategySystempoolList
{
	repeated StrategySystempoolResult list = 1;
}
message FinanceNow_req 
{
        repeated tagCodeWithNkey code = 1;
}

message FinanceNow_ans
{
        repeated Finance data = 1;
}

message NewFinance_req
{
        tagCodeWithNkey code          = 1;                        //股票
        repeated string FuncName = 2;                        //财务函数串
}

message NewFinance_ans
{
        repeated Finance data = 1;        
}

message StatictisTradeNum
{
	uint64 jcltime = 1;
	uint32 buyTradeNum = 2;
	uint32 sellTradeNum = 3;
}

message StatictisTradeNum_Ans
{
	repeated StatictisTradeNum data = 1;
}

message StatictisTradeNum_Req
{
	tagCodeWithNkey code = 1;
	uint32	period=2;
	uint32	offset=3;
	uint32	num=4;
	uint32	mulnum=5;
}
//NEW_PROTOCOL_MINUTE_REQ		= 22020;	//新分时协议
message minute_new_req
{
	tagCodeWithNkey code=1;
	uint32	days=2;
	uint32  exhq=3;
}

message minute_new_ans
{
	repeated MinuteInfo	minute=1;
	CurrStockDataBEx	hq=2;
}

//NEW_PROTOCOL_KLINE_OFFSET_REQ  = 22010;	//新K线按偏移请求协议
message kline_new_req
{
	tagCodeWithNkey	code=1;
	uint32	period=2;
	uint32	mulnum=3;
	uint32	offset=4;
	uint32	num=5;
	uint32  tqflag=6;
	uint32	exhq=7;
}

message kline_new_ans
{
	repeated	AnalyDataB	aK=1;
	CurrStockDataBEx	hq=2;
}

message multi_today_moneyflow_req
{
	repeated uint64 nkeys=1; 
}

message nkey_moneyflow_data
{
	uint64 nkey = 1;
	int64 jcltime = 2;
	moneyflow_data amt = 3;
	moneyflow_data vol = 4;
	moneyflow_data cnt = 5;
}

message multi_today_moneyflow_ans 
{
	repeated nkey_moneyflow_data data = 1;
}

message cfgdata
{
	uint64 nkey = 1;
	string name = 2;
	tagInt64Array cfgnkey = 3;
}

message bigblock
{
	string name = 1;
	repeated cfgdata data = 2;
}

message blockxmldata
{
	uint32 updatetag = 1;
	repeated bigblock alldata = 2;
}

message zscfgxmldata
{
	uint32 updatetag = 1;
	bigblock data = 2;
}

message StrategyOnekey_commentEx
{
	string groupid = 1;
	string comment = 2;
	string tags = 3;
	string version = 4;
}

message Calc_zdqj_req
{
	double minzaf = 1;
	double maxzaf = 2;
	uint32 cmptype = 3; // 0: zaf==minzaf;  1: minzaf <= zaf < maxzaf  2: minzaf < zaf <= maxzaf  3: minzaf <= zaf <= maxzaf 4: minzaf < zaf < maxzaf
	uint32 coltype = 4;
	uint32 sorttype = 5;
	uint32 offset = 6;
	uint32 wantnum = 7;
}

message Calc_zdqj_ans
{
	repeated uint64 nkeys = 1;
	repeated CurrStockDataBEx hqs = 2;
}

message statics_exdata_req
{
	uint64 nkey = 1;
	uint32 offset = 2;
	uint32 wantnum = 3;
}

message statics_exdata_reqs
{
	repeated statics_exdata_req reqs = 1;
}

message statics_exdata
{
	uint64 updatetimer = 1;
	double ztmoney = 2;
	uint32 kbtimes = 3;
	uint32 zdtstate = 4;
	double fdmoney = 5;
	double fcrate = 6;
	double flrate = 7;
	uint64 zttimer = 8;
	double maxztmoney = 9;
	double maxdtmoney = 10;
	double kpmoney = 11;
	double phmoney = 12;
}

message statics_exdata_ans
{
	uint64 nkey = 1;
	repeated statics_exdata data = 2;
}

message statics_exdata_anss
{
	repeated statics_exdata_ans ans = 1;
}

message many_minute_req
{
	repeated uint64 nkey = 1;
}

message minute_data
{
	uint64 nkey = 1;
	repeated MinuteInfo	minute=2;
}

message many_minute_ans
{
	repeated minute_data data = 1;
}

message bfday_data_req
{
	uint64 nkey = 1;
	uint64 jcltime = 2;
}

message bfday_data_ans
{
	uint64 nkey = 1;
	uint64 jcltime = 2;
	double amt = 3;
	double vol = 4;
}

message rankinfo
{
	uint64 nkey = 1;
	int32 rank = 2;
}

message rankinfoarr
{
	uint32 coltype = 1;
	uint32 sorttype = 2;
	repeated rankinfo rank = 3;
}

message rankreq
{
	uint32 coltype = 1;
	uint32 sorttype = 2;
}

message sort_rankex_req
{
	uint32 setDomain=1;
	uint32 coltype=2;
	uint32 startxh=3;
	uint32 wantnum=4;
	uint32 sorttype=5;
	repeated uint32 fieldids=6;
	repeated rankreq longrank = 7;
	uint32 needhyzafrank=8;
}

message sort_rankex_ans
{
	repeated uint32 fieldids=1;	//直接告知客户端，避免重新查询ID
	repeated hq_with_fields fields=2;	
	repeated rankinfoarr rankfield=3;
	repeated rankinfo hyzafrankfiled=4;
}

message KlineLabel_req
{
	tagCodeWithNkey code = 1;
	uint32 offset = 2;
}

message KlineLabel_Arr_req
{
	uint32 setcode = 1;
	repeated KlineLabel_req data = 2;
}

message KlineLabel_data
{
	uint32 date = 1;
	uint32 flag = 2;
	string remark = 3;
}

message KlineLabel_ans
{
	repeated KlineLabel_data data = 1;
}

message KlineLabelWithNkey_data
{
	uint64 nkey = 1;
	KlineLabel_ans ans = 2;
}

message KlineLabel_Arr_ans
{
	repeated KlineLabelWithNkey_data arrdata = 1;
}

message lv2OrderTick
{
	uint64 sequence = 1;	//序号
	uint64 buy_order_no = 2; //买方委托编号
	uint64 sell_order_no = 3; //卖方委托编号
	int64 price = 4; //价格 = 实际价格 * 1000
	int64 qty = 5; // 数量
	uint32 type = 6; //类型 0-未知 1-委买 2-委卖 3-成交 4-撤单 
	uint32 timer = 7; //时间 hhMMssmmm
}

message lv2OrderTick_req
{
	tagCodeWithNkey code=1;
	uint32 offset=2;// 偏移 从0开始
	uint32 num=3;   // 数量
	uint32 ba=4; //方向 0=正序 1=倒序
	uint32 type=5; //类型 0=委托+成交 1=委托 2=成交(不包括撤单)
	uint64 sequence = 6;	//序号, 如果有序号，则从序号开始偏移，如果序号为0，则按offset偏移
}

message lv2OrderTick_ans
{
	uint64 nkey = 1;
	repeated lv2OrderTick data = 2;
}

message lv2SigOrderTick_req
{
	tagCodeWithNkey code=1;
	uint64 buy_order_no = 2; //买方委托编号
	uint64 sell_order_no = 3; //卖方委托编号
}

message lv2Mulpk_req
{
	tagCodeWithNkey code=1;
}

message lv2Mulpk_data
{
	int64 price = 1; //价格 = 实际价格 * 1000
	int64 qty = 2; // 数量
	int32 numtrades = 3; // 笔数
	int32 nummore500 = 4; //大于500手的笔数
}

message lv2Mulpk_ans
{
	uint64 nkey = 1;
	repeated lv2Mulpk_data data_buy = 2;
	repeated lv2Mulpk_data data_sell = 3;
}

message lv2Sigpk_req
{
	tagCodeWithNkey code=1;
	uint32 bs = 2; // 0=买盘 1=卖盘
	int64 price = 3; //价格 = 实际价格 * 1000
}

message lv2Sigpk_ans
{
	uint64 nkey = 1;
	uint32 bs = 2; // 0=买盘 1=卖盘
	int64 price = 3; //价格 = 实际价格 * 1000
	repeated int64 qty = 4;
}

message lv2Kcbph_req
{
	tagCodeWithNkey code=1;
}

message mulstk_aday_req
{
	repeated uint64 nkey = 1;	//证券nkey
	uint64 jcltime = 2;			//K线时间YYYYMMDDHHMMSSsss
	int32 period = 3;			//周期	
}

message mulstk_aday_data
{
	uint64 nkey = 1;
	AnalyDataB ana = 2;
}

message mulstk_aday_ans
{
	repeated mulstk_aday_data data = 1;
}

//排序请求结构，带过滤品种逻辑
message sort_filter_req
{
	uint32 oldreq = 1; // 对应的旧协议,比如2015，2017等
	uint32 setcode = 2; // 市场编码
	uint32 setDomain = 3; // 排序类别
	uint32 coltype = 4; // 排序字段
	uint32 startxh = 5; // 其实序号
	uint32 wantnum = 6; // 请求数量
	uint32 sorttype = 7; // 升序降序
	tagCodeWithNkey code = 8; // 板块或者指数品种
	repeated uint64 keys = 9; // 品种nkey
	repeated string szkeys = 10; // nkey(字符串)
	repeated uint32 fieldids = 11; // 附加字段
	uint32 filter = 12;  // 过滤 0-不过滤 1-过滤创业板 2-过滤科创板 4-过滤北交所 8-过滤st 16-过滤退市股 多个过滤相与处理
}

// 板块积分查询
message block_score_data
{
	tagCodeWithNkey code = 1;
	int32				score =2;

}
message block_score_ans
{
	repeated block_score_data data = 1;
}

message jtjb_data
{
	uint64      nkey =1;
	uint64      jtjb = 2;
}
message jtjb_data_ans
{
	repeated jtjb_data data = 1;
}

// 因子统计应答数据
message factor_data
{
	int32 type = 1;		// 因子类型
	double value = 2;	// 数值
}

// 因子统计请求
message statics_factor_req
{
	repeated int32 type = 1;	// 查询的因子类型
}

// 因子统计应答
message statics_factor_ans
{
	repeated factor_data data = 1;
}

message lbdata
{
	int32 lb   = 1;			// 连板
	int32 stock_counts = 2;	// 连板个股数量
}

message hislb_stock
{
	tagCodeWithNkey	code =1;	// 代码
	bool    isLb = 2;		// 连板标记
}

message his_lb_date_line
{
	int32 date = 1;								// 日期
	repeated hislb_stock lb_line_stocks =2;	// 本行数据的 股票数
	repeated lbdata lb_line_counts = 3; // 本行数据的 连板数个股数量
}

// 连板天梯请求
message hislbdata_req
{
	int32 startDate = 1;	// 起始日期
	int32 endDate   = 2;	// 结束日期
}

message hislbdata_ans
{
	repeated his_lb_date_line lb_line_datas = 1;	// 连板天梯每行的数据
}

// 板块涨停家数统计
message static_blocks_zt
{
	uint64 nkey = 1;	// 板块 NKEY
	int32  setDomain=2;	// setdomain
	int32  nums = 3;	// 板块成分股涨停家数
}

// 板块涨停家数统计应答
message statics_block_zt_ans
{
	repeated static_blocks_zt data =1;
}

// 热门板块排序请求
message hot_block_sort_req
{
//	uint32 setdomain =1;
	uint32 coltype = 1;
	uint32 sorttype = 2;
	uint32 startxh=3;
	uint32 wantnum=4;
	repeated	uint32 fieldids=5;
};

// 热门板块排序应答
message hot_block_sort_ans
{
	uint32 total=1;
	uint32 count=2;
	repeated	uint32 fieldids=3;	//直接告知客户端，避免重新查询ID
	repeated	hq_with_fields fields=4; //直接告知客户端，避免重新查询ID
}

//STATICTISDATA_JJYZB = 23047;			// 竞价一字板统计
message jjyzb_stock
{
	tagCodeWithNkey code = 1;	// stock
	double          zanf = 2;  // 涨幅
}

message jjyzb_stocks_ans
{
	repeated jjyzb_stock stocks = 1;

}

//  STATICTISDATA_LBSBDB_STOCKS_REQ = 23049; // 获取昨日连板,昨日首板 昨日短板股票
message lbsbdb_stocks
{
	int32 type = 1; // 1-首板  2-连板 3-段板
	repeated uint64 nkey= 2;
}
message staticsdata_lbdbsb_stocks_req
{
	int32 type = 1;	// 0-所有 1-首板  2-连板 3-段板
}

message	staticsdata_lbdbsb_stocks_ans
{
	repeated lbsbdb_stocks  stocks = 1;	
}

// 获取涨停股票的连板天数和封板资金
message staticsdata_ztlb_stockinfo_req
{
	tagCodeWithNkey code = 1;
}
message	staticsdata_ztlb_stockinfo_ans
{
	tagCodeWithNkey code = 1;
	int32	lbnums		 = 2;	 //连板天数
	double    money		 = 3;	 //封单额
}

message rank_data
{
	uint64 nkey = 1; // 品种nkey
	uint32 rank = 2; // 排名
}

message rank_arr
{
	uint32 id = 1; //排名id
	repeated rank_data data = 2; //排名数据
}