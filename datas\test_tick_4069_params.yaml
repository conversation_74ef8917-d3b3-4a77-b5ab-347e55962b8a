# Tick数据测试参数配置 (4069)
# 格式: [code, setcode, startxh, num]

kline_test_params:
  # 基础测试场景
  basic_tests:
    - name: "SH-600000-start0-200"
      params: ["600000", 1, 0, 200]
      description: "浦发银行-从头开始-200条"

  # 科创板测试
  star_board_tests:
    - name: "STAR-688001-start0-10"
      params: ["688001", 1, 0, 10]
      description: "科创板-华兴源创-从头开始-10条"

  # 大偏移量测试
  large_offset_tests:
    - name: "SH-600519-start1000-1"
      params: ["600519", 1, 1000, 1]
      description: "贵州茅台-偏移1000-1条"
    - name: "SH-601318-start2000-1000"
      params: ["601318", 1, 2000, 1000]
      description: "中国平安-偏移2000-1000条"

# 测试字段配置
test_fields:
  quote_time_fields: ["quoteTime"]
  price_volume_fields: ["price", "vol"]
  flag_fields: ["flag"]

# 测试配置
test_config:
  method_name: 'get_tick_data'
  response_attribute: 'ticks'
  quote_time_length: 10
  decimal_places: 2
  
# 字段处理配置
field_processing:
  # quoteTime字段特殊处理：截取指定长度
  quote_time_length: 10
  # price和vol字段保留2位小数
  decimal_places: 2
  # flag字段精确比较
  exact_match_fields: ["flag"]
  
# 接口说明
interface_info:
  description: "4069 Tick数据接口"
  msg_id: 4069
  parameters: "code, setcode, startxh, num"
  response_structure: "ticks 数组"
  notes: "quoteTime字段截取10位进行比较，price和vol字段保留2位小数"
