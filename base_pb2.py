# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: base.proto
# Protobuf Python Version: 5.29.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    2,
    '',
    'base.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\nbase.proto\x12\x10wuhan.jcl.report\"}\n\x08\x42\x61seHead\x12\r\n\x05MsgID\x18\x01 \x01(\x05\x12\r\n\x05ReqID\x18\x02 \x01(\x04\x12\x11\n\tProtoType\x18\x03 \x01(\x05\x12\x14\n\x0c\x43ompressType\x18\x04 \x01(\x05\x12\x18\n\x10OriginBodyLength\x18\x05 \x01(\x05\x12\x10\n\x08\x43heckSum\x18\x06 \x01(\r\"D\n\x07\x42\x61seMsg\x12(\n\x04Head\x18\x01 \x01(\x0b\x32\x1a.wuhan.jcl.report.BaseHead\x12\x0f\n\x07MsgData\x18\x02 \x01(\x0c\"J\n\x0bRequestAuth\x12\r\n\x05Token\x18\x01 \x01(\t\x12\x0f\n\x07\x41ppName\x18\x02 \x01(\t\x12\x0e\n\x06\x41ppVer\x18\x03 \x01(\t\x12\x0b\n\x03Tag\x18\x04 \x01(\t\"/\n\x0cResponseAuth\x12\x12\n\nAuthResult\x18\x01 \x01(\x05\x12\x0b\n\x03Tag\x18\x02 \x01(\t*\x87\x12\n\tEnumMsgID\x12\x1f\n\x1bMsg_Rsp_DefaultErrorMessage\x10\x00\x12\x10\n\x0cMsg_Req_Auth\x10\x01\x12\x15\n\x11Msg_Req_Heartbeat\x10\t\x12\x14\n\x10Msg_Req_AutoGbbq\x10\x16\x12\x14\n\x10Msg_Req_AutoBase\x10\x17\x12\x14\n\x10Msg_Req_HostMore\x10\x1f\x12\x12\n\rMsg_Req_HqSub\x10\x86\x01\x12\x14\n\x0fMsg_Req_HqUnsub\x10\x87\x01\x12\x15\n\x10Msg_Req_StrHqSub\x10\x90\x01\x12\x17\n\x12Msg_Req_StrHqUnSub\x10\x91\x01\x12\x14\n\x0fMsg_Req_DxjlSub\x10\x96\x01\x12\x16\n\x11Msg_Req_DxjlUnSub\x10\x97\x01\x12\x17\n\x12Msg_Req_StaticCode\x10\xd6\x08\x12\x19\n\x14Msg_Req_StaticCodeEn\x10\xd7\x08\x12\x15\n\x10Msg_Req_UsedName\x10\xe0\x08\x12\x13\n\x0eMsg_Req_ZhSort\x10\xb5\t\x12\x13\n\x0eMsg_Req_SortHq\x10\xbf\r\x12\x14\n\x0fMsg_Req_MultiHq\x10\xc0\r\x12\x17\n\x12Msg_Req_SortNoHqEx\x10\xe5\x0f\x12\x15\n\x10Msg_Req_SortHqEx\x10\xe7\x0f\x12\x17\n\x11Msg_Req_MultiHqXg\x10\xec\xac\x01\x12\x18\n\x13Msg_Req_BlockSortHq\x10\xd8\x0f\x12\x15\n\x10Msg_Req_SortExHq\x10\xdf\x0f\x12\x15\n\x10Msg_Req_DgtlCode\x10\xe0\x0f\x12\x11\n\x0cMsg_Req_HqEx\x10\xe1\x0f\x12\x1e\n\x19Msg_Req_StockBlockInfoSet\x10\xe3\x0f\x12\x16\n\x11Msg_Req_MoneyFlow\x10\x8d\x0e\x12\x1a\n\x15Msg_Req_MoneyFlowMin1\x10\x8f\x0e\x12\x16\n\x11Msg_Req_MoneyRank\x10\xe8\x0f\x12\x18\n\x13Msg_Req_AuctionData\x10\xadN\x12\x1a\n\x15Msg_Req_AfterTradeKCB\x10\xb0N\x12\x17\n\x12Msg_Req_AfterTrade\x10\xb4N\x12\x19\n\x14Msg_Req_BlockXmlFile\x10\xd0\n\x12\x19\n\x14Msg_Req_BlockXmlData\x10\xa0j\x12\x19\n\x14Msg_Req_ZsCfgXmlData\x10\xa2j\x12\x18\n\x12Msg_Req_ZdfSection\x10\xa1\x9c\x01\x12\x17\n\x11Msg_Req_ZdfResult\x10\xa2\x9c\x01\x12\x17\n\x11Msg_Req_ZdfProfit\x10\xa3\x9c\x01\x12\x17\n\x11Msg_Req_ZdfZdtNum\x10\xa4\x9c\x01\x12\x1a\n\x14Msg_Req_FyzbZtResult\x10\xa5\x9c\x01\x12\x17\n\x11Msg_Req_HqZdtData\x10\xa6\x9c\x01\x12 \n\x1aMsg_Req_StrategyCenterList\x10\x84\x9d\x01\x12\x1e\n\x18Msg_Req_StrategyBsProfit\x10\x85\x9d\x01\x12\x1f\n\x19Msg_Req_StrategyBsSuccess\x10\x86\x9d\x01\x12$\n\x1eMsg_Req_StrategyCenterListJRTC\x10\x8f\x9d\x01\x12$\n\x1eMsg_Req_StrategyCenterListDQCC\x10\x90\x9d\x01\x12$\n\x1eMsg_Req_StrategyCenterListCLNG\x10\x91\x9d\x01\x12\x1f\n\x19Msg_Req_StrategyHisSignal\x10\x92\x9d\x01\x12\x1c\n\x16Msg_Req_StrategyOneKey\x10\x88\x9d\x01\x12\"\n\x1cMsg_Req_StrategyOnekeyProfit\x10\x89\x9d\x01\x12\"\n\x1cMsg_Req_StrategyOnekeyResult\x10\x8a\x9d\x01\x12\x19\n\x13Msg_Req_BigDataCXQN\x10\x8c\x9d\x01\x12\x18\n\x12Msg_ReqBigDataJHJJ\x10\x8d\x9d\x01\x12\x19\n\x13Msg_Req_BigDataZTZY\x10\x8e\x9d\x01\x12\x1e\n\x18Msg_Req_StrategyMMOnekey\x10\x97\x9d\x01\x12\x1a\n\x14Msg_Req_StrategyList\x10\xae\x9d\x01\x12\x1a\n\x14Msg_Req_StrategyDesc\x10\xb1\x9d\x01\x12 \n\x1aMsg_Req_StrategySystemPool\x10\xc0\x9d\x01\x12&\n Msg_Req_StrategySystemPoolResult\x10\xc1\x9d\x01\x12\x12\n\x0cMsg_Req_Dxjl\x10\xb0\x9f\x01\x12\x12\n\x0cMsg_Req_Bkyd\x10\xb5\x9f\x01\x12\x13\n\x0eMsg_Req_MinApp\x10\xd9\n\x12\x15\n\x10Msg_Req_KlineApp\x10\xda\n\x12\x16\n\x10Msg_Req_KlineNew\x10\xc2\xad\x01\x12\x1a\n\x14Msg_Req_KlineTimeNew\x10\xfb\xab\x01\x12\x17\n\x11Msg_Req_MinuteNew\x10\x84\xac\x01\x12\x1a\n\x14Msg_Req_HisMinuteNew\x10\x85\xac\x01\x12\x14\n\x0fMsg_Req_TickApp\x10\xe5\x1f\x12\x15\n\x0fMsg_Req_TickNew\x10\x86\xac\x01\x12\x18\n\x12Msg_Req_TickHisNew\x10\x87\xac\x01\x12\x11\n\x0bMsg_Req_Fjb\x10\x88\xac\x01\x12\x1b\n\x15Msg_Req_StaticsExData\x10\xd6\xb3\x01\x12\x14\n\x0eMsg_Req_LbData\x10\x81\xb4\x01\x12\x1c\n\x16Msg_Req_MoneyFlowStat1\x10\xa1\xb5\x01\x12\x1c\n\x16Msg_Req_MoneyFlowStat2\x10\xa2\xb5\x01\x12 \n\x1aMsg_Req_MoneyFlowStatBatch\x10\xa3\xb5\x01\x12\x15\n\x0fMsg_Req_JsonReq\x10\xb0\xea\x01\x12\x17\n\x11Msg_Req_BfdayData\x10\x94\xeb\x01\x12\x18\n\x12Msg_Req_KlineLabel\x10\x95\xeb\x01\x12\x1d\n\x17Msg_Req_KlineLabelBatch\x10\x96\xeb\x01\x12\x19\n\x13Msg_Req_L2OrderTick\x10\xdc\xec\x01\x12\x1c\n\x16Msg_Req_L2SigOrderTick\x10\xdd\xec\x01\x12\x15\n\x0fMsg_Req_L2Mulpk\x10\xde\xec\x01\x12\x15\n\x0fMsg_Req_L2Sigpk\x10\xdf\xec\x01\x12\x19\n\x13Msg_Req_L2KcbPhTick\x10\xe0\xec\x01\x12\x16\n\x10Msg_Req_FdHqStat\x10\xb1\x9f\x01\x12\x18\n\x13Msg_Req_ValueAddMin\x10\xa9\x46\x42\x08Z\x06/quoteb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'base_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z\006/quote'
  _globals['_ENUMMSGID']._serialized_start=355
  _globals['_ENUMMSGID']._serialized_end=2666
  _globals['_BASEHEAD']._serialized_start=32
  _globals['_BASEHEAD']._serialized_end=157
  _globals['_BASEMSG']._serialized_start=159
  _globals['_BASEMSG']._serialized_end=227
  _globals['_REQUESTAUTH']._serialized_start=229
  _globals['_REQUESTAUTH']._serialized_end=303
  _globals['_RESPONSEAUTH']._serialized_start=305
  _globals['_RESPONSEAUTH']._serialized_end=352
# @@protoc_insertion_point(module_scope)
