"""统一断言模块"""

from typing import List, Dict, Any, Optional, Union
from config import ASSERT_CONFIG
from common.test_utils import safe_field_comparison, batch_field_comparison


class UnifiedAssert:
    """统一断言类"""

    def __init__(self, tolerance: float = None, decimal_places: int = None):
        """初始化断言器"""
        self.tolerance = tolerance or ASSERT_CONFIG["tolerance"]
        self.decimal_places = decimal_places or ASSERT_CONFIG["decimal_places"]
        self.integer_fields = ASSERT_CONFIG["integer_fields"]
        self.truncate_fields = ASSERT_CONFIG["truncate_fields"]
        self.special_fieldids = ASSERT_CONFIG["special_fieldids"]
    
    def assert_responses(self, old_response: Any, new_response: Any, fields: List[str],
                        assert_type: str = "equal", request_params: Optional[Dict] = None,
                        server_info: Optional[Dict] = None, logger = None,
                        response_times: Optional[Dict] = None, **kwargs):
        """统一的响应断言方法"""
        errors = []
        
        try:
            if assert_type == "equal":
                errors = self._assert_equal(old_response, new_response, fields, **kwargs)
            elif assert_type == "fieldids":
                errors = self._assert_fieldids(old_response, new_response, fields, **kwargs)
            elif assert_type == "round":
                errors = self._assert_round(old_response, new_response, fields, **kwargs)
            elif assert_type == "batch":
                errors = self._assert_batch(old_response, new_response, fields, **kwargs)
            else:
                raise ValueError(f"不支持的断言类型: {assert_type}")
        
        except Exception as e:
            errors.append(f"断言执行异常: {str(e)}")
        
        # 记录日志
        if logger:
            logger.log_test_result(
                server_info=server_info or {},
                response_times=response_times or {},
                request_params=request_params or {},
                has_errors=bool(errors),
                error_details=errors
            )
        
        # 如果有错误，抛出断言异常
        if errors:
            error_count = len(errors)
            combined_error = "\n".join([f"共发现 {error_count} 处断言失败:"] + errors)
            raise AssertionError(combined_error)
    
    def _assert_equal(
        self, 
        old_response: Any, 
        new_response: Any, 
        fields: List[str],
        **kwargs
    ) -> List[str]:
        """基础相等断言"""
        errors = []
        
        for field in fields:
            try:
                old_val = getattr(old_response, field, None)
                new_val = getattr(new_response, field, None)
                
                # 特殊字段处理
                if field in self.integer_fields:
                    old_val = self._to_integer(old_val)
                    new_val = self._to_integer(new_val)
                elif field in self.truncate_fields:
                    length = self.truncate_fields[field]
                    old_val = str(old_val)[:length] if old_val is not None else None
                    new_val = str(new_val)[:length] if new_val is not None else None
                
                equal, error = safe_field_comparison(old_val, new_val, field, self.tolerance)
                if not equal:
                    errors.append(f"- {error}")
                    
            except Exception as e:
                errors.append(f"- {field}: 比较时发生异常 ({str(e)})")
        
        return errors
    
    def _assert_fieldids(
        self, 
        old_response: Any, 
        new_response: Any, 
        fields: List[str],
        **kwargs
    ) -> List[str]:
        """字段ID特殊断言（用于排序接口）"""
        errors = []
        
        for field in fields:
            try:
                old_val = getattr(old_response, field, None)
                new_val = getattr(new_response, field, None)
                
                # 对于特殊字段ID，使用比例比较
                if hasattr(old_response, 'fieldid') and old_response.fieldid in self.special_fieldids:
                    if old_val is not None and new_val is not None and old_val != 0:
                        ratio = abs(new_val / old_val)
                        if not (0.999999 <= ratio <= 1.000001) and abs(new_val - old_val) >= 0.1:
                            errors.append(f"- {field}: 新接口={new_val}, 基准接口={old_val}")
                    elif old_val != new_val:
                        errors.append(f"- {field}: 新接口={new_val}, 基准接口={old_val}")
                else:
                    # 普通字段比较
                    equal, error = safe_field_comparison(old_val, new_val, field, self.tolerance)
                    if not equal:
                        errors.append(f"- {error}")
                        
            except Exception as e:
                errors.append(f"- {field}: 比较时发生异常 ({str(e)})")
        
        return errors
    
    def _assert_round(
        self, 
        old_response: Any, 
        new_response: Any, 
        fields: List[str],
        decimal_places: int = None,
        **kwargs
    ) -> List[str]:
        """四舍五入断言"""
        errors = []
        decimal_places = decimal_places or self.decimal_places
        
        for field in fields:
            try:
                old_val = getattr(old_response, field, None)
                new_val = getattr(new_response, field, None)
                
                # 对数值进行四舍五入处理
                if isinstance(old_val, (int, float)) and isinstance(new_val, (int, float)):
                    old_rounded = round(old_val, decimal_places)
                    new_rounded = round(new_val, decimal_places)
                    
                    if old_rounded != new_rounded:
                        errors.append(f"- {field}: 新接口(rounded)={new_rounded}, 老接口(rounded)={old_rounded}")
                else:
                    # 非数值类型直接比较
                    equal, error = safe_field_comparison(old_val, new_val, field, self.tolerance)
                    if not equal:
                        errors.append(f"- {error}")
                        
            except Exception as e:
                errors.append(f"- {field}: 比较时发生异常 ({str(e)})")
        
        return errors
    
    def _assert_batch(
        self,
        old_response: Any,
        new_response: Any,
        fields: List[str],
        response_attr: str = "ahq",
        **kwargs
    ) -> List[str]:
        """批量断言（用于多条记录的响应）"""
        errors = []

        try:
            old_items = getattr(old_response, response_attr, [])
            new_items = getattr(new_response, response_attr, [])

            # 检查数据长度
            if len(old_items) != len(new_items):
                errors.append(f"数据条数不一致: 旧接口={len(old_items)}, 新接口={len(new_items)}")
                return errors

            # 限制比较数量，避免性能问题
            max_compare = min(100, len(old_items))
            if max_compare > 0:
                # 对于K线数据等，使用更精确的比较
                for i in range(max_compare):
                    old_item = old_items[i]
                    new_item = new_items[i]

                    for field in fields:
                        try:
                            old_val = getattr(old_item, field, None)
                            new_val = getattr(new_item, field, None)

                            # 特殊处理QuoteTime字段，只比较前12位
                            if field == "QuoteTime":
                                try:
                                    # 转换为字符串并只取前12位进行比较
                                    old_str = str(old_val)[:12] if old_val is not None else ""
                                    new_str = str(new_val)[:12] if new_val is not None else ""

                                    if old_str != new_str:
                                        errors.append(f"第{i+1}条记录 - {field}: 前12位不相等 (新接口(前12位)={new_str}, 老接口(前12位)={old_str})")
                                except Exception as e:
                                    errors.append(f"第{i+1}条记录 - {field}: QuoteTime比较时发生异常 ({str(e)})")
                            # 特殊处理数值字段，使用四舍五入比较
                            elif isinstance(old_val, (int, float)) and isinstance(new_val, (int, float)):
                                old_rounded = round(old_val, self.decimal_places)
                                new_rounded = round(new_val, self.decimal_places)

                                if old_rounded != new_rounded:
                                    errors.append(f"第{i+1}条记录 - {field}: 新接口(rounded)={new_rounded}, 老接口(rounded)={old_rounded}")
                            else:
                                # 非数值类型直接比较
                                equal, error = safe_field_comparison(old_val, new_val, field, self.tolerance)
                                if not equal:
                                    errors.append(f"第{i+1}条记录 - {error}")

                        except Exception as e:
                            errors.append(f"第{i+1}条记录 - {field}: 比较时发生异常 ({str(e)})")

        except Exception as e:
            errors.append(f"批量比较时发生异常: {str(e)}")

        return errors
    
    def _to_integer(self, value: Any) -> Optional[int]:
        """安全转换为整数"""
        if value is None:
            return None
        try:
            return int(float(value))
        except (ValueError, TypeError):
            return value


# 创建全局断言器实例
unified_assert = UnifiedAssert()


# 提供简化的函数接口，保持向后兼容
def assert_unified(
    old_response: Any,
    new_response: Any,
    fields: List[str],
    assert_type: str = "equal",
    request_params: Optional[Dict] = None,
    server_info: Optional[Dict] = None,
    logger = None,
    response_times: Optional[Dict] = None,
    **kwargs
):
    """
    统一断言函数，简化的接口
    
    Args:
        old_response: 旧接口响应
        new_response: 新接口响应
        fields: 要比较的字段列表
        assert_type: 断言类型
        request_params: 请求参数
        server_info: 服务器信息
        logger: 日志记录器
        response_times: 响应时间
        **kwargs: 额外参数
    """
    unified_assert.assert_responses(
        old_response, new_response, fields, assert_type,
        request_params, server_info, logger, response_times, **kwargs
    )


# 提供特定类型的简化函数
def assert_hq_response(old_response, new_response, fields, **kwargs):
    """行情响应断言"""
    return assert_unified(old_response, new_response, fields, "batch", **kwargs)


def assert_sort_response(old_response, new_response, fields, **kwargs):
    """排序响应断言"""
    return assert_unified(old_response, new_response, fields, "fieldids", **kwargs)


def assert_kline_response(old_response, new_response, fields, **kwargs):
    """K线响应断言"""
    return assert_unified(old_response, new_response, fields, "round", **kwargs)
