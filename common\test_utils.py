"""测试工具模块"""

from typing import List, Dict, Any, <PERSON><PERSON>


def create_stock_codes(sz_codes: List[str] = None, sh_codes: List[str] = None,
                      bj_codes: List[str] = None, indices: List[str] = None) -> List[Dict[str, Any]]:
    """创建股票代码列表"""
    codes = []
    code_mappings = [(sz_codes, 0), (sh_codes, 1), (bj_codes, 2), (indices, 0)]
    for code_list, setcode in code_mappings:
        if code_list:
            codes.extend([{"nkey": 0, "setcode": setcode, "code": code} for code in code_list])
    return codes


def create_futures_codes(
    cffex_codes: List[str] = None,
    shfe_codes: List[str] = None,
    dce_codes: List[str] = None,
    czce_codes: List[str] = None
) -> List[Dict[str, Any]]:
    """
    创建期货代码列表

    Args:
        cffex_codes: 中金所期货代码列表 (股指期货)
        shfe_codes: 上期所期货代码列表
        dce_codes: 大商所期货代码列表
        czce_codes: 郑商所期货代码列表

    Returns:
        格式化的期货代码列表
    """
    codes = []

    # 中金所期货 (setcode=3) - 股指期货
    if cffex_codes:
        for code in cffex_codes:
            codes.append({"nkey": 0, "setcode": 3, "code": code})

    # 上期所期货 (setcode=4)
    if shfe_codes:
        for code in shfe_codes:
            codes.append({"nkey": 0, "setcode": 4, "code": code})

    # 大商所期货 (setcode=5)
    if dce_codes:
        for code in dce_codes:
            codes.append({"nkey": 0, "setcode": 5, "code": code})

    # 郑商所期货 (setcode=6)
    if czce_codes:
        for code in czce_codes:
            codes.append({"nkey": 0, "setcode": 6, "code": code})

    return codes


def create_mixed_codes(
    sz_codes: List[str] = None,
    sh_codes: List[str] = None,
    bj_codes: List[str] = None,
    indices: List[str] = None,
    cffex_codes: List[str] = None,
    shfe_codes: List[str] = None,
    dce_codes: List[str] = None,
    czce_codes: List[str] = None
) -> List[Dict[str, Any]]:
    """
    创建混合代码列表（股票+期货）

    Args:
        sz_codes: 深市股票代码列表
        sh_codes: 沪市股票代码列表
        bj_codes: 北交所股票代码列表
        indices: 指数代码列表
        cffex_codes: 中金所期货代码列表
        shfe_codes: 上期所期货代码列表
        dce_codes: 大商所期货代码列表
        czce_codes: 郑商所期货代码列表

    Returns:
        格式化的混合代码列表
    """
    codes = []

    # 添加股票代码
    stock_codes = create_stock_codes(sz_codes, sh_codes, bj_codes, indices)
    codes.extend(stock_codes)

    # 添加期货代码
    futures_codes = create_futures_codes(cffex_codes, shfe_codes, dce_codes, czce_codes)
    codes.extend(futures_codes)

    return codes


def get_common_test_codes() -> List[List[Dict[str, Any]]]:
    """
    获取常用的测试股票代码组合

    Returns:
        测试代码组合列表
    """
    # 提供默认的股票和期货配置
    common_stocks = {
        "sz_stocks": ["000001", "300115", "300001", "002490"],
        "sh_stocks": ["600000", "601318", "688001", "603259"],
        "bj_stocks": ["830001", "830002"],
        "indices": ["399001", "399006", "000001"]
    }
    common_futures = {
        "cffex_futures": ["IC2509", "ICL8", "ICL9", "IF2509", "IH2509"],
        "shfe_futures": ["cu2509", "au2509", "ag2509"],
        "dce_futures": ["i2509", "j2509", "jm2509"],
        "czce_futures": ["MA509", "TA509", "CF509"]
    }

    test_combinations = []

    # 单个股票测试
    for code in common_stocks["sz_stocks"][:2]:  # 取前2个深市股票
        test_combinations.append(create_stock_codes(sz_codes=[code]))

    for code in common_stocks["sh_stocks"][:2]:  # 取前2个沪市股票
        test_combinations.append(create_stock_codes(sh_codes=[code]))

    # 单个期货测试
    if common_futures.get("cffex_futures"):
        for code in common_futures["cffex_futures"][:2]:  # 取前2个中金所期货
            test_combinations.append(create_futures_codes(cffex_codes=[code]))

    # 混合测试
    test_combinations.extend([
        # 深沪混合
        create_stock_codes(
            sz_codes=["000001"],
            sh_codes=["600000"]
        ),
        # 包含北交所
        create_stock_codes(
            sz_codes=["000001"],
            sh_codes=["600000"],
            bj_codes=["830001"]
        ),
        # 多股票组合
        create_stock_codes(
            sz_codes=["000001", "300115"],
            sh_codes=["600000", "601318"],
            indices=["399006"]
        )
    ])

    # 如果有期货配置，添加股票+期货混合测试
    if common_futures.get("cffex_futures"):
        test_combinations.extend([
            # 股票+期货混合
            create_mixed_codes(
                sz_codes=["000001"],
                cffex_codes=["IC2509"]
            ),
            # 多市场混合
            create_mixed_codes(
                sz_codes=["000001"],
                sh_codes=["600000"],
                cffex_codes=["IC2509", "IF2509"]
            )
        ])

    return test_combinations


def generate_kline_test_params() -> List[Tuple]:
    """
    生成K线测试参数组合
    
    Returns:
        K线测试参数元组列表
    """
    # 基础参数组合
    base_params = [
        # (code, setcode, period, mulnum, offset, num, tqflag, exhq)
        ("000001", 0, 1, 1, 0, 10, 0, 0),  # 深市日K
        ("600000", 1, 1, 1, 0, 10, 0, 0),  # 沪市日K
        ("000001", 0, 4, 1, 0, 5, 0, 0),   # 深市分时
        ("600000", 1, 2, 1, 0, 20, 1, 0),  # 沪市周K，前复权
        ("300115", 0, 7, 1, 0, 5, 0, 1),   # 创业板，包含扩展行情
    ]
    
    return base_params


def generate_sort_test_params() -> List[Tuple]:
    """
    生成排序测试参数组合
    
    Returns:
        排序测试参数元组列表
    """
    # 基础排序参数
    sort_params = [
        # (setDomain, coltype, startxh, wantnum, sorttype, drate, fieldids)
        (0, 14, 0, 10, 1, 0, [1, 2, 3, 14]),  # 深市基础排序
        (1, 13, 0, 20, 2, 0, [13, 9, 10, 11]),  # 沪市价格排序
        (0, 24, 10, 10, 2, 0, [13, 24, 26]),  # 成交量排序，分页
        (10, 23, 0, 15, 1, 0, [13, 23, 24]),  # 创业板市盈率排序
        (11, 26, 0, 5, 2, 0, [13, 26, 24]),   # 科创板成交额排序
    ]
    
    return sort_params


def create_test_params_dict(**kwargs) -> Dict[str, Any]:
    """
    创建测试参数字典，过滤None值
    
    Args:
        **kwargs: 参数键值对
        
    Returns:
        过滤后的参数字典
    """
    return {k: v for k, v in kwargs.items() if v is not None}


def get_field_list(field_type: str, exclude: List[str] = None) -> List[str]:
    """
    获取指定类型的字段列表
    
    Args:
        field_type: 字段类型 (hq_fields, kline_fields等)
        exclude: 要排除的字段列表
        
    Returns:
        字段列表
    """
    fields = FIELD_CONFIG.get(field_type, [])
    if exclude:
        fields = [f for f in fields if f not in exclude]
    return fields


def format_test_error(errors: List[str]) -> str:
    """
    格式化测试错误信息
    
    Args:
        errors: 错误信息列表
        
    Returns:
        格式化的错误信息
    """
    if not errors:
        return ""
    
    error_count = len(errors)
    formatted_errors = [f"共发现 {error_count} 处错误:"] + errors
    return "\n".join(formatted_errors)


def safe_field_comparison(
    old_val: Any, 
    new_val: Any, 
    field_name: str,
    tolerance: float = 0.000001
) -> Tuple[bool, str]:
    """
    安全的字段比较，处理各种数据类型
    
    Args:
        old_val: 旧值
        new_val: 新值
        field_name: 字段名
        tolerance: 数值比较容差
        
    Returns:
        (是否相等, 错误信息)
    """
    try:
        # 处理None值
        if old_val is None and new_val is None:
            return True, ""
        if old_val is None or new_val is None:
            return False, f"{field_name}: 一个为None，另一个不为None"

        # 特殊处理QuoteTime字段，只比较前12位（优先级最高）
        if field_name == "QuoteTime":
            try:
                # 转换为字符串并只取前12位进行比较
                old_str = str(old_val)[:12] if old_val is not None else ""
                new_str = str(new_val)[:12] if new_val is not None else ""

                if old_str == new_str:
                    return True, ""
                else:
                    return False, f"{field_name}: 前12位不相等 (新接口(前12位)={new_str}, 老接口(前12位)={old_str})"
            except Exception as e:
                return False, f"{field_name}: QuoteTime比较时发生异常 ({str(e)})"

        # 处理数值类型
        if isinstance(old_val, (int, float)) and isinstance(new_val, (int, float)):
            if abs(old_val - new_val) <= tolerance:
                return True, ""
            else:
                return False, f"{field_name}: 数值差异超过容差 ({old_val} vs {new_val})"
        
        # 处理列表类型
        if isinstance(old_val, (list, tuple)) and isinstance(new_val, (list, tuple)):
            if len(old_val) != len(new_val):
                return False, f"{field_name}: 列表长度不同 ({len(old_val)} vs {len(new_val)})"
            
            for i, (o, n) in enumerate(zip(old_val, new_val)):
                equal, error = safe_field_comparison(o, n, f"{field_name}[{i}]", tolerance)
                if not equal:
                    return False, error
            return True, ""
        
        # 处理字符串和其他类型
        if old_val == new_val:
            return True, ""
        else:
            return False, f"{field_name}: 值不相等 ({old_val} vs {new_val})"
            
    except Exception as e:
        return False, f"{field_name}: 比较时发生异常 ({str(e)})"


def batch_field_comparison(
    response_items: List[Any],
    new_response_items: List[Any], 
    fields: List[str],
    tolerance: float = 0.000001
) -> List[str]:
    """
    批量字段比较
    
    Args:
        response_items: 旧响应项目列表
        new_response_items: 新响应项目列表
        fields: 要比较的字段列表
        tolerance: 数值比较容差
        
    Returns:
        错误信息列表
    """
    errors = []
    
    if len(response_items) != len(new_response_items):
        errors.append(f"响应项目数量不匹配: {len(response_items)} vs {len(new_response_items)}")
        return errors
    
    for i, (old_item, new_item) in enumerate(zip(response_items, new_response_items)):
        for field in fields:
            try:
                old_val = getattr(old_item, field, None)
                new_val = getattr(new_item, field, None)
                
                equal, error = safe_field_comparison(old_val, new_val, field, tolerance)
                if not equal:
                    errors.append(f"第{i+1}条记录 - {error}")
                    
            except Exception as e:
                errors.append(f"第{i+1}条记录 - {field}: 获取字段值时发生异常 ({str(e)})")
    
    return errors
