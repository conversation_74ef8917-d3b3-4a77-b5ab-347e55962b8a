# 当日多品种资金流测试参数配置 (23203)
# 格式: [nkey] (nkey本身是一个列表)

kline_test_params:
  # 基础测试场景
  basic_tests:
    - name: "multi-today-basic"
      params: [[6582227395838651953]]
      description: "当日多品种资金流基础测试"

# 测试字段配置
test_fields:
  moneyflow_fields:
    - 'superIn'
    - 'superOut'
    - 'bigIn'
    - 'bigOut'
    - 'midIn'
    - 'midOut'
    - 'smallIn'
    - 'smallOut'
  
  time_fields:
    - 'jcltime'

# 测试配置
test_config:
  method_name: 'get_today_moneyflow'
  response_structure: 'data[0]'
  data_types: ['amt', 'vol', 'cnt']
  decimal_places: 2
  
# 字段处理配置
field_processing:
  # 资金流向字段保留2位小数
  decimal_places: 2
  # jcltime字段需要精确匹配
  time_exact_match: true
  comparison_mode: 'round'  # 使用四舍五入比较模式
  
# 接口说明
interface_info:
  description: "23203 当日多品种资金流接口"
  msg_id: 23203
  parameters: "nkey (列表格式)"
  response_structure: "data[0] 包含 amt, vol, cnt 三个数据集和 jcltime"
  notes: "jcltime字段需要精确匹配，资金流向字段保留2位小数进行比较"
