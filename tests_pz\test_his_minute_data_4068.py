import pytest
from common.assert_response import assert_response_equal, assert_round_response
from common.yaml_loader import load_test_params, load_test_config, get_param_ids


class TestHisMinuteData:
    """测试历史分钟线数据接口一致性"""

    @pytest.mark.parametrize(
        "code, setcode, date",
        load_test_params('test_his_minute_data_4068_params.yaml'),
        ids=get_param_ids('test_his_minute_data_4068_params.yaml')
    )
    def test_his_minute_data_4068(self, api_helper, code, setcode, date, logger):
        """测试历史分钟线接口(4068)，并校验响应时间和内容一致性（特定字段需忽略）"""
        # 加载配置
        config = load_test_config('test_his_minute_data_4068_params.yaml')
        test_fields = config['test_fields']
        test_config = config['test_config']

        # 创建参数字典用于日志记录
        params = {'code': code, 'setcode': setcode, 'date': date}

        print(f"请求参数：code={code}, setcode={setcode}, date={date}")
        logger.log_debug(f"请求参数：code={code}, setcode={setcode}, date={date}")

        # 并发调用新旧协议接口
        response, new_response = api_helper.call_method_concurrently(
            test_config['method_name'],
            code=code, setcode=setcode, date=date
        )

        # 获取响应时间和服务器信息
        response_times = api_helper.get_last_response_times()
        server_info = api_helper.get_last_server_info()

        # 执行断言测试
        all_errors = self._run_assertions(
            response, new_response, test_fields, test_config,
            params, logger
        )

        # 记录测试结果
        logger.log_test_result(
            server_info=server_info, response_times=response_times,
            request_params=params, has_errors=bool(all_errors), error_details=all_errors
        )

        # 如果有错误则抛出
        if all_errors:
            combined_error = "\n".join([f"共发现 {len(all_errors)} 处断言失败:"] + all_errors)
            raise AssertionError(combined_error)

    def _run_assertions(self, response, new_response, test_fields, test_config, params, logger):
        """执行断言测试"""
        all_errors = []
        response_attr = test_config['response_attribute']

        for resp_item, new_resp_item in zip(getattr(response, response_attr), getattr(new_response, response_attr)):

            # 浮点数字段测试
            try:
                assert_round_response(
                    resp_item, new_resp_item, test_fields['float_fields'],
                    decimal_places=test_config['decimal_places'], request_params=params
                )
            except Exception as e:
                all_errors.append(str(e))

            # 整数字段测试
            try:
                assert_response_equal(resp_item, new_resp_item, test_fields['int_fields'], request_params=params)
            except Exception as e:
                all_errors.append(str(e))

            # avePrice字段特殊处理
            try:
                self._assert_ave_price(resp_item, new_resp_item, test_config['ave_price_tolerance'], all_errors)
            except Exception as e:
                all_errors.append(str(e))

        return all_errors

    def _assert_ave_price(self, response, new_response, tolerance, all_errors):
        """处理avePrice字段的特殊断言（整数部分比较，允许容差）"""
        if hasattr(response, 'avePrice') and hasattr(new_response, 'avePrice'):
            old_ave_price_int = int(response.avePrice)
            new_ave_price_int = int(new_response.avePrice)
            price_diff = abs(old_ave_price_int - new_ave_price_int)
            # 允许配置的价格差异，这在实时数据中是合理的
            if price_diff > tolerance:
                all_errors.append(
                    f"avePrice字段(整数部分)差异过大: 基准接口={old_ave_price_int}, "
                    f"新接口={new_ave_price_int}, 差异={price_diff}, 允许容差={tolerance}"
                )
