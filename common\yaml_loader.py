"""YAML参数加载工具"""
import yaml
import os
from typing import List, Dict, Any


def _get_yaml_path(yaml_file: str) -> str:
    """获取YAML文件的完整路径"""
    # 尝试多个可能的路径
    possible_paths = [
        # 相对于项目根目录的datas路径
        os.path.join('datas', yaml_file),
        # 相对于当前文件的路径
        os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'datas', yaml_file),
        # 相对于工作目录的路径
        os.path.join('tests_pz', 'datas', yaml_file),
        # 旧的test_data路径（向后兼容）
        os.path.join('tests_pz', 'test_data', yaml_file),
        # 当前目录
        yaml_file
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    
    # 如果都找不到，返回第一个路径并让错误自然发生
    return possible_paths[0]


def load_test_params(yaml_file: str, return_format: str = 'tuple') -> List:
    """从YAML文件加载测试参数

    Args:
        yaml_file: YAML文件路径
        return_format: 返回格式 ('tuple' 或 'dict')

    Returns:
        参数列表（元组或字典格式）
    """
    # 获取YAML文件的完整路径
    yaml_path = _get_yaml_path(yaml_file)

    with open(yaml_path, 'r', encoding='utf-8') as f:
        data = yaml.safe_load(f)

    params = []
    kline_params = data.get('kline_test_params', {})

    # 合并所有参数组
    for group_name, group_params in kline_params.items():
        for item in group_params:
            if return_format == 'dict':
                # 返回字典格式（用于需要字典参数的测试）
                param_dict = {}
                param_list = item['params']
                # 根据参数数量确定键名
                if len(param_list) == 3:
                    param_dict = {'nkey': param_list[0], 'setcode': param_list[1], 'code': param_list[2]}
                elif len(param_list) == 4:
                    param_dict = {'code': param_list[0], 'setcode': param_list[1], 'nkey': param_list[2], 'type': param_list[3]}
                elif len(param_list) == 7:
                    param_dict = {'nkey': param_list[0], 'setcode': param_list[1], 'code': param_list[2],
                                 'period': param_list[3], 'offset': param_list[4], 'num': param_list[5], 'mulnum': param_list[6]}
                else:
                    # 默认转换为元组
                    param_dict = tuple(param_list)
                params.append(param_dict)
            else:
                # 返回元组格式（默认）
                param_tuple = tuple(item['params'])
                params.append(param_tuple)

    return params


def load_test_config(yaml_file: str) -> Dict[str, Any]:
    """从YAML文件加载测试配置
    
    Args:
        yaml_file: YAML文件路径
        
    Returns:
        配置字典
    """
    yaml_path = _get_yaml_path(yaml_file)
    
    with open(yaml_path, 'r', encoding='utf-8') as f:
        data = yaml.safe_load(f)
    
    return {
        'test_fields': data.get('test_fields', {}),
        'test_config': data.get('test_config', {})
    }


def get_param_ids(yaml_file: str) -> List[str]:
    """获取参数ID列表，用于pytest参数化的ids
    
    Args:
        yaml_file: YAML文件路径
        
    Returns:
        参数ID列表
    """
    yaml_path = _get_yaml_path(yaml_file)
    
    with open(yaml_path, 'r', encoding='utf-8') as f:
        data = yaml.safe_load(f)
    
    ids = []
    kline_params = data.get('kline_test_params', {})
    
    for group_name, group_params in kline_params.items():
        for item in group_params:
            ids.append(item['name'])
    
    return ids


def load_dict_params(yaml_file: str) -> List[Dict[str, Any]]:
    """从YAML文件加载字典格式的测试参数

    Args:
        yaml_file: YAML文件路径

    Returns:
        字典参数列表
    """
    return load_test_params(yaml_file, return_format='dict')


def load_nested_params(yaml_file: str) -> List[Any]:
    """从YAML文件加载嵌套格式的测试参数（用于复杂的参数结构）

    Args:
        yaml_file: YAML文件路径

    Returns:
        嵌套参数列表
    """
    # 获取YAML文件的完整路径
    yaml_path = _get_yaml_path(yaml_file)

    with open(yaml_path, 'r', encoding='utf-8') as f:
        data = yaml.safe_load(f)

    params = []
    kline_params = data.get('kline_test_params', {})

    # 合并所有参数组
    for group_name, group_params in kline_params.items():
        for item in group_params:
            # 直接返回params中的原始数据结构
            params.append(item['params'][0])  # 取第一个元素，因为YAML中是嵌套的

    return params
