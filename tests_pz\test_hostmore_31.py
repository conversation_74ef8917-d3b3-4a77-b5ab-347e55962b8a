import pytest
from common.assert_response import assert_response_equal
from common.yaml_loader import load_test_params, load_test_config, get_param_ids


class TestHostmoreAns:
    """测试主机配置接口一致性"""

    @pytest.mark.parametrize(
        "setcode, verflag",
        load_test_params('test_hostmore_31_params.yaml'),
        ids=get_param_ids('test_hostmore_31_params.yaml')
    )
    def test_hostmore_ans(self, api_helper, setcode, verflag, logger):
        """测试主机配置接口一致性"""
        # 加载配置
        config = load_test_config('test_hostmore_31_params.yaml')
        test_fields = config['test_fields']
        test_config = config['test_config']

        # 创建参数字典用于日志记录
        params = {'setcode': setcode, 'verflag': verflag}

        logger.log_debug(f'请求参数：setcode={setcode}, verflag={verflag}')
        print(f'请求参数：setcode={setcode}, verflag={verflag}')

        # 并发获取新旧协议接口响应（使用不同的方法名）
        response, new_response = api_helper.call_method_concurrently(
            None, setcode=setcode, verflag=verflag,
            old_method=test_config['old_method_name'],
            new_method=test_config['new_method_name']
        )

        # 获取响应时间和服务器信息
        response_times = api_helper.get_last_response_times()
        server_info = api_helper.get_last_server_info()

        # 执行断言测试
        all_errors = self._run_assertions(
            response, new_response, test_fields, test_config,
            params, logger
        )

        # 记录测试结果
        logger.log_test_result(
            server_info=server_info, response_times=response_times,
            request_params=params, has_errors=bool(all_errors), error_details=all_errors
        )

        # 如果有错误则抛出
        if all_errors:
            combined_error = "\n".join([f"共发现 {len(all_errors)} 处断言失败:"] + all_errors)
            raise AssertionError(combined_error)

    def _run_assertions(self, response, new_response, test_fields, test_config, params, logger):
        """执行断言测试"""
        all_errors = []
        response_attr = test_config['response_attribute']

        for resp_item, new_resp_item in zip(getattr(response, response_attr), getattr(new_response, response_attr)):
            try:
                assert_response_equal(resp_item, new_resp_item, test_fields['host_fields'], request_params=params)
                print("测试通过")
            except AssertionError as e:
                all_errors.append(str(e))

        return all_errors