import pytest
from common.assert_response import assert_fieldids
from common.yaml_loader import load_test_params, load_test_config, get_param_ids


class TestSortExHqReq:
    """测试排序行情接口，校验响应时间及内容一致性"""

    @pytest.mark.parametrize(
        "setDomain, coltype, startxh, wantnum, sorttype, drate, fieldids",
        load_test_params('test_sort_ex_hq_2021_params.yaml'),
        ids=get_param_ids('test_sort_ex_hq_2021_params.yaml')
    )
    def test_sort_ex_hq(self, api_helper, logger, setDomain, coltype, startxh, wantnum, sorttype, drate, fieldids):
        """测试排序行情接口"""
        # 加载配置
        config = load_test_config('test_sort_ex_hq_2021_params.yaml')
        test_fields = config['test_fields']
        test_config = config['test_config']

        # 请求参数字典，用于日志记录
        params = {
            'setDomain': setDomain, 'coltype': coltype, 'startxh': startxh,
            'wantnum': wantnum, 'sorttype': sorttype, 'drate': drate, 'fieldids': fieldids
        }

        print(f"请求参数: {params}")

        # 并发获取接口响应
        response, new_response = api_helper.call_method_concurrently(
            test_config['method_name'],
            setDomain=setDomain, coltype=coltype, startxh=startxh,
            wantnum=wantnum, sorttype=sorttype, drate=drate, fieldids=fieldids
        )

        # 获取响应时间和服务器信息
        response_times = api_helper.get_last_response_times()
        server_info = api_helper.get_last_server_info()

        # 执行断言测试
        all_errors = self._run_assertions(
            response, new_response, test_fields, test_config,
            params, server_info, logger, response_times
        )

        # 记录测试结果
        logger.log_test_result(
            server_info=server_info, response_times=response_times,
            request_params=params, has_errors=bool(all_errors), error_details=all_errors
        )

        # 如果有错误则抛出
        if all_errors:
            combined_error = "\n".join([f"共发现 {len(all_errors)} 处错误:"] + all_errors)
            raise AssertionError(combined_error)

    def _run_assertions(self, response, new_response, test_fields, test_config,
                       params, server_info, logger, response_times):
        """执行断言测试"""
        all_errors = []
        hq_fields = test_fields['hq_fields']

        # 根据配置获取响应数据结构
        response_fields = getattr(response, test_config['response_structure'])
        new_response_fields = getattr(new_response, test_config['response_structure'])

        # 比较排序行情数据
        for i, (response_item, new_response_item) in enumerate(zip(response_fields, new_response_fields)):
            try:
                # 单独检查nkey字段（避免大整数导致内存问题）
                old_nkey = getattr(response_item.hq, 'nkey', None)
                new_nkey = getattr(new_response_item.hq, 'nkey', None)
                if old_nkey != new_nkey:
                    all_errors.append(f"第{i+1}条记录nkey字段不匹配: 基准接口={old_nkey}, 新接口={new_nkey}")

                # 比较其他字段
                assert_fieldids(
                    response_item.hq, new_response_item.hq, hq_fields,
                    request_params=params, server_info=server_info,
                    logger=logger, response_times=response_times
                )
            except Exception as e:
                all_errors.append(f"第{i+1}条记录: {str(e)}")

        return all_errors
