# 分时数据测试参数配置 (1369)
# 格式: [setcode, code, days]

kline_test_params:
  # 基础测试场景
  basic_tests:
    - name: "GEM-301156-1day"
      params: [0, "301156", 1]
      description: "默认创业板-1日分时"
    - name: "SH-600000-2days"
      params: [1, "600000", 2]
      description: "沪市主板-2日分时"
    - name: "SZ-000001-5days"
      params: [0, "000001", 5]
      description: "深市主板-5日分时"

  # 特殊板块测试
  special_boards:
    - name: "STAR-688001-10days"
      params: [1, "688001", 10]
      description: "科创板-10日分时(最大天数)"
    - name: "BJ-830001-3days"
      params: [2, "830001", 3]
      description: "北交所-3日分时"

  # 指数测试场景
  indices_tests:
    - name: "SSE-000001-1day"
      params: [1, "000001", 1]
      description: "上证指数-1日分时"
    - name: "CSI300-000300-7days"
      params: [0, "000300", 7]
      description: "沪深300指数-7日分时"
    - name: "GEM-INDEX-399006-4days"
      params: [0, "399006", 4]
      description: "创业板指-4日分时"

  # 其他股票测试
  other_stocks:
    - name: "SZ-000008-1day"
      params: [0, "000008", 1]
      description: "深市000008-1日分时"
    - name: "GEM-300059-1day"
      params: [0, "300059", 1]
      description: "创业板300059-1日分时"
    - name: "SME-002024-1day"
      params: [0, "002024", 1]
      description: "深市中小板-1日分时(最小天数)"
    - name: "SH-601398-10days"
      params: [1, "601398", 10]
      description: "沪市大盘股-10日分时(最大天数)"

# 测试字段配置
test_fields:
  hq_fields:
    - 'Status'
    - 'PreClosePrice'
    - 'OpenPrice'
    - 'HighPrice'
    - 'LowPrice'
    - 'NowPrice'
    - 'zangsu'
    - 'AveragePrice'
    - 'LimitUpPrice'
    - 'LimitDownPrice'
    - 'PERatio'
    - 'Volume'
    - 'NowVol'
    - 'Amount'
    - 'NowAmount'
    - 'Inside'
    - 'Outside'
    - 'AllBuyPriceCount'
    - 'AllSellPriceCount'
    - 'BuyPrice'
    - 'BuyVolume'
    - 'SellPrice'
    - 'SellVolume'
  
  minute_fields:
    - 'jclTime'
    - 'openPrice'
    - 'highPrice'
    - 'nowPrice'
    - 'lowPrice'
    - 'avePrice'
    - 'amount'
    - 'volume'
    - 'buyvol'
    - 'sellvol'
    - 'preSettlePrice'

# 测试配置
test_config:
  old_method_name: 'get_minute_req'
  new_method_name: 'get_minute_data'
  max_minute_compare: 10  # 最多比较前10条分时数据
  max_errors: 5  # 最多记录5个错误
  decimal_places: 2  # 小数位数
  ave_price_tolerance: 2  # avePrice字段允许的容差
  
# 字段处理配置
field_processing:
  # avePrice字段特殊处理：整数部分允许±2的容差
  ave_price_tolerance: 2
  # preSettlePrice字段不用比较
  excluded_fields: ['preSettlePrice']
  # nkey字段单独处理（避免大整数导致的内存问题）
  special_nkey_handling: true
  
# 接口说明
interface_info:
  description: "1369 分时数据接口"
  msg_id: 1369
  parameters: "setcode, code, days"
  notes: "preSettlePrice不用比，avePrice字段整数部分允许±2容差，nkey字段单独处理"
