"""
2008 板块、指数成分股接口
TickCount 10以内的差异忽略

"""
import pytest
from common.assert_response import assert_response_equal, assert_fieldids
from common.yaml_loader import load_test_params, load_test_config, get_param_ids


class TestBlockSortHQ:
    """测试板块、指数成分股接口一致性"""

    @pytest.mark.parametrize(
        "code, setcode, coltype, startxh, wantnum, sorttype, fieldids",
        load_test_params('test_block_sort_hq_2008_params.yaml'),
        ids=get_param_ids('test_block_sort_hq_2008_params.yaml')
    )
    def test_block_sort_hq(self, api_helper, logger, code, setcode, coltype, startxh, wantnum, sorttype, fieldids):
        """测试板块、指数成分股接口，比较旧接口和新接口的响应一致性"""
        # 加载配置
        config = load_test_config('test_block_sort_hq_2008_params.yaml')
        test_fields = config['test_fields']
        test_config = config['test_config']

        # 请求参数字典，用于日志记录
        params = {
            "code": code, "setcode": setcode, "coltype": coltype, "startxh": startxh,
            "wantnum": wantnum, "sorttype": sorttype, "fieldids": fieldids
        }

        print(f"请求参数：{params}")

        # 并发执行新旧接口
        response, new_response = api_helper.call_method_concurrently(
            test_config['method_name'],
            code=code, setcode=setcode, coltype=coltype, startxh=startxh,
            wantnum=wantnum, sorttype=sorttype, fieldids=fieldids
        )

        # 获取响应时间和服务器信息
        response_times = api_helper.get_last_response_times()
        server_info = api_helper.get_last_server_info()

        # 执行断言测试
        all_errors = self._run_assertions(
            response, new_response, test_fields, test_config,
            params, server_info, logger, response_times
        )

        # 记录测试结果
        logger.log_test_result(
            server_info=server_info, response_times=response_times,
            request_params=params, has_errors=bool(all_errors), error_details=all_errors
        )

        # 如果有错误则抛出
        if all_errors:
            combined_error = "\n".join([f"共发现 {len(all_errors)} 处断言失败:"] + all_errors)
            raise AssertionError(combined_error)


    def _run_assertions(self, response, new_response, test_fields, test_config,
                       params, server_info, logger, response_times):
        """执行断言测试"""
        all_errors = []
        hq_fields = test_fields['hq_fields']

        # 根据配置获取响应数据结构
        response_fields = getattr(response, test_config['response_structure'])
        new_response_fields = getattr(new_response, test_config['response_structure'])

        for response_hq, new_response_hq in zip(response_fields, new_response_fields):
            try:
                print(f"基准接口响应：{response_hq.hq}-新接口响应：{new_response_hq.hq}-比对字段{hq_fields}")
                # 使用assert_fieldids函数，支持PERatio等字段的比较
                assert_fieldids(
                    response_hq.hq, new_response_hq.hq, hq_fields,
                    request_params=params, server_info=server_info,
                    logger=logger, response_times=response_times
                )
                print("测试通过")
            except Exception as e:
                all_errors.append(str(e))

        return all_errors