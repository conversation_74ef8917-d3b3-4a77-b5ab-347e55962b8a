"""2015 分类排序行情接口"""
import pytest
import time
from common.assert_response import assert_response_equal, assert_fieldids, assert_list_equal, assert_round_response
from common.yaml_loader import load_test_params, load_test_config, get_param_ids

# 暂不测试，接口还未改好 (此注释已了解)


class TestGetSortedHQ:
    """测试分类排序行情接口一致性"""

    @pytest.mark.parametrize(
        "setDomain, coltype, startxh, wantnum, sorttype, drate, fieldids",
        load_test_params('test_get_sorted_hq_2015_params.yaml'),
        ids=get_param_ids('test_get_sorted_hq_2015_params.yaml')
    )
    def test_get_sorted_hq(self, api_helper, logger, setDomain, coltype, startxh, wantnum, sorttype, drate,
                           fieldids):
        """测试分类排序行情接口，比较旧接口和新接口的响应一致性"""
        # 加载配置
        config = load_test_config('test_get_sorted_hq_2015_params.yaml')
        test_fields = config['test_fields']
        test_config = config['test_config']

        # 创建参数字典用于日志记录
        params = {
            'setDomain': setDomain, 'coltype': coltype, 'startxh': startxh,
            'wantnum': wantnum, 'sorttype': sorttype, 'drate': drate, 'fieldids': fieldids
        }

        logger.log_debug(f"请求参数：setDomain={setDomain},coltype={coltype},startxh={startxh},"
                        f"wantnum={wantnum},sorttype={sorttype},drate={drate},fieldids={fieldids}")
        print(f"请求参数：setDomain={setDomain},coltype={coltype},startxh={startxh},"
              f"wantnum={wantnum},sorttype={sorttype},drate={drate},fieldids={fieldids}")

        # 并发调用新旧接口
        response, new_response = api_helper.call_method_concurrently(
            test_config['method_name'],
            setDomain=setDomain, coltype=coltype, startxh=startxh,
            wantnum=wantnum, sorttype=sorttype, drate=drate, fieldids=fieldids
        )

        # 获取响应时间和服务器信息
        response_times = api_helper.get_last_response_times()
        server_info = api_helper.get_last_server_info()

        # 执行断言测试
        all_errors = self._run_assertions(
            response, new_response, test_fields, test_config, params
        )

        # 记录测试结果
        logger.log_test_result(
            server_info=server_info, response_times=response_times,
            request_params=params, has_errors=bool(all_errors), error_details=all_errors
        )

        # 如果有错误则抛出
        if all_errors:
            combined_error = "\n".join([f"共发现 {len(all_errors)} 处断言失败:"] + all_errors)
            raise AssertionError(combined_error)

    def _run_assertions(self, response, new_response, test_fields, test_config, params):
        """执行断言测试"""
        all_errors = []
        hq_fields = test_fields['hq_fields']

        # 根据配置获取响应数据结构
        response_fields = getattr(response, test_config['response_structure'])
        new_response_fields = getattr(new_response, test_config['response_structure'])

        for resp_field, new_resp_field in zip(response_fields, new_response_fields):
            try:
                assert_response_equal(resp_field.hq, new_resp_field.hq, hq_fields, request_params=params)
            except Exception as e:
                all_errors.append(str(e))

        return all_errors
