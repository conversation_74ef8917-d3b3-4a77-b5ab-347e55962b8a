import pytest
import time
from common.assert_response import assert_round_response
from common.yaml_loader import load_test_params, load_test_config, get_param_ids

# 自查jcltime 都是正常的 (此注释已了解)


class TestMultiTodayMoneyFlow:
    """测试当日多品种资金流接口一致性"""

    @pytest.mark.parametrize(
        "nkey",
        load_test_params('test_multi_today_23203_params.yaml'),
        ids=get_param_ids('test_multi_today_23203_params.yaml')
    )
    def test_multi_today_moneyflow(self, api_helper, nkey, logger):
        """测试当日多品种资金流接口，并校验响应时间和内容一致性"""
        # 加载配置
        config = load_test_config('test_multi_today_23203_params.yaml')
        test_fields = config['test_fields']
        test_config = config['test_config']

        # 创建参数字典用于日志记录
        params = {'nkey': nkey}

        logger.log_debug(f"请求参数：nkey={nkey}")
        print(f"请求参数：nkey={nkey}")

        # 并发执行新旧接口
        # nkey是从YAML加载的元组，需要提取第一个元素（列表）
        nkey_list = nkey[0] if isinstance(nkey, tuple) else nkey
        response, new_response = api_helper.call_method_concurrently(
            test_config['method_name'], nkey_list
        )

        # 获取响应时间和服务器信息
        response_times = api_helper.get_last_response_times()
        server_info = api_helper.get_last_server_info()

        # 执行断言测试
        all_errors = self._run_assertions(
            response, new_response, test_fields, test_config, params
        )

        # 记录测试结果
        logger.log_test_result(
            server_info=server_info, response_times=response_times,
            request_params=params, has_errors=bool(all_errors), error_details=all_errors
        )

        # 如果有错误则抛出
        if all_errors:
            combined_error = "\n".join([f"共发现 {len(all_errors)} 处断言失败:"] + all_errors)
            raise AssertionError(combined_error)


    def _run_assertions(self, response, new_response, test_fields, test_config, params):
        """执行断言测试"""
        all_errors = []

        # 检查响应是否为None
        if response is None or new_response is None:
            all_errors.append("接口返回None，无法进行数据比较")
            return all_errors

        # 检查响应是否有data属性
        if not hasattr(response, 'data') or not hasattr(new_response, 'data'):
            all_errors.append("响应缺少data属性")
            return all_errors

        # 检查data是否为空
        if not response.data or not new_response.data:
            all_errors.append("响应data为空")
            return all_errors

        # 检查jcltime一致性
        try:
            assert response.data[0].jcltime == new_response.data[0].jcltime
        except Exception as e:
            if hasattr(response.data[0], 'jcltime') and hasattr(new_response.data[0], 'jcltime'):
                error_msg = (f"jcltime新老接口不一致: 新接口={new_response.data[0].jcltime}, "
                            f"老接口={response.data[0].jcltime}")
            else:
                error_msg = f"jcltime字段访问失败: {str(e)}"
            all_errors.append(error_msg)

        # 获取资金流向字段
        fields = test_fields['moneyflow_fields']
        decimal_places = test_config['decimal_places']

        # 逐个字段对比amt、vol、cnt三个数据集
        for data_type in test_config['data_types']:
            try:
                old_data = getattr(response.data[0], data_type)
                new_data = getattr(new_response.data[0], data_type)
                assert_round_response(
                    old_data, new_data, fields,
                    decimal_places=decimal_places, request_params=params
                )
                if data_type == 'cnt':  # 最后一个数据集测试通过时打印
                    print("测试通过")
            except Exception as e:
                all_errors.append(f"{data_type}数据断言失败: {str(e)}")

        return all_errors

