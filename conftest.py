import logging
import pytest
from config import SERVER_CONFIG, LOG_CONFIG
from common.log_handle import LogManager
from common.request_api_new import QuoteClient
from common.request_api import JCLApi
from common.concurrent_api_caller import ApiCallHelper

@pytest.fixture(scope="session")
def connect():
    """创建一个已连接的行情客户端夹具（旧接口服务器，使用request_api.py连接逻辑）"""
    server_config = SERVER_CONFIG["old_server"]
    client = JCLApi(server_config["host"], server_config["port"])

    # 连接服务器
    if not client.connect():
        pytest.fail(f"无法连接到服务器 {server_config['host']}:{server_config['port']}")

    yield client  # 提供客户端给测试用例使用

    # 测试结束后清理资源
    client.close()

@pytest.fixture(scope="session")
def connect_new():
    """创建一个已连接并认证的行情客户端夹具（新接口服务器）"""
    server_config = SERVER_CONFIG["new_server"]

    # 检查服务器是否启用
    if not server_config.get("enabled", True):
        pytest.skip(f"新服务器已禁用: {server_config['description']}")

    client = QuoteClient(server_config["host"], server_config["port"])

    # 连接服务器
    if not client.connect():
        pytest.fail(f"无法连接到服务器 {server_config['host']}:{server_config['port']}")

    # 进行认证
    if not client.auth():
        pytest.fail(f"认证失败 - 服务器: {server_config['host']}:{server_config['port']}")

    yield client  # 提供客户端给测试用例使用

    # 测试结束后清理资源
    if client.sock:
        client.sock.close()

@pytest.fixture(scope="session")
def logger():
    """全局日志管理器（记录INFO和ERROR）"""
    global _session_logger
    if _session_logger is None:
        _session_logger = LogManager(log_level=logging.INFO)
    yield _session_logger

@pytest.fixture(scope="session")
def api_helper(connect, connect_new, logger):
    """并发API调用助手"""
    helper = ApiCallHelper(connect, connect_new, logger=logger.logger)
    yield helper


# pytest钩子函数，用于在测试文件结束时记录汇总
_current_test_file = None
_session_logger = None

def pytest_sessionstart(session):
    """测试会话开始时初始化"""
    global _session_logger
    if _session_logger is None:
        _session_logger = LogManager(log_level=logging.INFO)

def pytest_runtest_setup(item):
    """每个测试开始前检查是否切换了文件"""
    global _current_test_file, _session_logger
    current_file = item.fspath.basename

    if _current_test_file and _current_test_file != current_file and _session_logger:
        # 切换到新文件时，为上一个文件记录汇总
        _session_logger.log_test_file_summary(_current_test_file)

    _current_test_file = current_file

def pytest_sessionfinish(session, exitstatus):
    """测试会话结束时为最后一个文件记录汇总"""
    global _current_test_file, _session_logger
    if _current_test_file and _session_logger:
        _session_logger.log_test_file_summary(_current_test_file)
