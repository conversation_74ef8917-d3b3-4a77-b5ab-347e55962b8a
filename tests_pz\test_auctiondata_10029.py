import sys
from pathlib import Path
# 确保可以从项目根目录导入common包（直接运行该文件时需要）
sys.path.insert(0, str(Path(__file__).resolve().parents[1]))

import pytest
from common.assert_response import assert_response_equal, assert_round_response, assert_response_equal_with_quotetime
from common.yaml_loader import load_test_params, load_test_config, get_param_ids


class TestAuctionData:
    """测试竞价数据接口一致性"""

    @pytest.mark.parametrize(
        "code, setcode, nkey, type_id",
        load_test_params('test_auctiondata_10029_params.yaml'),
        ids=get_param_ids('test_auctiondata_10029_params.yaml')
    )
    def test_get_auctiondata_ans(self, api_helper, code, setcode, nkey, type_id, logger):
        """测试新旧竞价数据接口"""
        # 加载配置
        config = load_test_config('test_auctiondata_10029_params.yaml')
        test_fields = config['test_fields']
        test_config = config['test_config']

        # 请求参数
        params = {"code": code, "setcode": setcode, "nkey": nkey, "type": type_id}
        print(f"请求参数:code={code}, setcode={setcode}, nkey={nkey}, type={type_id}")

        # 并发获取响应
        response, new_response = api_helper.call_method_concurrently(
            test_config['method_name'], **params
        )

        # 获取响应时间和服务器信息
        response_times = api_helper.get_last_response_times()
        server_info = api_helper.get_last_server_info()

        # 执行断言测试
        all_errors = self._run_assertions(
            response, new_response, test_fields, test_config,
            params, server_info, logger, response_times
        )

        # 记录测试结果
        logger.log_test_result(
            server_info=server_info, response_times=response_times,
            request_params=params, has_errors=bool(all_errors), error_details=all_errors
        )

        # 如果有错误则抛出
        if all_errors:
            combined_error = "\n".join([f"共发现 {len(all_errors)} 处断言失败:"] + all_errors)
            raise AssertionError(combined_error)

    def _run_assertions(self, response, new_response, test_fields, test_config,
                       params, server_info, logger, response_times):
        """执行断言测试"""
        all_errors = []

        for resp_item, new_resp_item in zip(response.ticks, new_response.ticks):
            # 时间字段测试
            try:
                assert_response_equal_with_quotetime(
                    resp_item, new_resp_item, test_fields['quote_fields'],
                    quote_time_length=test_config['quote_time_length'], request_params=params
                )
            except AssertionError as e:
                all_errors.append(str(e))

            # 数量字段测试
            try:
                assert_response_equal(resp_item, new_resp_item, test_fields['volume_fields'], request_params=params)
            except AssertionError as e:
                all_errors.append(str(e))

            # 浮点数字段测试
            try:
                assert_round_response(
                    resp_item, new_resp_item, test_fields['float_fields'],
                    decimal_places=test_config['decimal_places'], request_params=params
                )
            except AssertionError as e:
                all_errors.append(str(e))

        return all_errors