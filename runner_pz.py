# 配置 pytest 参数
import pytest
import os

# 创建reports目录
reports_dir = "reports"
if not os.path.exists(reports_dir):
    os.makedirs(reports_dir)

args = [
    '--report=TestReport_ph.html',  # 使用pytest-testreport生成报告，使用简单文件名
    '--title=APP新-老行情接口盘中测试报告',  # 指定报告标题
    '--tester=魏振',  # 指定报告中的测试者
    '--desc=APP新老行情接口盘中自动化测试',  # 指定报告中的项目描述
    '--template=1',  # 指定报告模板样式（样式二）
    '-v',  # 详细输出
    'tests_pz/'  # 指定测试目录
]
# 执行 pytest 测试
pytest.main(args)