import socket
import struct
import time
import logging
import zlib
from http.client import responses
from typing import Optional, Any
import base_pb2
import JCLBean_pb2
from typing import Dict, List
import snappy  # 引入 Snappy 解压缩库

class QuoteBaseClient:
    """行情客户端基类"""

    def __init__(self, host: str, port: int):
        self.host = host
        self.port = port
        self.sock: Optional[socket.socket] = None
        self.is_auth = False
        self.logger = self._setup_logger()
        self.response_times = {}  # 记录每个API的响应时间
        self.server_info = f"{host}:{port}"  # 服务器标识

    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger('QuoteClient')
        logger.setLevel(logging.INFO)
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        return logger

    def connect(self) -> bool:
        """建立TCP连接"""
        try:
            if self.sock:
                self.sock.close()

            self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.sock.connect((self.host, self.port))
            self.logger.info(f"已连接到服务器 {self.host}:{self.port}")
            return True
        except Exception as e:
            self.logger.error(f"连接失败: {e}")
            return False

    def disconnect(self) -> bool:
        """断开TCP连接"""
        try:
            if self.sock:
                self.sock.close()
                self.sock = None
                self.is_auth = False
                self.logger.info(f"已断开与服务器 {self.host}:{self.port} 的连接")
                return True
            return True
        except Exception as e:
            self.logger.error(f"断开连接失败: {e}")
            return False

    def reconnect(self, max_attempts: int = 3, delay: int = 5) -> bool:
        """重连机制"""
        self.logger.info("开始尝试重连...")
        for attempt in range(max_attempts):
            if self.connect():
                if self.auth():
                    return True
            self.logger.warning(f"重连尝试 {attempt + 1}/{max_attempts} 失败")
            time.sleep(delay)
        return False

    def _send_msg(self, msg_id: int, msg_data: Any = None) -> bool:
        """发送消息"""
        try:
            head = base_pb2.BaseHead(
                MsgID=msg_id,
                ReqID=1,
                ProtoType=0,
                CompressType=0,
                OriginBodyLength=0,
                CheckSum=0
            )

            base_msg = base_pb2.BaseMsg()
            base_msg.Head.CopyFrom(head)  # 正确赋值 Head 字段

            if msg_data:
                base_msg.MsgData = msg_data.SerializeToString()

            msg_bytes = base_msg.SerializeToString()
            msg_len = len(msg_bytes)

            self.sock.sendall(struct.pack("<i", msg_len) + msg_bytes)
            return True
        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")
            return False

    def _recv_msg(self) -> Optional[base_pb2.BaseMsg]:
        """接收消息，并根据 CompressType 进行解压缩"""
        try:
            # 接收消息长度（4字节）
            length_bytes = b""
            while len(length_bytes) < 4:
                chunk = self.sock.recv(4 - len(length_bytes))
                if not chunk:
                    raise ConnectionError("连接中断，未能接收到消息长度")
                length_bytes += chunk
            msg_len = struct.unpack("<i", length_bytes)[0]

            # 接收消息内容
            msg_bytes = b""
            while len(msg_bytes) < msg_len:
                chunk = self.sock.recv(msg_len - len(msg_bytes))
                if not chunk:
                    raise ConnectionError("连接中断，未能接收到完整消息")
                msg_bytes += chunk

            # 解析消息
            base_msg = base_pb2.BaseMsg()
            base_msg.ParseFromString(msg_bytes)

            # 检查是否需要解压缩
            if base_msg.Head.CompressType == 2:
                try:
                    base_msg.MsgData = snappy.uncompress(base_msg.MsgData)
                    base_msg.Head.CompressType = 0  # 解压后将 CompressType 置为 0
                    self.logger.debug("成功解压缩 Snappy 数据")
                except Exception as e:
                    self.logger.error(f"Snappy 解压缩失败: {e}")
                    return None

            return base_msg

        except Exception as e:
            self.logger.error(f"接收消息失败: {e}")
            return None

    def _api_call_with_timing(self, api_name: str, msg_id: int, msg_data: Any = None, response_parser=None):
        """
        带响应时间记录的API调用

        Args:
            api_name: API名称，用于日志记录
            msg_id: 消息ID
            msg_data: 请求数据
            response_parser: 响应解析函数

        Returns:
            解析后的响应数据和响应时间
        """
        start_time = time.time()

        try:
            # 发送请求
            if not self._send_msg(msg_id, msg_data):
                self.logger.error(f"[{self.server_info}] {api_name} 发送请求失败")
                return None, 0

            # 接收响应
            response = self._recv_msg()
            end_time = time.time()
            response_time = (end_time - start_time) * 1000  # 转换为毫秒

            if response is None:
                self.logger.error(f"[{self.server_info}] {api_name} 接收响应失败")
                return None, response_time

            # 记录响应时间（仅存储，不记录日志）
            self.response_times[api_name] = response_time

            # 解析响应
            if response_parser:
                parsed_response = response_parser(response)
                return parsed_response, response_time
            else:
                return response, response_time

        except Exception as e:
            end_time = time.time()
            response_time = (end_time - start_time) * 1000
            self.logger.error(f"[{self.server_info}] {api_name} 调用异常: {e}, 耗时: {response_time:.2f}ms")
            return None, response_time

    def get_response_time_summary(self) -> Dict[str, float]:
        """获取响应时间汇总"""
        return self.response_times.copy()

    def log_response_time_summary(self):
        """记录响应时间汇总日志"""
        if not self.response_times:
            self.logger.info(f"[{self.server_info}] 暂无API响应时间记录")
            return

        self.logger.info(f"📊 [{self.server_info}] API响应时间汇总:")
        total_time = 0
        for api_name, response_time in self.response_times.items():
            self.logger.info(f"  📡 {api_name}: {response_time:.2f}ms")
            total_time += response_time

        avg_time = total_time / len(self.response_times)
        self.logger.info(f"  📈 平均响应时间: {avg_time:.2f}ms")
        self.logger.info(f"  📊 总调用次数: {len(self.response_times)}")
        self.logger.info(f"  ⏱️  总耗时: {total_time:.2f}ms")

    def auth(self) -> bool:
        """认证"""
        try:
            # 尝试不同的Token配置
            tokens_to_try = [
                "your_token",  # 原始Token
                "",            # 空Token
                "test",        # 简单Token
                "demo"         # 演示Token
            ]

            for token in tokens_to_try:
                self.logger.info(f"尝试使用Token: '{token}'")

                auth_req = base_pb2.RequestAuth(
                    Token=token,
                    AppName="PythonDemo",
                    AppVer="1.0.0",
                    Tag="test"
                )

                if not self._send_msg(1, auth_req):
                    self.logger.error("发送认证消息失败")
                    continue

                resp = self._recv_msg()
                if not resp:
                    self.logger.error("未收到认证响应")
                    continue

                try:
                    auth_resp = base_pb2.ResponseAuth()
                    auth_resp.ParseFromString(resp.MsgData)

                    self.is_auth = (auth_resp.AuthResult == 0)
                    if self.is_auth:
                        self.logger.info(f"认证成功，使用Token: '{token}'")
                        return True
                    else:
                        self.logger.warning(f"认证失败，AuthResult: {auth_resp.AuthResult}, Token: '{token}'")
                except Exception as parse_e:
                    self.logger.error(f"解析认证响应失败: {parse_e}")
                    continue

            self.logger.error("所有Token尝试均失败")
            return False

        except Exception as e:
            self.logger.error(f"认证过程发生错误: {e}")
            return False


class QuoteClient(QuoteBaseClient):
    """行情客户端"""

    def subscribe_quote(self, nkeys: list) -> bool:
        """订阅行情"""
        if not self.is_auth:
            self.logger.error("未认证，请先调用auth()")
            return False

        try:
            sub_req = JCLBean_pb2.sub_unsub_hq_req()
            sub_req.aKey.extend(nkeys)

            if not self._send_msg(134, sub_req):
                return False

            resp = self._recv_msg()
            if not resp:
                return False

            if resp.MsgData:
                hq_data = JCLBean_pb2.multi_hq_ans()
                hq_data.ParseFromString(resp.MsgData)

            return hq_data

        except Exception as e:
            self.logger.error(f"订阅行情失败: {e}")
            return False

    def unsubscribe_quote(self, codes: List[int]) -> Optional[int]:
        """
        取消订阅行情推送

        Args:
            codes: 要取消订阅的品种key列表，传入[-1]表示取消全部订阅

        Returns:
            int: 剩余订阅数量（subnum），失败返回None
        """
        if not self.is_auth:
            self.logger.error("未认证，请先调用auth()")
            return None

        try:
            # 构造请求消息
            req = JCLBean_pb2.sub_unsub_hq_req()
            req.aKey.extend(codes)  # 使用extend处理列表

            # 发送请求，MsgID 为 135
            if not self._send_msg(135, req):
                self.logger.error("发送取消订阅请求失败")
                return None

            # 接收响应
            resp = self._recv_msg()
            if not resp:
                self.logger.error("未收到取消订阅响应")
                return None

            # 解析响应
            resp_data = JCLBean_pb2.unsub_hq_ans()
            resp_data.ParseFromString(resp.MsgData)

            self.logger.info(f"剩余订阅数量: {resp_data.subnum}")
            return resp_data.subnum

        except Exception as e:
            self.logger.error(f"取消订阅失败: {e}", exc_info=True)
            return None

    def get_code_info(self, setcode: int, startxh: int ) -> List[Dict[str, Any]]:
        """
        请求市场代码链（含中英文名称）

        Args:
            setcode (int): 市场代码
            startxh (int): 起始序号，默认为0

        Returns:
            List[Dict[str, Any]]: 包含代码链信息的列表，每个元素是一个字典，包含StkInfoNew的所有字段
        """
        if not self.is_auth:
            self.logger.error("未认证，请先调用auth()")
            return []

        try:
            # 构造请求消息
            req = JCLBean_pb2.code_req()
            req.setcode = setcode
            req.startxh = startxh

            # 发送请求，MsgID 为 1111
            if not self._send_msg(1111, req):
                self.logger.error("发送请求失败")
                return []

            # 接收响应
            resp = self._recv_msg()
            if not resp:
                self.logger.error("未收到响应")
                return []

            self.logger.info(f"收到代码链响应, MsgID={resp.Head.MsgID}, MsgData长度={len(resp.MsgData)}")

            # 调试：打印原始字节数据（前100字节，避免日志过长）
            self.logger.debug(f"原始MsgData (前100字节): {resp.MsgData[:100].hex()}")

            # 解析响应消息
            resp_data = JCLBean_pb2.code_ans()
            try:
                resp_data.ParseFromString(resp.MsgData)
                self.logger.info(f"成功解析code_ans，条目数: {len(resp_data.code)}")
            except Exception as e:
                self.logger.error(f"解析code_ans失败: {e}")
                # 尝试打印部分数据以帮助调试
                self.logger.debug(f"MsgData样本: {resp.MsgData[:50].hex()}")
                return []

            # 将 StkInfoNew 转换为 Python 字典列表
            return resp_data

        except Exception as e:
            self.logger.error(f"获取市场代码链失败: {e}")
            return []

    def sort_ex_hq_req(self, setDomain: int, coltype: int, startxh: int, wantnum: int, sorttype: int, drate: int,
                       fieldids: list) -> dict:
        """请求分类排序行情"""
        if not self.is_auth:
            self.logger.error("未认证，请先调用auth()")
            return {}

        try:
            # 构造请求消息
            req = JCLBean_pb2.sort_ex_hq_req()
            req.setDomain = setDomain
            req.coltype = coltype
            req.startxh = startxh
            req.wantnum = wantnum
            req.sorttype = sorttype
            req.drate = drate
            req.fieldids.extend(fieldids)  # 通过 extend 方法来添加 fieldids

            # 发送请求，MsgID 为 2015
            if not self._send_msg(2015, req):
                self.logger.error("发送请求失败")
                return {}

            # 接收响应
            resp = self._recv_msg()
            if not resp:
                self.logger.error("未收到响应")
                return {}

            self.logger.info(f"收到分类排序行情响应, MsgID={resp.Head.MsgID}, MsgData长度={len(resp.MsgData)}")

            # 解析响应消息
            resp_data = JCLBean_pb2.sort_ex_hq_ans()
            try:
                resp_data.ParseFromString(resp.MsgData)
                self.logger.info(f"成功解析sort_ex_hq_ans，条目数: {len(resp_data.fields)}")
            except Exception as e:
                self.logger.error(f"解析sort_ex_hq_ans失败: {e}")
                return {}


            return resp_data

        except Exception as e:
            self.logger.error(f"请求分类排序行情失败: {e}")
            return {}

    def get_minute_data(self, code: str, setcode: int = 0, days: int = 1,nkey: int = 0) -> Optional[
        Dict[str, Any]]:
        """
        获取1-10日分时走势数据

        Args:
            nkey: 品种内部编码（优先使用，0表示不使用）
            setcode: 市场代码（当nkey=0时需提供）
            code: 品种代码（当nkey=0时需提供）
            days: 请求天数（1-10，默认1）

        Returns:
            dict: 包含分时数据的字典结构，包含以下键：
                - count: 数据条数
                - close: 收盘价
                - hq: 当前行情快照（字典）
                - minute: 分时数据列表（字典列表）
        """
        if not self.is_auth:
            self.logger.error("未认证，请先调用auth()")
            return None

        if not (1 <= days <= 10):
            self.logger.error("请求天数需在1-10之间")
            return None



        try:
            # 构造请求结构
            code_info = JCLBean_pb2.tagCodeWithNkey()
            if nkey != 0:
                code_info.nkey = nkey
            else:
                code_info.setcode = setcode
                code_info.code = code

            req = JCLBean_pb2.minute_req()
            req.code.CopyFrom(code_info)
            req.days = days

            # 发送请求（MsgID 1369）
            if not self._send_msg(1369, req):
                self.logger.error("发送分时数据请求失败")
                return None

            # 接收响应
            resp = self._recv_msg()
            if not resp or not resp.MsgData:
                self.logger.error("未收到有效响应")
                return None

            # 解析响应数据
            resp_data = JCLBean_pb2.minute_ans()
            resp_data.ParseFromString(resp.MsgData)

            return resp_data

        except Exception as e:
            self.logger.error(f"获取分时数据失败: {e}", exc_info=True)
            return None

    def get_adjusted_kline(self,
                           nkey: int = 0,
                           setcode: int = 0,
                           code: str = "",
                           period: int = 1,
                           offset: int = 0,
                           num: int = 100,
                           mulnum: int = 1) -> Optional[Dict[str, Any]]:
        """
        1370 获取复权K线数据

        Args:
            nkey: 品种内部编码（优先使用，0表示不使用）
            setcode: 市场代码（当nkey=0时需提供）
            code: 品种代码（当nkey=0时需提供）
            period: K线周期（如1=日线，2=周线等，具体值域需参考协议）
            offset: 数据偏移量（0表示最新位置）
            num: 请求数据条数
            mulnum: 周期倍数（如5分钟线的5）

        Returns:
            dict: 包含K线数据的字典结构，包含以下键：
                - nkey: 品种内部编码
                - num: 实际返回数据条数
                - aK: K线数据列表，每个元素包含：
                    * dwItemNum: 采样点数
                    * jclTime: 时间戳（毫秒）
                    * fOpen: 开盘价
                    * fHigh: 最高价
                    * fLow: 最低价
                    * fClose: 收盘价
                    * 其他字段...
        """
        if not self.is_auth:
            self.logger.error("未认证，请先调用auth()")
            return None

        if num <= 0:
            self.logger.error("请求数量需大于0")
            return None

        try:
            # 构造品种标识
            code_info = JCLBean_pb2.tagCodeWithNkey()
            if nkey != 0:
                code_info.nkey = nkey
            else:
                code_info.setcode = setcode
                code_info.code = code

            # 构建请求
            req = JCLBean_pb2.analy_tq_req()
            req.code.CopyFrom(code_info)
            req.period = period
            req.offset = offset
            req.num = num
            req.mulnum = mulnum

            # 发送请求（MsgID 1370）
            if not self._send_msg(1370, req):
                self.logger.error("发送K线请求失败")
                return None

            # 接收响应
            resp = self._recv_msg()
            if not resp or not resp.MsgData:
                self.logger.error("未收到有效响应")
                return None

            # 解析响应
            resp_data = JCLBean_pb2.analy_tq_ans()
            resp_data.ParseFromString(resp.MsgData)

            return resp_data

        except Exception as e:
            self.logger.error(f"获取复权K线失败: {e}", exc_info=True)
            return None

    def get_sort_stock_block(self, code: str = "", setcode: int = 0, nkey: int = 0) -> Optional[
        List[JCLBean_pb2.stock_block_fields]]:
        """
        获取股票板块排序数据

        Args:
            code (str): 证券代码（当 nkey 为 0 时使用）
            setcode (int): 市场代码
            nkey (int): 品种内部编码（优先级高于 code，设为 0 时使用 code）

        Returns:
            Optional[List[JCLBean_pb2.stock_block_fields]]: 股票板块排序数据列表，或 None（如果请求失败）
        """
        if not self.is_auth:
            self.logger.error("未认证，请先调用auth()")
            return None

        try:
            # 构造嵌套的 tagCodeWithNkey 子结构
            req = JCLBean_pb2.tagCodeWithNkey(
                nkey=nkey,
                setcode=setcode,
                code=code if nkey == 0 else ""  # 当 nkey 不为 0 时，code 置空
            )

            # 发送请求，MsgID 使用 report_reqno.SORT_STOCK_BLOCK_REQ (2019)
            if not self._send_msg(JCLBean_pb2.report_reqno.SORT_STOCK_BLOCK_REQ, req):
                self.logger.error("发送股票板块排序请求失败")
                return None

            # 接收响应
            resp = self._recv_msg()
            if not resp:
                self.logger.error("未收到股票板块排序响应")
                return None



            # 解析响应数据
            if resp.MsgData:
                resp_data = JCLBean_pb2.sort_stock_block_ans()
                resp_data.ParseFromString(resp.MsgData)

                return resp_data.fields  # 返回 fields 列表
            else:
                self.logger.error("响应中无数据")
                return None

        except Exception as e:
            self.logger.error(f"获取股票板块排序数据失败: {e}")
            return None

    def get_financial_data(self, codes: List[Dict[str, Any]]) -> Optional[List[Dict[str, Any]]]:
        """
        23 获取批量财务数据

        Args:
            codes: 品种标识列表，每个元素为字典，支持两种格式：
                - {'nkey': 品种内部编码}
                - {'setcode': 市场代码, 'code': 证券代码}

        Returns:
            List[Dict]: 财务数据列表，每个元素包含完整的BaseCWInfo字段
        """
        if not self.is_auth:
            self.logger.error("请先完成认证操作")
            return None

        if not codes:
            self.logger.error("请求品种列表不能为空")
            return None

        try:
            # 构造请求体
            req = JCLBean_pb2.autobase_req()

            for code_info in codes:
                pb_code = req.codes.add()

                if 'nkey' in code_info and code_info['nkey'] != 0:
                    pb_code.nkey = code_info['nkey']
                else:
                    if 'setcode' not in code_info or 'code' not in code_info:
                        self.logger.error("缺失必要字段 setcode/code")
                        return None
                    pb_code.setcode = code_info['setcode']
                    pb_code.code = code_info['code']

            # 发送请求（MsgID 23）
            if not self._send_msg(23, req):
                self.logger.error("财务数据请求发送失败")
                return None

            # 接收并解析响应
            resp = self._recv_msg()
            if not resp or not resp.MsgData:
                self.logger.error("无效的响应数据")
                return None

            resp_data = JCLBean_pb2.autobase_ans()
            resp_data.ParseFromString(resp.MsgData)

            return resp_data.basep

        except Exception as e:
            self.logger.error(f"获取财务数据失败: {str(e)}", exc_info=True)
            return None

    def get_equity_adjustments(self, codes: List[Dict[str, Any]]) -> Optional[List[Dict[str, Any]]]:
        """
        获取批量股本除权数据

        Args:
            codes: 品种标识列表，每个元素为字典，支持两种格式：
                - {'nkey': 品种内部编码}
                - {'setcode': 市场代码, 'code': 证券代码}

        Returns:
            List[Dict]: 除权数据列表，每个元素包含：
                - nkey: 品种内部编码
                - setcode: 市场代码
                - code: 证券代码
                - adjustments: 除权明细列表，包含：
                    * date: 日期（uint32格式）
                    * type: 除权类型
                    * v01-v04: 调整参数值
        """
        if not self.is_auth:
            self.logger.error("请先完成认证操作")
            return None

        if not codes:
            self.logger.error("请求品种列表不能为空")
            return None

        try:
            # 构造请求体
            req = JCLBean_pb2.autogbbq_req()

            for code_info in codes:
                pb_code = req.codes.add()

                if 'nkey' in code_info and code_info['nkey'] != 0:
                    pb_code.nkey = code_info['nkey']
                else:
                    if 'setcode' not in code_info or 'code' not in code_info:
                        self.logger.error("缺失必要字段 setcode/code")
                        return None
                    pb_code.setcode = code_info['setcode']
                    pb_code.code = code_info['code']

            # 发送请求（MsgID 22）
            if not self._send_msg(22, req):
                self.logger.error("股本除权请求发送失败")
                return None

            # 接收并解析响应
            resp = self._recv_msg()
            if not resp or not resp.MsgData:
                self.logger.error("无效的响应数据")
                return None

            resp_data = JCLBean_pb2.autogbbq_ans()
            resp_data.ParseFromString(resp.MsgData)



            return resp_data

        except Exception as e:
            self.logger.error(f"获取股本除权失败: {str(e)}", exc_info=True)
            return None

    def get_market_config(self, setcode: int, verflag: int = 0) -> Optional[List[Dict[str, Any]]]:
        """
        获取市场配置信息

        Args:
            setcode: 市场代码（示例：1表示沪市）
            verflag: 版本标志（默认为0，具体含义需参考协议文档）

        Returns:
            List[Dict]: 市场配置列表，每个元素包含：
                - errflag: 错误标志（0表示正常）
                - setcode: 市场代码
                - timestamp: 纳秒级时间戳
                - date_components: 日期分解信息（年/月/日）
                - time_components: 时间分解信息（时/分/秒/百分秒）
                - common_times: 共同开盘时间列表
                - stock_count: 市场股票数量（qt[0]）
                - hostnames: 主机名列表
                # 其他字段根据需求展开...
        """
        if not self.is_auth:
            self.logger.error("请先完成认证操作")
            return None

        try:
            # 构造请求
            req = JCLBean_pb2.hostmore_req()
            req.setcode = int(setcode)
            req.verflag = verflag

            # 发送请求（MsgID 31）
            if not self._send_msg(31, req):
                self.logger.error("市场配置请求发送失败")
                return None

            # 接收并解析响应
            resp = self._recv_msg()
            if not resp or not resp.MsgData:
                self.logger.error("无效的响应数据")
                return None

            resp_data = JCLBean_pb2.hostmore_ans()
            resp_data.ParseFromString(resp.MsgData)


            return resp_data

        except Exception as e:
            self.logger.error(f"获取市场配置失败: {str(e)}", exc_info=True)
            return None

    def get_tick_data(self,
                      nkey: int = 0,
                      setcode: int = 0,
                      code: str = "",
                      type: int = 0,
                      startxh: int = 0,
                      num: int = 100) -> Optional[Dict[str, Any]]:
        """
        获取Tick级别行情数据

        Args:
            nkey: 品种内部编码（优先使用，0表示不使用）
            setcode: 市场代码（当nkey=0时需提供）
            code: 品种代码（当nkey=0时需提供）
            data_type: 数据类型（具体含义需参考协议文档）
            start_seq: 起始序号（0表示最新位置）
            num: 请求数据条数（最大支持1000条）

        Returns:
            dict: 包含Tick数据的字典结构，包含以下键：
                - nkey: 品种内部编码
                - ticks: Tick数据列表，每个元素包含：
                    * quote_time: 行情时间戳（纳秒级）
                    * price: 成交价格
                    * volume: 成交量（集合竞价为匹配量）
                    * unsuit_vol: 集合竞价未匹配量
                    * vol_diff: 持仓量变化（期货）
                    * flag: Tick标识位
                    * buy_order_no: 买方委托编号
                    * sell_order_no: 卖方委托编号
        """
        if not self.is_auth:
            self.logger.error("请先完成认证操作")
            return None

        if num <= 0 or num > 1000:
            self.logger.error("请求数量需在1-1000之间")
            return None

        try:
            # 构造品种标识
            code_info = JCLBean_pb2.tagCodeWithNkey()
            if nkey != 0:
                code_info.nkey = nkey
            else:
                if not code or setcode <= 0:
                    self.logger.error("当nkey=0时需提供有效的setcode和code")
                    return None
                code_info.setcode = setcode
                code_info.code = code

            # 构建请求
            req = JCLBean_pb2.tick_req()
            req.code.CopyFrom(code_info)
            req.type = type
            req.startxh = startxh
            req.num = num

            # 发送请求（MsgID 4069）
            if not self._send_msg(4069, req):
                self.logger.error("Tick数据请求发送失败")
                return None

            # 接收并解析响应
            resp = self._recv_msg()
            if not resp or not resp.MsgData:
                self.logger.error("未收到有效响应")
                return None

            resp_data = JCLBean_pb2.tick_ans()
            resp_data.ParseFromString(resp.MsgData)

            return resp_data

        except Exception as e:
            self.logger.error(f"获取Tick数据失败: {str(e)}", exc_info=True)
            return None

    def get_auction_data(self,
                         type: int,
                         nkey: int = 0,
                         setcode: int = 0,
                         code: str = None) -> Optional[Dict[str, Any]]:
        """
        获取集合竞价数据（含未匹配量）

        Args:
            auction_type: 竞价类型 (1=开盘集合竞价, 4=尾盘集合竞价)
            nkey: 品种内部编码（优先使用，0表示不使用）
            setcode: 市场代码（当nkey=0时需提供）
            code: 品种代码（当nkey=0时需提供）

        Returns:
            dict: 包含竞价数据的字典结构，包含以下键：
                - nkey: 品种内部编码
                - ticks: 竞价数据列表，每个元素包含：
                    * quote_time: 行情时间戳（纳秒级）
                    * price: 匹配价格
                    * volume: 匹配量
                    * unsuit_vol: 未匹配量
                    * vol_diff: 持仓量变化（期货）
                    * flag: Tick标识位
                    * buy_order_no: 买方委托编号
                    * sell_order_no: 卖方委托编号
        """
        if not self.is_auth:
            self.logger.error("请先完成认证操作")
            return None

        if type not in (1, 4):
            self.logger.error("无效的竞价类型，请使用1(开盘)或4(尾盘)")
            return None

        try:
            # 构造品种标识
            code_info = JCLBean_pb2.tagCodeWithNkey()
            if nkey != 0:
                code_info.nkey = nkey
            else:
                if not code or setcode < 0:
                    self.logger.error("当nkey=0时需提供有效的setcode和code")
                    return None
                code_info.setcode = setcode
                code_info.code = code

            # 构建请求
            req = JCLBean_pb2.auctiondata_req()
            req.code.CopyFrom(code_info)
            req.type = type

            # 发送请求（MsgID 10029）
            if not self._send_msg(10029, req):
                self.logger.error("集合竞价数据请求发送失败")
                return None

            # 接收并解析响应
            resp = self._recv_msg()
            if not resp or not resp.MsgData:
                self.logger.error("未收到有效响应")
                return None

            resp_data = JCLBean_pb2.auctiondata_ans()
            resp_data.ParseFromString(resp.MsgData)

            return resp_data

        except Exception as e:
            self.logger.error(f"获取集合竞价数据失败: {str(e)}", exc_info=True)
            return None

    def get_today_moneyflow(self, nkeys: List[int]) -> Optional[List[Dict[str, Any]]]:
        """
        获取当日资金流向及统计汇总数据（多品种）

        Args:
            nkeys: 品种内部编码列表（至少包含1个有效编码）

        Returns:
            List[Dict]: 资金流向数据列表，每个元素包含（字段需根据实际协议补充）：
                - nkey: 品种内部编码
                - main_net_inflow: 主力净流入
                - retail_net_inflow: 散户净流入
                - large_order_ratio: 大单成交占比
                # 其他字段需根据nkey_moneyflow_data实际结构补充
        """
        if not self.is_auth:
            self.logger.error("请先完成认证操作")
            return None

        if not nkeys or len(nkeys) == 0:
            self.logger.error("请求品种列表不能为空")
            return None

        try:
            # 构造请求
            req = JCLBean_pb2.multi_today_moneyflow_req()
            req.nkeys.extend(nkeys)  # 使用extend处理列表

            # 发送请求（MsgID 23203）
            if not self._send_msg(23203, req):
                self.logger.error("资金流向请求发送失败")
                return None

            # 接收并解析响应
            resp = self._recv_msg()
            if not resp or not resp.MsgData:
                self.logger.error("无效的响应数据")
                return None

            resp_data = JCLBean_pb2.multi_today_moneyflow_ans()
            resp_data.ParseFromString(resp.MsgData)


            self.logger.info(f"获取到 {len(resp_data.data)} 个品种的资金数据")
            return resp_data

        except Exception as e:
            self.logger.error(f"获取资金流向失败: {str(e)}", exc_info=True)
            return None

    def get_code_list(self, setcode: int, startxh: int = 0) -> Optional[List[Dict[str, Any]]]:
        """
        获取市场代码链信息（含中英文名称）

        Args:
            setcode: 市场代码（示例：0-沪深A股，1-沪市，2-深市）
            start_index: 起始序号（默认为0，用于分页获取）

        Returns:
            List[Dict]: 代码信息列表，每个元素包含：
                - code: 证券代码
                - name_cn: 中文名称
                - name_en: 英文名称
                - setcode: 所属市场代码
                - nkey: 品种内部编码
                # 其他字段根据StkInfoNew实际结构补充
        """
        if not self.is_auth:
            self.logger.error("请先调用auth()完成认证")
            return None

        if startxh < 0:
            self.logger.error("起始序号不能为负数")
            return None

        try:
            # 构造请求
            req = JCLBean_pb2.code_req()
            req.setcode = setcode
            req.startxh = startxh

            # 使用带响应时间记录的API调用
            def parse_response(resp):
                if not resp or not resp.MsgData:
                    self.logger.error("未收到有效响应")
                    return None

                resp_data = JCLBean_pb2.code_ans()
                resp_data.ParseFromString(resp.MsgData)
                self.logger.info(f"获取到{len(resp_data.code)}条代码信息，市场{setcode}")
                return resp_data

            result, response_time = self._api_call_with_timing(
                api_name=f"get_code_list(setcode={setcode})",
                msg_id=1110,
                msg_data=req,
                response_parser=parse_response
            )

            return result

        except Exception as e:
            self.logger.error(f"获取代码链失败: {e}", exc_info=True)
            return None

    def get_sort_shq_ex_req(self,
                               keys: list = None,
                               szkeys: list = None,
                               fieldids: list = None) -> dict:
        """获取排序行情数据 (功能号2023)

        Args:
            keys: 数字ID列表 或 szkeys二选一
            szkeys: 代码列表 或 keys二选一
            field_ids: 必填，查询字段ID列表

        Returns:
            dict: {timestamp: 时间戳, data: [{symbol: 代码, fields: {字段ID: 值}}]}
        """
        # 必要前置检查
        if not self.is_auth or not fieldids or not (keys or szkeys) or (keys and szkeys):
            self.logger.error("参数校验失败" +
                              ("未认证" if not self.is_auth else "") +
                              ("字段为空" if not fieldids else "") +
                              ("标识冲突" if (keys and szkeys) else ""))
            return {}

        try:
            # 构建请求
            req = JCLBean_pb2.sort_shq_ex_req()
            (req.keys if keys else req.szkeys).extend(keys or szkeys)
            req.fieldids.extend(fieldids)

            # 发送请求
            if not self._send_msg(2023, req): return {}

            # 接收响应
            if not (resp := self._recv_msg()) or not resp.MsgData:
                return {}

            # 解析响应
            result = JCLBean_pb2.sort_ex_hq_ans()
            result.ParseFromString(resp.MsgData)

            return result

        except Exception as e:
            self.logger.error(f"获取排序行情异常: {e}")
            return None

    def get_sort_ex_hq(self, setDomain: int, coltype: int, startxh: int, wantnum: int,
                       sorttype: int, drate: int, fieldids: List[int]) -> Optional[JCLBean_pb2.sort_ex_hq_ans]:
        """
        请求分类排序行情
        :param setDomain: 市场域
        :param coltype: 列类型
        :param startxh: 起始序号
        :param wantnum: 请求的条数
        :param sorttype: 排序类型
        :param drate: 数据更新速率
        :param fieldids: 要查询的字段ID列表
        :return: 分类排序行情响应数据 (JCLBean_pb2.sort_ex_hq_ans)
        """
        if not self.is_auth:
            self.logger.error("未认证，请先调用auth()")
            return None

        try:
            # 构造请求数据
            req = JCLBean_pb2.sort_ex_hq_req()
            req.setDomain = setDomain
            req.coltype = coltype
            req.startxh = startxh
            req.wantnum = wantnum
            req.sorttype = sorttype
            req.drate = drate
            req.fieldids.extend(fieldids)

            # 发送请求，假设 MsgID 为 2021（需根据实际协议确认）
            if not self._send_msg(2021, req):
                self.logger.error("发送请求失败")
                return None

            # 接收并解析响应
            resp = self._recv_msg()
            if not resp:
                self.logger.error("未收到响应")
                return None

            resp_data = JCLBean_pb2.sort_ex_hq_ans()
            resp_data.ParseFromString(resp.MsgData)
            return resp_data

        except Exception as e:
            self.logger.error(f"请求分类排序行情失败: {e}")
            return None

    def get_hqfd_req(self) -> Optional[JCLBean_pb2.CalcStatics_Agfd_AnsInfo]:
        """
        获取行情涨幅分段信息
        :return: 行情涨幅分段信息 (JCLBean_pb2.CalcStatics_Agfd_AnsInfo)
        """
        if not self.is_auth:
            self.logger.error("未认证，请先调用auth()")
            return None

        try:
            # 发送请求，假设 MsgID 为 20401（需根据实际协议确认）
            if not self._send_msg(20401):
                self.logger.error("发送请求失败")
                return None

            # 接收并解析响应
            resp = self._recv_msg()
            if not resp:
                self.logger.error("未收到响应")
                return None

            resp_data = JCLBean_pb2.CalcStatics_Agfd_AnsInfo()
            resp_data.ParseFromString(resp.MsgData)
            return resp_data

        except Exception as e:
            self.logger.error(f"获取行情涨幅分段信息失败: {e}")
            return None

    def get_moneyflow_req(self, code: str = "600000", setcode: int = 0, offset: int = 0, num: int = 10) -> Optional[
        Dict[str, Any]]:
        """
        获取资金流向数据
        :param code: 证券代码
        :param setcode: 市场代码
        :param offset: 数据偏移量
        :param num: 获取的数量
        :return: 资金流向数据字典，包含 amt、vol、cnt
        """
        if not self.is_auth:
            self.logger.error("未认证，请先调用auth()")
            return None

        try:
            # 构造嵌套结构 tagCodeWithNkey
            tag_code_with_nkey = JCLBean_pb2.tagCodeWithNkey()
            tag_code_with_nkey.code = code
            tag_code_with_nkey.setcode = setcode
            tag_code_with_nkey.nkey = 0  # 假设默认值为 0

            # 构造请求数据
            req = JCLBean_pb2.moneyflow_req()
            req.code.CopyFrom(tag_code_with_nkey)
            req.offset = offset
            req.num = num

            # 发送请求，假设 MsgID 为 1805（需根据实际协议确认）
            if not self._send_msg(1805, req):
                self.logger.error("发送请求失败")
                return None

            # 接收并解析响应
            resp = self._recv_msg()
            if not resp:
                self.logger.error("未收到响应")
                return None

            resp_data = JCLBean_pb2.moneyflow_ans()
            resp_data.ParseFromString(resp.MsgData)
            return resp_data

        except Exception as e:
            self.logger.error(f"获取资金流向数据失败: {e}")
            return None



    def get_money_flow_day(self, code: str = "600000", setcode: int = 0, offset: int = 0, num: int = 10) -> Optional[
        JCLBean_pb2.moneyflow_ans]:
        """
        获取日周期资金流向统计数据

        Args:
            code (str): 证券代码，默认为 "600000"
            setcode (int): 市场代码，默认为 0
            offset (int): 数据偏移量，从第几条数据开始，默认为 0
            num (int): 请求的数据条数，默认为 10

        Returns:
            Optional[JCLBean_pb2.moneyflow_ans]: 包含资金流向统计数据的 moneyflow_ans 对象，如果失败则返回 None
        """
        if not self.is_auth:
            self.logger.error("未认证，请先调用auth()")
            return None

        try:
            # 构造请求消息
            tag_code_with_nkey = JCLBean_pb2.tagCodeWithNkey(code=code, setcode=setcode, nkey=0)
            req = JCLBean_pb2.moneyflow_req(
                code=tag_code_with_nkey,
                offset=offset,
                num=num
            )

            # 发送请求，MsgID 为 23201
            if not self._send_msg(23201, req):
                self.logger.error("发送资金流向请求失败")
                return None

            # 接收响应
            resp = self._recv_msg()
            if not resp:
                self.logger.error("未收到资金流向响应")
                return None

            self.logger.info(f"收到资金流向响应, MsgID={resp.Head.MsgID}, MsgData长度={len(resp.MsgData)}")

            # 调试：打印原始字节数据（前100字节，避免日志过长）
            self.logger.debug(f"原始MsgData (前100字节): {resp.MsgData[:100].hex()}")

            # 解析响应消息
            resp_data = JCLBean_pb2.moneyflow_ans()
            try:
                resp_data.ParseFromString(resp.MsgData)
                self.logger.info(f"成功解析moneyflow_ans，资金流向条目数: {len(resp_data.amt)}")
            except Exception as e:
                self.logger.error(f"解析moneyflow_ans失败: {e}")
                # 尝试打印部分数据以帮助调试
                self.logger.debug(f"MsgData样本: {resp.MsgData[:50].hex()}")
                return None

            # 返回完整的 moneyflow_ans 对象
            return resp_data

        except Exception as e:
            self.logger.error(f"获取日周期资金流向数据失败: {e}")
            return None

    def get_multi_hq(self, codes: List[Dict[str, Any]]) -> Optional[JCLBean_pb2.multi_hq_ans]:
        """
        请求指定若干品种的行情数据

        Args:
            codes: 包含证券信息的列表，每个元素是一个字典，例如 [{"code": "600000", "setcode": 0, "nkey": 0}, ...]

        Returns:
            Optional[JCLBean_pb2.multi_hq_ans]: multi_hq_ans 对象，如果失败则返回 None
        """
        if not self.is_auth:
            self.logger.error("未认证，请先调用auth()")
            return None

        try:
            # 构造请求消息
            req = JCLBean_pb2.multi_hq_req()
            for code_info in codes:
                tag_code = req.codes.add()
                tag_code.nkey = int(code_info.get("nkey", 0))  # 转换为整数，确保兼容 uint64
                tag_code.setcode = code_info.get("setcode", 0)
                tag_code.code = code_info.get("code", "")

            # 使用带响应时间记录的API调用
            def parse_response(resp):
                if not resp:
                    self.logger.error("未收到行情响应")
                    return None

                self.logger.info(f"收到行情响应, MsgID={resp.Head.MsgID}, MsgData长度={len(resp.MsgData)}")
                self.logger.debug(f"原始MsgData (前100字节): {resp.MsgData[:100].hex()}")

                # 解析响应消息
                hq_data = JCLBean_pb2.multi_hq_ans()
                try:
                    hq_data.ParseFromString(resp.MsgData)
                    self.logger.info(f"成功解析multi_hq_ans，行情条目数: {len(hq_data.ahq)}")
                    return hq_data
                except Exception as e:
                    self.logger.error(f"解析multi_hq_ans失败: {e}")
                    self.logger.debug(f"MsgData样本: {resp.MsgData[:50].hex()}")
                    return None

            codes_str = ",".join([c.get("code", "") for c in codes[:3]])  # 只显示前3个代码
            if len(codes) > 3:
                codes_str += f"...({len(codes)}个)"

            result, response_time = self._api_call_with_timing(
                api_name=f"get_multi_hq({codes_str})",
                msg_id=1728,
                msg_data=req,
                response_parser=parse_response
            )

            return result

        except Exception as e:
            self.logger.error(f"获取多品种行情数据失败: {e}")
            return None

    def get_his_minute_data(self, code: str = "600000", setcode: int = 0, date: int = 0) -> Optional[List[JCLBean_pb2.MinuteInfo]]:
        """
        获取历史分时走势数据

        Args:
            code (str): 证券代码，默认为 "600000"
            setcode (int): 市场代码，默认为 0
            date (int): 查询日期（格式如 20250314，若为 0 则使用默认值由服务器决定），默认为 0

        Returns:
            Optional[List[JCLBean_pb2.MinuteInfo]]: 历史分时数据列表，失败时返回 None
        """
        if not self.is_auth:
            self.logger.error("未认证，请先调用auth()")
            return None

        try:
            # 构造请求数据
            tag_code_with_nkey = JCLBean_pb2.tagCodeWithNkey(code=code, setcode=setcode, nkey=0)
            req = JCLBean_pb2.his_minute_req(date=date, code=tag_code_with_nkey)

            # 发送请求，MsgID 为 4068
            if not self._send_msg(4068, req):
                self.logger.error("发送历史分时数据请求失败")
                return None

            # 接收响应
            resp = self._recv_msg()
            if not resp:
                self.logger.error("未收到历史分时数据响应")
                return None

            self.logger.info(f"收到历史分时数据响应, MsgID={resp.Head.MsgID}, MsgData长度={len(resp.MsgData)}")

            # 调试：打印原始字节数据（前100字节，避免日志过长）
            self.logger.debug(f"原始MsgData (前100字节): {resp.MsgData[:100].hex()}")

            # 解析响应消息
            his_minute_ans = JCLBean_pb2.his_minute_ans()
            try:
                his_minute_ans.ParseFromString(resp.MsgData)
                self.logger.info(f"成功解析his_minute_ans，分钟数据条目数: {len(his_minute_ans.minutes)}")
            except Exception as e:
                self.logger.error(f"解析his_minute_ans失败: {e}")
                self.logger.debug(f"MsgData样本: {resp.MsgData[:50].hex()}")
                return None

            # 返回历史分时数据列表
            return his_minute_ans if his_minute_ans else None

        except Exception as e:
            self.logger.error(f"获取历史分时数据失败: {e}")
            return None

    def get_kline_data(self, code: str, setcode: int = 0, period: int = 1, mulnum: int = 1, offset: int = 0,
                       num: int = 100, tqflag: int = 0, exhq: int = 0) -> Optional[JCLBean_pb2.kline_new_ans]:
        """
        请求K线数据（日K、周K、月K等）

        Args:
            code (str): 证券代码，例如 "300115"
            setcode (int): 市场代码，默认为 0
            period (int): K线周期（1=日K，2=周K，3=月K等），默认为 1
            mulnum (int): 倍数，默认为 1
            offset (int): 偏移量，默认为 0
            num (int): 请求的数据条数，默认为 100
            tqflag (int): 复权标志（0=不复权，1=前复权，2=后复权等），默认为 0
            exhq (int): 是否包含扩展行情数据（0=否，1=是），默认为 0

        Returns:
            Optional[JCLBean_pb2.kline_new_ans]: K线数据对象，包含 aK（K线列表）和 hq（当前行情），失败时返回 None
        """
        if not self.is_auth:
            self.logger.error("未认证，请先调用auth()")
            return None

        try:
            # 构造请求消息
            req = JCLBean_pb2.kline_new_req()
            req.code.code = code
            req.code.setcode = setcode
            req.code.nkey = 0  # 默认 nkey 为 0，可根据需求调整
            req.period = period
            req.mulnum = mulnum
            req.offset = offset
            req.num = num
            req.tqflag = tqflag
            req.exhq = exhq

            # 发送请求，MsgID 为 22010
            if not self._send_msg(22010, req):
                self.logger.error("发送K线数据请求失败")
                return None

            # 接收响应
            resp = self._recv_msg()
            if not resp:
                self.logger.error("未收到K线数据响应")
                return None

            self.logger.info(f"收到K线数据响应, MsgID={resp.Head.MsgID}, MsgData长度={len(resp.MsgData)}")

            # 调试：打印原始字节数据（前100字节，避免日志过长）
            self.logger.debug(f"原始MsgData (前100字节): {resp.MsgData[:100].hex()}")

            # 解析响应消息
            resp_data = JCLBean_pb2.kline_new_ans()
            try:
                resp_data.ParseFromString(resp.MsgData)
                self.logger.info(f"成功解析kline_new_ans，K线条目数: {len(resp_data.aK)}")
            except Exception as e:
                self.logger.error(f"解析kline_new_ans失败: {e}")
                self.logger.debug(f"MsgData样本: {resp.MsgData[:50].hex()}")
                return None

            # 返回完整的 kline_new_ans 对象
            return resp_data

        except Exception as e:
            self.logger.error(f"获取K线数据失败: {e}")
            return None

    def get_block_xml(self) -> Optional[str]:
        """
        获取 block.xml 数据

        Returns:
            Optional[str]: block.xml 的字符串内容（GBK编码解码后的结果），如果请求失败则返回 None
        """
        if not self.is_auth:
            self.logger.error("未认证，请先调用auth()")
            return None

        try:
            # 请求编号
            req_no = 1360  # COMBBLOCK_NREQ

            # 发送请求，无需请求参数
            if not self._send_msg(req_no):
                self.logger.error("发送 block.xml 请求失败")
                return None

            # 接收响应
            resp = self._recv_msg()
            if not resp:
                self.logger.error("未收到 block.xml 响应")
                return None

            self.logger.info(f"收到 block.xml 响应, MsgID={resp.Head.MsgID}, MsgData长度={len(resp.MsgData)}")

            # 解析响应消息
            block_ans = JCLBean_pb2.Binary_Data_Ans()
            try:
                block_ans.ParseFromString(resp.MsgData)
                self.logger.debug("成功解析 Binary_Data_Ans")
            except Exception as e:
                self.logger.error(f"解析 Binary_Data_Ans 失败: {e}")
                return None

            # 检查返回数据
            if not block_ans.data:
                self.logger.warning("block.xml 返回数据为空")
                return None

            # 解压并解码数据
            try:
                decompressed_data = zlib.decompress(block_ans.data)
                response = decompressed_data.decode('gbk')
                self.logger.info("成功解压并解码 block.xml 数据")
                return response
            except zlib.error as e:
                self.logger.error(f"zlib 解压失败: {e}")
                return None
            except UnicodeDecodeError as e:
                self.logger.error(f"GBK 解码失败: {e}")
                return None

        except Exception as e:
            self.logger.error(f"获取 block.xml 数据失败: {e}")
            return None

    def get_block_sort_hq(self, code: str, setcode: int, coltype: int, startxh: int, wantnum: int, sorttype: int,
                          fieldids: List[int]) -> Optional[Dict]:
        """
        获取板块或指数成分股的排序行情数据

        Args:
            code (str): 板块或指数代码（例如 "000001"）
            setcode (int): 市场代码（例如 1）
            coltype (int): 列类型
            startxh (int): 起始序号（例如 0）
            wantnum (int): 请求的记录数（例如 10）
            sorttype (int): 排序类型（例如 1 表示升序）
            fieldids (List[int]): 请求的字段 ID 列表（例如 [1, 2, 3]）

        Returns:
            Optional[Dict]: 包含排序行情数据的字典，格式为 {"data": [{"nkey": int, "code": str, ...}, ...]}，或 None（如果失败）
        """
        if not self.is_auth:
            self.logger.error("未认证，请先调用auth()")
            return None

        try:
            # 构造 tagCodeWithNkey 对象
            tag_code_with_nkey = JCLBean_pb2.tagCodeWithNkey(code=code, setcode=setcode, nkey=0)

            # 构造请求数据
            req = JCLBean_pb2.sort_block_hq_req(
                code=tag_code_with_nkey,
                coltype=coltype,
                startxh=startxh,
                wantnum=wantnum,
                sorttype=sorttype,
                fieldids=fieldids
            )

            # 发送请求，MsgID 为 2008
            if not self._send_msg(2008, req):
                self.logger.error("发送板块排序行情请求失败")
                return None

            # 接收响应
            resp = self._recv_msg()
            if not resp:

                return None



            # 解析响应
            sort_ex_hq_ans = JCLBean_pb2.sort_ex_hq_ans()
            try:
                sort_ex_hq_ans.ParseFromString(resp.MsgData)
                self.logger.info("成功解析sort_ex_hq_ans")
            except Exception as e:
                self.logger.error(f"解析sort_ex_hq_ans失败，可能是定义不匹配: {str(e)}")

                return None



            return sort_ex_hq_ans

        except Exception as e:
            self.logger.error(f"获取板块排序行情数据失败: {e}")
            return None
