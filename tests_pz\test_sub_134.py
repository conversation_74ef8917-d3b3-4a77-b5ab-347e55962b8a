"""134 测试行情订阅接口"""
# 新接口比老接口快3秒 (此为性能观察备注)

import time
import pytest
from common.assert_response import assert_response_equal, assert_response_equal_with_peratio, assert_list_equal  # 保留导入


class TestSubscribe:

    @pytest.mark.parametrize("nkey", [
        [6582227395838651953],  # nkey 参数本身是一个列表
    ])
    def test_subscribe_hq(self, nkey, api_helper, logger):  # logger 已在参数中
        # 创建参数字典用于日志记录
        params = {'nkey': nkey}

        # 并发执行新旧协议
        response, new_response = api_helper.call_method_concurrently(
            'subscribe_quote', nkey
        )

        # 获取响应时间和服务器信息
        response_times = api_helper.get_last_response_times()
        server_info = api_helper.get_last_server_info()

        all_errors = []
        # 新老接口字段逐个校验
        fields = ['nkey', 'Status','QuoteTime','InOutFlag','TickCount','PreClosePrice','OpenPrice','HighPrice',
                  'LowPrice','NowPrice','zangsu','PriceDiff','LimitUpPrice','LimitDownPrice'
                  ,'Volume','NowVol','Amount','NowAmount','Inside','Outside']

        fields_list = ['BuyPrice', 'BuyVolume', 'SellPrice', 'SellVolume']

        try:
            print(f"老接口响应：{response.ahq[0]}-新接口响应：{new_response.ahq[0]}-比对字段{fields}")
            assert_response_equal_with_peratio(response.ahq[0], new_response.ahq[0], fields)
            assert round(response.ahq[0].AveragePrice, 2) == round(new_response.ahq[0].AveragePrice, 2)
            assert_list_equal(response.ahq[0], new_response.ahq[0], fields_list, slice_length=5)

        except Exception as e:
            # 收集错误但不立即记录日志
            all_errors.append(str(e))  # 保存错误信息

        # 使用新的日志记录规则
        logger.log_test_result(
            server_info=server_info,
            response_times=response_times,
            request_params=params,
            has_errors=bool(all_errors),
            error_details=all_errors
        )

        # 循环结束后，如果有错误则统一抛出
        if all_errors:
            # 合并所有错误信息
            combined_error = "\n".join([f"共发现 {len(all_errors)} 处断言失败:"] + all_errors)
            raise AssertionError(combined_error)