"""测试基类模块"""

import pytest
from typing import Dict, List, Any, Optional, Tu<PERSON>
from config import ASSERT_CONFIG
from common.assert_response import assert_response_equal, assert_fieldids, assert_round_response


class BaseAPITest:
    """API测试基类"""

    def setup_method(self):
        """测试方法初始化"""
        self.all_errors = []
        self.test_params = {}

    def create_params_dict(self, **kwargs) -> Dict[str, Any]:
        """创建参数字典"""
        return {k: v for k, v in kwargs.items() if v is not None}
    
    def execute_concurrent_test(self, api_helper, method_name: str = None, old_method: str = None,
                               new_method: str = None, **params) -> Tuple[Any, Any, Dict, Dict]:
        """执行并发测试的通用方法"""
        # 存储测试参数
        self.test_params = self.create_params_dict(**params)

        # 并发调用新旧接口
        if method_name:
            response, new_response = api_helper.call_method_concurrently(
                method_name, **params
            )
        elif old_method and new_method:
            response, new_response = api_helper.call_method_concurrently(
                None, **params,
                old_method=old_method, new_method=new_method
            )
        else:
            raise ValueError("必须提供 method_name 或者同时提供 old_method 和 new_method")

        # 获取响应时间和服务器信息
        response_times = api_helper.get_last_response_times()
        server_info = api_helper.get_last_server_info()

        return response, new_response, response_times, server_info
    
    def assert_and_log(
        self,
        response: Any,
        new_response: Any,
        fields: List[str],
        logger,
        response_times: Dict,
        server_info: Dict,
        assert_type: str = "equal",
        **assert_kwargs
    ):
        """
        执行断言并记录日志的通用方法
        
        Args:
            response: 旧接口响应
            new_response: 新接口响应
            fields: 要比较的字段列表
            logger: 日志记录器
            response_times: 响应时间信息
            server_info: 服务器信息
            assert_type: 断言类型 ("equal", "fieldids", "round")
            **assert_kwargs: 断言函数的额外参数
        """
        try:
            # 根据断言类型选择相应的断言函数
            if assert_type == "equal":
                assert_response_equal(
                    response, new_response, fields,
                    request_params=self.test_params,
                    server_info=server_info,
                    logger=logger,
                    response_times=response_times,
                    **assert_kwargs
                )
            elif assert_type == "fieldids":
                assert_fieldids(
                    response, new_response, fields,
                    request_params=self.test_params,
                    server_info=server_info,
                    logger=logger,
                    response_times=response_times,
                    **assert_kwargs
                )
            elif assert_type == "round":
                assert_round_response(
                    response, new_response, fields,
                    request_params=self.test_params,
                    server_info=server_info,
                    logger=logger,
                    response_times=response_times,
                    **assert_kwargs
                )
            elif assert_type == "batch":
                # 使用统一断言模块处理批量比较
                from common.unified_assert import assert_unified
                assert_unified(
                    response, new_response, fields, assert_type,
                    request_params=self.test_params,
                    server_info=server_info,
                    logger=logger,
                    response_times=response_times,
                    **assert_kwargs
                )
            else:
                raise ValueError(f"不支持的断言类型: {assert_type}")
                
        except AssertionError as e:
            # 收集错误信息
            self.all_errors.append(str(e))
        
        # 记录测试结果
        logger.log_test_result(
            server_info=server_info,
            response_times=response_times,
            request_params=self.test_params,
            has_errors=bool(self.all_errors),
            error_details=self.all_errors
        )
        
        # 如果有错误，抛出汇总的断言错误
        if self.all_errors:
            combined_error = "\n".join([f"共发现 {len(self.all_errors)} 处错误:"] + self.all_errors)
            raise AssertionError(combined_error)
    
    def get_field_config(self, field_type: str) -> List[str]:
        """获取字段配置 - 提供默认字段配置"""
        default_fields = {
            "hq_fields": [
                'Status', 'QuoteTime', 'InOutFlag', 'TickCount', 'PreClosePrice', 'OpenPrice',
                'HighPrice', 'LowPrice', 'NowPrice', 'zangsu', 'AveragePrice', 'PriceDiff',
                'LimitUpPrice', 'LimitDownPrice', 'PERatio', 'Volume', 'NowVol', 'Amount',
                'NowAmount', 'Inside', 'Outside', 'BuyPrice', 'BuyVolume', 'SellPrice', 'SellVolume'
            ],
            "kline_fields": ['fOpen', 'fHigh', 'fLow', 'fClose', 'YClose', 'fAmount', 'dVolume'],
            "time_fields": ['jclTime'],
            "sort_fields": ['value']
        }
        return default_fields.get(field_type, [])

    def get_common_stocks(self, market: str = "all") -> List[str]:
        """获取常用股票代码 - 提供默认股票代码"""
        default_stocks = {
            "sz_stocks": ["000001", "300115", "300001", "002490"],
            "sh_stocks": ["600000", "601318", "688001", "603259"],
            "bj_stocks": ["830001", "830002"]
        }
        if market == "all":
            return default_stocks["sz_stocks"] + default_stocks["sh_stocks"]
        return default_stocks.get(market, [])


class HQTestBase(BaseAPITest):
    """行情测试基类"""
    
    def get_hq_fields(self) -> List[str]:
        """获取行情字段"""
        return self.get_field_config("hq_fields")
    
    def run_hq_api_test(
        self,
        api_helper,
        logger,
        method_name: str,
        codes: List[Dict],
        exclude_fields: Optional[List[str]] = None
    ):
        """
        通用的行情API测试方法

        Args:
            api_helper: API调用助手
            logger: 日志记录器
            method_name: API方法名
            codes: 股票代码列表
            exclude_fields: 要排除的字段列表
        """
        # 执行并发测试
        response, new_response, response_times, server_info = self.execute_concurrent_test(
            api_helper, method_name, codes=codes
        )
        
        # 获取字段列表
        fields = self.get_hq_fields()
        if exclude_fields:
            fields = [f for f in fields if f not in exclude_fields]
        
        print(f"请求参数: {self.test_params}")

        # 行情数据使用批量断言，因为数据在ahq属性中
        self.assert_and_log(
            response, new_response, fields, logger,
            response_times, server_info, "batch",
            response_attr="ahq"  # 指定行情数据属性
        )


class KlineTestBase(BaseAPITest):
    """K线测试基类"""

    def get_kline_fields(self) -> List[str]:
        """获取K线字段"""
        return self.get_field_config("kline_fields")

    def get_time_fields(self) -> List[str]:
        """获取时间字段"""
        return self.get_field_config("time_fields")

    def run_kline_api_test(
        self,
        api_helper,
        logger,
        method_name: str = None,
        old_method: str = None,
        new_method: str = None,
        **params
    ):
        """
        通用的K线API测试方法
        """
        # 执行并发测试
        response, new_response, response_times, server_info = self.execute_concurrent_test(
            api_helper, method_name=method_name, old_method=old_method, new_method=new_method, **params
        )

        # 获取K线字段和时间字段
        kline_fields = self.get_kline_fields()
        time_fields = self.get_time_fields()

        print(f"请求参数: {self.test_params}")

        # K线数据使用批量断言，因为数据在aK属性中
        self.assert_and_log(
            response, new_response, kline_fields + time_fields, logger,
            response_times, server_info, "batch",
            response_attr="aK"  # 指定K线数据属性
        )


class SortTestBase(BaseAPITest):
    """排序测试基类"""

    def get_sort_fields(self) -> List[str]:
        """获取排序字段"""
        return self.get_field_config("sort_fields")

    def run_sort_api_test(
        self,
        api_helper,
        logger,
        method_name: str,
        **params
    ):
        """
        通用的排序API测试方法
        """
        # 执行并发测试
        response, new_response, response_times, server_info = self.execute_concurrent_test(
            api_helper, method_name, **params
        )

        # 获取排序字段
        fields = self.get_sort_fields()

        print(f"请求参数: {self.test_params}")

        # 执行断言和日志记录
        self.assert_and_log(
            response, new_response, fields, logger,
            response_times, server_info, "fieldids"
        )
