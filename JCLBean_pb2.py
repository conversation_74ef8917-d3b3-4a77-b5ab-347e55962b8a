# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: JCLBean.proto
# Protobuf Python Version: 5.29.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    2,
    '',
    'JCLBean.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import any_pb2 as google_dot_protobuf_dot_any__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\rJCLBean.proto\x12\x10wuhan.jcl.report\x1a\x19google/protobuf/any.proto\"\x87\x01\n\x0ereport_package\x12\x0f\n\x07Version\x18\x01 \x01(\r\x12\x0e\n\x06\x63ookie\x18\x02 \x01(\r\x12\x0e\n\x06MainID\x18\x03 \x01(\r\x12\x0f\n\x07\x41ssisID\x18\x04 \x01(\r\x12\x0b\n\x03req\x18\x05 \x01(\r\x12&\n\x08packdata\x18\x06 \x01(\x0b\x32\x14.google.protobuf.Any\"M\n\x15tagReportPackageArray\x12\x34\n\nreportpack\x18\x01 \x03(\x0b\x32 .wuhan.jcl.report.report_package\"\x15\n\x08tagInt32\x12\t\n\x01i\x18\x01 \x01(\r\"\x15\n\x08tagInt64\x12\t\n\x01i\x18\x01 \x01(\x04\"\x15\n\x08tagFloat\x12\t\n\x01\x66\x18\x01 \x01(\x02\"\x16\n\ttagDouble\x12\t\n\x01\x64\x18\x01 \x01(\x01\"\x1a\n\rtagInt32Array\x12\t\n\x01i\x18\x01 \x03(\r\"\x1a\n\rtagInt64Array\x12\t\n\x01i\x18\x01 \x03(\x04\"\x1a\n\rtagFloatArray\x12\t\n\x01\x66\x18\x01 \x03(\x02\"\x1b\n\x0etagDoubleArray\x12\t\n\x01\x64\x18\x01 \x03(\x01\"\x16\n\ttagString\x12\t\n\x01s\x18\x01 \x01(\t\">\n\x0ftagCodeWithNkey\x12\x0c\n\x04nkey\x18\x01 \x01(\x04\x12\x0f\n\x07setcode\x18\x02 \x01(\x05\x12\x0c\n\x04\x63ode\x18\x03 \x01(\t\"G\n\x14tagCodeWithNkeyArray\x12/\n\x04\x63ode\x18\x01 \x03(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\"\x86\x01\n\x07\x42SQueue\x12\r\n\x05total\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\x12\x0b\n\x03\x61\x64\x64\x18\x03 \x01(\r\x12\x10\n\x08\x63omplete\x18\x04 \x01(\r\x12\x0e\n\x06update\x18\x05 \x01(\r\x12\x0e\n\x06\x63\x61ncel\x18\x06 \x01(\r\x12\x0f\n\x07volumes\x18\x07 \x03(\x01\x12\r\n\x05state\x18\x08 \x03(\r\"(\n\tNewBroker\x12\x0b\n\x03pos\x18\x01 \x01(\r\x12\x0e\n\x06\x62roker\x18\x02 \x01(\x04\"k\n\x12NewMDHKBrokerQueue\x12\x14\n\x0creceive_time\x18\x01 \x01(\x03\x12\x12\n\nbroker_cnt\x18\x02 \x01(\r\x12+\n\x06\x62roker\x18\x03 \x03(\x0b\x32\x1b.wuhan.jcl.report.NewBroker\"\xc4\x0b\n\x10\x43urrStockDataBEx\x12\x0c\n\x04nkey\x18\x01 \x01(\x04\x12\x0e\n\x06Source\x18\x02 \x01(\r\x12\x0e\n\x06Status\x18\x03 \x01(\r\x12\x11\n\tQuoteTime\x18\x04 \x01(\x04\x12\x11\n\tInOutFlag\x18\x05 \x01(\r\x12\x11\n\tTickCount\x18\x06 \x01(\r\x12\x0f\n\x07HqCount\x18\x07 \x01(\r\x12\x12\n\nTradeCount\x18\x08 \x01(\x04\x12\x15\n\rPreClosePrice\x18\t \x01(\x01\x12\x11\n\tOpenPrice\x18\n \x01(\x01\x12\x11\n\tHighPrice\x18\x0b \x01(\x01\x12\x10\n\x08LowPrice\x18\x0c \x01(\x01\x12\x10\n\x08NowPrice\x18\r \x01(\x01\x12\x0e\n\x06zangsu\x18\x0e \x01(\x01\x12\x14\n\x0c\x41veragePrice\x18\x0f \x01(\x01\x12\x11\n\tPriceDiff\x18\x10 \x01(\x01\x12\x14\n\x0cLimitUpPrice\x18\x11 \x01(\x01\x12\x16\n\x0eLimitDownPrice\x18\x12 \x01(\x01\x12\x10\n\x08TaxPrice\x18\x13 \x01(\x01\x12\x17\n\x0f\x41verageTaxPrice\x18\x14 \x01(\x01\x12\x16\n\x0ePreSettlePrice\x18\x15 \x01(\x01\x12\x13\n\x0bSettlePrice\x18\x16 \x01(\x01\x12\x0f\n\x07PERatio\x18\x17 \x01(\x01\x12\x0e\n\x06Volume\x18\x18 \x01(\x01\x12\x0e\n\x06NowVol\x18\x19 \x01(\x01\x12\x0e\n\x06\x41mount\x18\x1a \x01(\x01\x12\x11\n\tNowAmount\x18\x1b \x01(\x01\x12\x0e\n\x06Inside\x18\x1c \x01(\x01\x12\x0f\n\x07Outside\x18\x1d \x01(\x01\x12\x15\n\rPreVolInStock\x18\x1e \x01(\x01\x12\x12\n\nVolInStock\x18\x1f \x01(\x01\x12\x16\n\x0eVolInStockDiff\x18  \x01(\x01\x12\x16\n\x0eTotalBuyVolume\x18! \x01(\x01\x12\x17\n\x0fTotalSellVolume\x18\" \x01(\x01\x12\x17\n\x0f\x42uyAveragePrice\x18# \x01(\x01\x12\x18\n\x10SellAveragePrice\x18$ \x01(\x01\x12\x18\n\x10\x41llBuyPriceCount\x18% \x01(\r\x12\x14\n\x0c\x42uyTickCount\x18& \x01(\r\x12\x19\n\x11\x41llSellPriceCount\x18\' \x01(\r\x12\x15\n\rSellTickCount\x18( \x01(\r\x12\x10\n\x08\x42uyPrice\x18) \x03(\x01\x12\x11\n\tBuyVolume\x18* \x03(\x01\x12\x11\n\tSellPrice\x18+ \x03(\x01\x12\x12\n\nSellVolume\x18, \x03(\x01\x12+\n\x08\x42uyQueue\x18- \x01(\x0b\x32\x19.wuhan.jcl.report.BSQueue\x12,\n\tSellQueue\x18. \x01(\x0b\x32\x19.wuhan.jcl.report.BSQueue\x12\x13\n\x0bPreNetValue\x18/ \x01(\x01\x12\x10\n\x08NetValue\x18\x30 \x01(\x01\x12\x14\n\x0c\x45TFBuyNumber\x18\x31 \x01(\r\x12\x15\n\rETFSellNumber\x18\x32 \x01(\r\x12\x14\n\x0c\x45TFBuyVolume\x18\x33 \x01(\x01\x12\x15\n\rETFSellVolume\x18\x34 \x01(\x01\x12\x14\n\x0c\x45TFBuyAmount\x18\x35 \x01(\x01\x12\x15\n\rETFSellAmount\x18\x36 \x01(\x01\x12\x10\n\x08PreYield\x18\x37 \x01(\x01\x12\r\n\x05Yield\x18\x38 \x01(\x01\x12\x0c\n\x04Lead\x18\x39 \x01(\x01\x12\x14\n\x0cIndexUpCount\x18: \x01(\r\x12\x17\n\x0fIndexLevelCount\x18; \x01(\r\x12\x16\n\x0eIndexDownCount\x18< \x01(\r\x12\x11\n\tWarnCount\x18= \x01(\r\x12<\n\x0e\x42uyBrokerQueue\x18> \x01(\x0b\x32$.wuhan.jcl.report.NewMDHKBrokerQueue\x12=\n\x0fSellBrokerQueue\x18? \x01(\x0b\x32$.wuhan.jcl.report.NewMDHKBrokerQueue\x12\x14\n\x0c\x42uyBrokerNum\x18@ \x03(\r\x12\x15\n\rSellBrokerNum\x18\x41 \x03(\r\"^\n\rPriceVolTable\x12\r\n\x05Price\x18\x01 \x01(\x01\x12\x0e\n\x06Volume\x18\x02 \x01(\x01\x12\x0f\n\x07Outside\x18\x03 \x01(\x01\x12\x0e\n\x06Inside\x18\x04 \x01(\x01\x12\r\n\x05\x43ount\x18\x05 \x01(\r\"\xd9\x02\n\nMinuteInfo\x12\x0f\n\x07jclTime\x18\x01 \x01(\x04\x12\x10\n\x08preClose\x18\x02 \x01(\x01\x12\x11\n\topenPrice\x18\x03 \x01(\x01\x12\x11\n\thighPrice\x18\x04 \x01(\x01\x12\x10\n\x08nowPrice\x18\x05 \x01(\x01\x12\x10\n\x08lowPrice\x18\x06 \x01(\x01\x12\x10\n\x08\x61vePrice\x18\x07 \x01(\x01\x12\x0e\n\x06\x61mount\x18\x08 \x01(\x01\x12\x0e\n\x06volume\x18\t \x01(\x01\x12\n\n\x02up\x18\n \x01(\r\x12\x0c\n\x04\x64own\x18\x0b \x01(\r\x12\x0b\n\x03\x65qu\x18\x0c \x01(\r\x12\x0c\n\x04lead\x18\r \x01(\x01\x12\x0e\n\x06\x62uyvol\x18\x0e \x01(\x01\x12\x0f\n\x07sellvol\x18\x0f \x01(\x01\x12\x15\n\rpreVolInStock\x18\x10 \x01(\x01\x12\x12\n\nvolInStock\x18\x11 \x01(\x01\x12\x16\n\x0epreSettlePrice\x18\x12 \x01(\x01\x12\x13\n\x0bsettlePrice\x18\x13 \x01(\x01\"\xd2\x01\n\nAnalyDataB\x12\x11\n\tdwItemNum\x18\x01 \x01(\r\x12\x0f\n\x07jclTime\x18\x02 \x01(\x04\x12\r\n\x05\x66Open\x18\x03 \x01(\x01\x12\r\n\x05\x66High\x18\x04 \x01(\x01\x12\x0c\n\x04\x66Low\x18\x05 \x01(\x01\x12\x0e\n\x06\x66\x43lose\x18\x06 \x01(\x01\x12\x0f\n\x07\x66\x41mount\x18\x07 \x01(\x01\x12\x0f\n\x07\x64Volume\x18\x08 \x01(\x01\x12\x0b\n\x03\x43\x43L\x18\t \x01(\x01\x12\x0b\n\x03JSJ\x18\n \x01(\x01\x12\x0e\n\x06YClose\x18\x0b \x01(\x01\x12\n\n\x02up\x18\x0c \x01(\r\x12\x0c\n\x04\x64own\x18\r \x01(\r\"7\n\x06PKData\x12\r\n\x05price\x18\x01 \x01(\x01\x12\x0b\n\x03vol\x18\x02 \x01(\x01\x12\x11\n\tnumTrades\x18\x03 \x01(\r\"\xa1\x01\n\x08TickInfo\x12\x11\n\tquoteTime\x18\x01 \x01(\x04\x12\r\n\x05price\x18\x02 \x01(\x01\x12\x0b\n\x03vol\x18\x03 \x01(\x01\x12\x11\n\tunSuitVol\x18\x04 \x01(\x01\x12\x0f\n\x07volDiff\x18\x05 \x01(\x01\x12\x0c\n\x04\x66lag\x18\x06 \x01(\r\x12\x0f\n\x07\x62uy_num\x18\x07 \x01(\x04\x12\x10\n\x08sell_num\x18\x08 \x01(\x04\x12\x11\n\tnumtrades\x18\t \x01(\r\"\x84\x01\n\x07\x43\x44PInfo\x12\x0b\n\x03\x63\x64p\x18\x01 \x01(\x01\x12\n\n\x02nh\x18\x02 \x01(\x01\x12\n\n\x02nl\x18\x03 \x01(\x01\x12\n\n\x02\x61h\x18\x04 \x01(\x01\x12\n\n\x02\x61l\x18\x05 \x01(\x01\x12\x0e\n\x06\x64kflag\x18\x06 \x01(\r\x12\x0b\n\x03tbp\x18\x07 \x01(\x01\x12\x10\n\x08stoplost\x18\x08 \x01(\x01\x12\r\n\x05leave\x18\t \x01(\x01\".\n\terror_ans\x12\x10\n\x08\x65rrreqno\x18\x01 \x01(\r\x12\x0f\n\x07\x65rrinfo\x18\x02 \x01(\t\"\xc1\x04\n\x0fmonitorinfo_ans\x12\x11\n\tClientNum\x18\x01 \x01(\r\x12\x15\n\rMaxConnectNum\x18\x02 \x01(\r\x12\x12\n\nPackageNum\x18\x03 \x01(\r\x12\x11\n\tHasStatus\x18\x04 \x01(\r\x12\x0e\n\x06HasLog\x18\x05 \x01(\r\x12\x0b\n\x03\x62HQ\x18\x06 \x01(\r\x12\x0b\n\x03\x62WT\x18\x07 \x01(\r\x12\x11\n\tstarttime\x18\x08 \x03(\r\x12\x0f\n\x07HostVer\x18\t \x01(\t\x12\x13\n\x0bProtocolVer\x18\n \x01(\r\x12\x16\n\x0eTotalClientNum\x18\x0b \x01(\r\x12\x15\n\rUsedClientNum\x18\x0c \x01(\r\x12\x11\n\tbAutoBase\x18\r \x01(\r\x12\x10\n\x08HomePath\x18\x0e \x01(\t\x12\x12\n\nNetCardStr\x18\x0f \x01(\t\x12\x0f\n\x07InfDate\x18\x10 \x01(\r\x12\x0e\n\x06InfHms\x18\x11 \x01(\r\x12\x10\n\x08HostType\x18\x12 \x01(\r\x12\x10\n\x08ProcType\x18\x13 \x01(\r\x12\x14\n\x0c\x43ompressType\x18\x14 \x01(\r\x12\x11\n\tbDayToMem\x18\x15 \x01(\r\x12\x12\n\nbWeekToMem\x18\x16 \x01(\r\x12\x11\n\tbMonToMem\x18\x17 \x01(\r\x12\x11\n\tbMinToMem\x18\x18 \x01(\r\x12\x11\n\tbKExplain\x18\x19 \x01(\r\x12\x0e\n\x06\x62\x41larm\x18\x1a \x01(\r\x12\x0f\n\x07\x62HqStat\x18\x1b \x01(\r\x12\x12\n\nProcessNum\x18\x1c \x01(\r\x12\x11\n\tThreadNum\x18\x1d \x01(\r\x12\x0f\n\x07unused2\x18\x1e \x03(\r\"c\n\nCqcxCWInfo\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12$\n\x02\x63w\x18\x02 \x03(\x0b\x32\x18.wuhan.jcl.report.CWInfo\"X\n\x06\x43WInfo\x12\x0c\n\x04\x44\x61te\x18\x01 \x01(\r\x12\x0c\n\x04Type\x18\x02 \x01(\r\x12\x0b\n\x03V01\x18\x03 \x01(\x01\x12\x0b\n\x03V02\x18\x04 \x01(\x01\x12\x0b\n\x03V03\x18\x05 \x01(\x01\x12\x0b\n\x03V04\x18\x06 \x01(\x01\"\xe3\x05\n\nBaseCWInfo\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\r\n\x05Yield\x18\x02 \x01(\x01\x12\x15\n\rActiveCapital\x18\x03 \x01(\x01\x12\x14\n\x0cGrossCapital\x18\x04 \x01(\x01\x12\x0c\n\x04shzt\x18\x05 \x01(\r\x12\x0f\n\x07startrq\x18\x06 \x01(\r\x12\x0c\n\x04gxrq\x18\x07 \x01(\r\x12\x0e\n\x06lastrq\x18\x08 \x01(\r\x12\x0b\n\x03\x66xj\x18\t \x01(\x01\x12\r\n\x05tsspj\x18\n \x01(\x01\x12\x0c\n\x04yycb\x18\x0b \x01(\x01\x12\x0b\n\x03zgb\x18\x0c \x01(\x01\x12\x0b\n\x03gjg\x18\r \x01(\x01\x12\x0e\n\x06\x66qrfrg\x18\x0e \x01(\x01\x12\x0b\n\x03\x66rg\x18\x0f \x01(\x01\x12\n\n\x02\x62g\x18\x10 \x01(\x01\x12\n\n\x02hg\x18\x11 \x01(\x01\x12\x0b\n\x03zgg\x18\x12 \x01(\x01\x12\x0b\n\x03zzc\x18\x13 \x01(\x01\x12\x0c\n\x04ldzc\x18\x14 \x01(\x01\x12\x0c\n\x04gdzc\x18\x15 \x01(\x01\x12\x0c\n\x04wxzc\x18\x16 \x01(\x01\x12\x0c\n\x04\x63qtz\x18\x17 \x01(\x01\x12\x0c\n\x04ldfz\x18\x18 \x01(\x01\x12\x0c\n\x04\x63qfz\x18\x19 \x01(\x01\x12\r\n\x05zbgjj\x18\x1a \x01(\x01\x12\x0b\n\x03jzc\x18\x1b \x01(\x01\x12\x0c\n\x04zysy\x18\x1c \x01(\x01\x12\x0c\n\x04zyly\x18\x1d \x01(\x01\x12\x0c\n\x04qtly\x18\x1e \x01(\x01\x12\x0c\n\x04yyly\x18\x1f \x01(\x01\x12\x0c\n\x04tzsy\x18  \x01(\x01\x12\x0c\n\x04\x62tsy\x18! \x01(\x01\x12\r\n\x05yywsz\x18\" \x01(\x01\x12\x0e\n\x06snsytz\x18# \x01(\x01\x12\x0c\n\x04lyze\x18$ \x01(\x01\x12\x0c\n\x04shly\x18% \x01(\x01\x12\x0b\n\x03jly\x18& \x01(\x01\x12\r\n\x05wfply\x18\' \x01(\x01\x12\x0e\n\x06tzmgjz\x18( \x01(\x01\x12\x14\n\x0cHalfYearFlag\x18) \x01(\x01\x12\x0c\n\x04prov\x18* \x01(\r\x12\n\n\x02hy\x18+ \x01(\r\x12\x0f\n\x07provstr\x18, \x01(\t\x12\x0e\n\x06hangye\x18- \x01(\t\x12\r\n\x05srkpj\x18. \x01(\x01\x12\r\n\x05start\x18/ \x01(\x04\"@\n\x0c\x61utogbbq_req\x12\x30\n\x05\x63odes\x18\x01 \x03(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\":\n\x0c\x61utogbbq_ans\x12*\n\x04\x63qcx\x18\x01 \x03(\x0b\x32\x1c.wuhan.jcl.report.CqcxCWInfo\"@\n\x0c\x61utobase_req\x12\x30\n\x05\x63odes\x18\x01 \x03(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\";\n\x0c\x61utobase_ans\x12+\n\x05\x62\x61sep\x18\x01 \x03(\x0b\x32\x1c.wuhan.jcl.report.BaseCWInfo\"0\n\x0chostmore_req\x12\x0f\n\x07setcode\x18\x01 \x01(\x05\x12\x0f\n\x07verflag\x18\x02 \x01(\r\"\x96\x04\n\rhostmore_info\x12\x0f\n\x07\x65rrflag\x18\x01 \x01(\r\x12\x0f\n\x07setcode\x18\x02 \x01(\x05\x12\x12\n\nepoch_time\x18\x03 \x01(\x04\x12\x0f\n\x07\x64\x61_year\x18\x04 \x01(\r\x12\x0e\n\x06\x64\x61_day\x18\x05 \x01(\r\x12\x0e\n\x06\x64\x61_mon\x18\x06 \x01(\r\x12\x0e\n\x06ti_min\x18\x07 \x01(\r\x12\x0f\n\x07ti_hour\x18\x08 \x01(\r\x12\x0f\n\x07ti_hund\x18\t \x01(\r\x12\x0e\n\x06ti_sec\x18\n \x01(\r\x12\x0e\n\x06\x63ommon\x18\x0b \x03(\r\x12\n\n\x02qt\x18\x0c \x03(\r\x12\x12\n\nbyesterday\x18\r \x01(\r\x12\x14\n\x0c\x63odelisthash\x18\x0e \x01(\x04\x12\x13\n\x0binfcodedate\x18\x0f \x01(\r\x12\x12\n\ninfcodehms\x18\x10 \x01(\r\x12\x0e\n\x06\x62serv1\x18\x11 \x01(\r\x12\x0e\n\x06\x62serv2\x18\x12 \x01(\r\x12\x0e\n\x06\x62serv3\x18\x13 \x01(\r\x12\x12\n\nnUrgentNum\x18\x14 \x01(\r\x12\x12\n\nlinuxcheck\x18\x15 \x01(\r\x12\x0f\n\x07\x62\x62igjbm\x18\x16 \x01(\r\x12\r\n\x05\x62\x35mmp\x18\x17 \x01(\r\x12\x16\n\x0e\x62\x63\x61nuserurgent\x18\x18 \x01(\r\x12\x16\n\x0ehasBakHostFile\x18\x19 \x01(\r\x12\x0f\n\x07webpage\x18\x1a \x01(\r\x12\x10\n\x08hostname\x18\x1b \x03(\r\x12\x13\n\x0b\x62yesterdays\x18\x1c \x03(\r\x12\r\n\x05other\x18\x1d \x03(\r\">\n\x0chostmore_ans\x12.\n\x05hosts\x18\x01 \x03(\x0b\x32\x1f.wuhan.jcl.report.hostmore_info\" \n\x10sub_unsub_hq_req\x12\x0c\n\x04\x61Key\x18\x01 \x03(\x04\"\x1e\n\x0cunsub_hq_ans\x12\x0e\n\x06subnum\x18\x01 \x01(\r\" \n\x10str_unsub_hq_req\x12\x0c\n\x04\x61Key\x18\x01 \x03(\t\")\n\nstknum_req\x12\x0f\n\x07setcode\x18\x01 \x01(\x05\x12\n\n\x02rq\x18\x02 \x01(\r\"\x1c\n\nstknum_ans\x12\x0e\n\x06stknum\x18\x01 \x01(\r\"\xc0\x02\n\nStkInfoNew\x12\x0c\n\x04nkey\x18\x01 \x01(\x04\x12\x0f\n\x07setcode\x18\x02 \x01(\x05\x12\x0c\n\x04\x43ode\x18\x03 \x01(\t\x12\x0c\n\x04Name\x18\x04 \x01(\t\x12\x0c\n\x04Unit\x18\x05 \x01(\x01\x12\x0f\n\x07VolBase\x18\x06 \x01(\x01\x12\x0f\n\x07precise\x18\x07 \x01(\r\x12\x0c\n\x04main\x18\x08 \x01(\r\x12\r\n\x05\x43lose\x18\t \x01(\x01\x12\x0e\n\x06Settle\x18\n \x01(\x01\x12\x0c\n\x04Tick\x18\x0b \x01(\x01\x12\x0b\n\x03nFZ\x18\x0c \x03(\r\x12\x10\n\x08OpenDate\x18\r \x01(\r\x12\x16\n\x0e\x42\x61seFreshCount\x18\x0e \x01(\r\x12\x16\n\x0eGbbqFreshCount\x18\x0f \x01(\r\x12\x13\n\x0b\x45nglishName\x18\x10 \x01(\t\x12\x13\n\x0b\x45nglishAbbr\x18\x11 \x01(\t\x12\x13\n\x0btypeOfStock\x18\x12 \x03(\x04\",\n\x08\x63ode_req\x12\x0f\n\x07setcode\x18\x01 \x01(\x05\x12\x0f\n\x07startxh\x18\x02 \x01(\r\"6\n\x08\x63ode_ans\x12*\n\x04\x63ode\x18\x01 \x03(\x0b\x32\x1c.wuhan.jcl.report.StkInfoNew\"K\n\x0bzhsort_item\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\x0b\n\x03val\x18\x02 \x03(\x01\")\n\nzhsort_req\x12\x0e\n\x06\x64omain\x18\x01 \x01(\r\x12\x0b\n\x03num\x18\x02 \x01(\r\";\n\nzhsort_ans\x12-\n\x06result\x18\x01 \x03(\x0b\x32\x1d.wuhan.jcl.report.zhsort_item\":\n\x07\x66jb_req\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\"g\n\x07\x66jb_ans\x12.\n\x02hq\x18\x01 \x01(\x0b\x32\".wuhan.jcl.report.CurrStockDataBEx\x12,\n\x03\x66jb\x18\x02 \x03(\x0b\x32\x1f.wuhan.jcl.report.PriceVolTable\"p\n\x0f\x61naly_range_req\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\x10\n\x08linetype\x18\x02 \x01(\r\x12\r\n\x05start\x18\x03 \x01(\x04\x12\x0b\n\x03\x65nd\x18\x04 \x01(\x04\"{\n\x0f\x61naly_range_ans\x12.\n\x03req\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.analy_range_req\x12\x0e\n\x06status\x18\x02 \x01(\r\x12(\n\x02\x61k\x18\x03 \x03(\x0b\x32\x1c.wuhan.jcl.report.AnalyDataB\"\x80\x01\n\x10\x61naly_offset_req\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\x0e\n\x06period\x18\x02 \x01(\r\x12\x0e\n\x06offset\x18\x03 \x01(\r\x12\x0b\n\x03num\x18\x04 \x01(\r\x12\x0e\n\x06mulnum\x18\x05 \x01(\r\"J\n\x10\x61naly_offset_ans\x12\x0c\n\x04nkey\x18\x01 \x01(\x04\x12(\n\x02\x61K\x18\x02 \x03(\x0b\x32\x1c.wuhan.jcl.report.AnalyDataB\"K\n\nminute_req\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\x0c\n\x04\x64\x61ys\x18\x02 \x01(\r\"\x88\x01\n\nminute_ans\x12\r\n\x05\x63ount\x18\x01 \x01(\r\x12\r\n\x05\x63lose\x18\x02 \x01(\x01\x12.\n\x02hq\x18\x03 \x01(\x0b\x32\".wuhan.jcl.report.CurrStockDataBEx\x12,\n\x06minute\x18\x04 \x03(\x0b\x32\x1c.wuhan.jcl.report.MinuteInfo\"|\n\x0c\x61naly_tq_req\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\x0e\n\x06period\x18\x02 \x01(\r\x12\x0e\n\x06offset\x18\x03 \x01(\r\x12\x0b\n\x03num\x18\x04 \x01(\r\x12\x0e\n\x06mulnum\x18\x05 \x01(\r\"S\n\x0c\x61naly_tq_ans\x12\x0c\n\x04nkey\x18\x01 \x01(\x04\x12\x0b\n\x03num\x18\x02 \x01(\r\x12(\n\x02\x61K\x18\x03 \x03(\x0b\x32\x1c.wuhan.jcl.report.AnalyDataB\"e\n\x0bsort_hq_req\x12\x11\n\tsetDomain\x18\x01 \x01(\r\x12\x0f\n\x07\x63oltype\x18\x02 \x01(\r\x12\x0f\n\x07startxh\x18\x03 \x01(\r\x12\x0f\n\x07wantnum\x18\x04 \x01(\r\x12\x10\n\x08sorttype\x18\x05 \x01(\r\"@\n\x0cmulti_hq_req\x12\x30\n\x05\x63odes\x18\x01 \x03(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\"?\n\x0cmulti_hq_ans\x12/\n\x03\x61hq\x18\x01 \x03(\x0b\x32\".wuhan.jcl.report.CurrStockDataBEx\"b\n\x0fmulti_ex_hq_req\x12\x0b\n\x03num\x18\x01 \x01(\r\x12\x10\n\x08\x66ieldnum\x18\x02 \x01(\r\x12\x30\n\x05\x63odes\x18\x03 \x03(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\"\x81\x01\n\x0fmulti_ex_hq_ans\x12\r\n\x05total\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\x12\x0e\n\x06\x65x_len\x18\x03 \x01(\r\x12\x30\n\x04\x64\x61ta\x18\x04 \x03(\x0b\x32\".wuhan.jcl.report.CurrStockDataBEx\x12\x0e\n\x06\x65xdata\x18\x05 \x03(\r\"?\n\x0c\x66ull_mmp_req\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\"e\n\x0c\x66ull_mmp_ans\x12\x0c\n\x04nkey\x18\x01 \x01(\x04\x12\x0e\n\x06\x62uyNum\x18\x02 \x01(\r\x12\x0f\n\x07SellNum\x18\x03 \x01(\r\x12&\n\x04\x64\x61ta\x18\x04 \x03(\x0b\x32\x18.wuhan.jcl.report.PKData\"[\n\x0blv2tick_req\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\x0e\n\x06offset\x18\x02 \x01(\r\x12\x0b\n\x03num\x18\x03 \x01(\r\"E\n\x0blv2tick_ans\x12\x0c\n\x04nkey\x18\x01 \x01(\x04\x12(\n\x04\x64\x61ta\x18\x02 \x03(\x0b\x32\x1a.wuhan.jcl.report.TickInfo\"E\n\rsort_code_req\x12\x11\n\tsetDomain\x18\x01 \x01(\r\x12\x0f\n\x07\x63oltype\x18\x02 \x01(\r\x12\x10\n\x08sorttype\x18\x03 \x01(\r\"\x1e\n\rsort_code_ans\x12\r\n\x05nkeys\x18\x01 \x03(\x04\"Z\n\x0cmmp_tick_req\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\r\n\x05price\x18\x02 \x01(\x01\x12\n\n\x02\x62s\x18\x03 \x01(\r\"-\n\x0cmmp_tick_ans\x12\x0c\n\x04nkey\x18\x01 \x01(\x04\x12\x0f\n\x07volumes\x18\x02 \x03(\x01\"\xac\x01\n\x11\x62lock_sort_hq_req\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\x0f\n\x07\x63oltype\x18\x02 \x01(\r\x12\x0f\n\x07startxh\x18\x03 \x01(\r\x12\x0f\n\x07wantnum\x18\x04 \x01(\r\x12\x10\n\x08sorttype\x18\x05 \x01(\r\x12\x10\n\x08\x66ieldnum\x18\x06 \x01(\r\x12\x0f\n\x07\x66ieldid\x18\x07 \x03(\r\"\x82\x01\n\x11\x62lock_sort_hq_ans\x12\r\n\x05total\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\x12\x0e\n\x06\x65x_len\x18\x03 \x01(\r\x12/\n\x03\x61hq\x18\x04 \x03(\x0b\x32\".wuhan.jcl.report.CurrStockDataBEx\x12\x0e\n\x06\x65xdata\x18\x05 \x03(\r\"\x89\x01\n\x0esort_ex_hq_req\x12\x11\n\tsetDomain\x18\x01 \x01(\r\x12\x0f\n\x07\x63oltype\x18\x02 \x01(\r\x12\x0f\n\x07startxh\x18\x03 \x01(\r\x12\x0f\n\x07wantnum\x18\x04 \x01(\r\x12\x10\n\x08sorttype\x18\x05 \x01(\r\x12\r\n\x05\x64rate\x18\x06 \x01(\r\x12\x10\n\x08\x66ieldids\x18\x07 \x03(\r\"f\n\x0ehq_with_fields\x12.\n\x02hq\x18\x01 \x01(\x0b\x32\".wuhan.jcl.report.CurrStockDataBEx\x12$\n\x06\x65xdata\x18\x02 \x03(\x0b\x32\x14.google.protobuf.Any\"\xb0\x01\n\x0esort_ex_hq_ans\x12\r\n\x05total\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\x12\x10\n\x08\x66ieldids\x18\x03 \x03(\r\x12\x0e\n\x06\x65x_len\x18\x04 \x01(\r\x12\x30\n\x06\x66ields\x18\x05 \x03(\x0b\x32 .wuhan.jcl.report.hq_with_fields\x12,\n\x08rankinfo\x18\x06 \x03(\x0b\x32\x1a.wuhan.jcl.report.rank_arr\"\x9b\x01\n\x11sort_block_hq_req\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\x0f\n\x07\x63oltype\x18\x02 \x01(\r\x12\x0f\n\x07startxh\x18\x03 \x01(\r\x12\x0f\n\x07wantnum\x18\x04 \x01(\r\x12\x10\n\x08sorttype\x18\x05 \x01(\r\x12\x10\n\x08\x66ieldids\x18\x06 \x03(\r\"g\n\x11sort_conblock_req\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\x0f\n\x07\x63oltype\x18\x02 \x01(\r\x12\x10\n\x08sorttype\x18\x03 \x01(\r\"y\n\x12sort_lbsbdb_hq_req\x12\x0c\n\x04type\x18\x01 \x01(\r\x12\x0f\n\x07\x63oltype\x18\x02 \x01(\r\x12\x0f\n\x07startxh\x18\x03 \x01(\r\x12\x0f\n\x07wantnum\x18\x04 \x01(\r\x12\x10\n\x08sorttype\x18\x05 \x01(\r\x12\x10\n\x08\x66ieldids\x18\x06 \x03(\r\"\x86\x01\n\x12sort_lbsbdb_hq_ans\x12\r\n\x05total\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\x12\x10\n\x08\x66ieldids\x18\x03 \x03(\r\x12\x0e\n\x06\x65x_len\x18\x04 \x01(\r\x12\x30\n\x06\x66ields\x18\x05 \x03(\x0b\x32 .wuhan.jcl.report.hq_with_fields\"\x8f\x01\n\x12stock_block_fields\x12\x11\n\tblocktype\x18\x01 \x01(\r\x12\x33\n\x07\x62lockhq\x18\x02 \x01(\x0b\x32\".wuhan.jcl.report.CurrStockDataBEx\x12\x31\n\x05lzghq\x18\x03 \x01(\x0b\x32\".wuhan.jcl.report.CurrStockDataBEx\"L\n\x14sort_stock_block_ans\x12\x34\n\x06\x66ields\x18\x01 \x03(\x0b\x32$.wuhan.jcl.report.stock_block_fields\"A\n\x0fsort_shq_ex_req\x12\x0c\n\x04keys\x18\x01 \x03(\x04\x12\x10\n\x08\x66ieldids\x18\x02 \x03(\r\x12\x0e\n\x06szkeys\x18\x03 \x03(\t\"V\n\x13sort_new_shq_ex_req\x12\x0f\n\x07setcode\x18\x01 \x01(\r\x12\x0c\n\x04keys\x18\x02 \x03(\x04\x12\x10\n\x08\x66ieldids\x18\x03 \x03(\r\x12\x0e\n\x06szkeys\x18\x04 \x03(\t\"\x94\x01\n\x0csort_zdy_req\x12\x0f\n\x07setcode\x18\x01 \x01(\r\x12\x0f\n\x07\x63oltype\x18\x02 \x01(\r\x12\x0f\n\x07startxh\x18\x03 \x01(\r\x12\x0f\n\x07wantnum\x18\x04 \x01(\r\x12\x10\n\x08sorttype\x18\x05 \x01(\r\x12\x0c\n\x04keys\x18\x06 \x03(\x04\x12\x10\n\x08\x66ieldids\x18\x07 \x03(\r\x12\x0e\n\x06szkeys\x18\x08 \x03(\t\"O\n\x0ehis_minute_req\x12\x0c\n\x04\x64\x61te\x18\x01 \x01(\r\x12/\n\x04\x63ode\x18\x02 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\"\\\n\x0ehis_minute_ans\x12\x0c\n\x04nkey\x18\x01 \x01(\x04\x12\r\n\x05\x63lose\x18\x02 \x01(\x01\x12-\n\x07minutes\x18\x03 \x03(\x0b\x32\x1c.wuhan.jcl.report.MinuteInfo\"g\n\x08tick_req\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\x0c\n\x04type\x18\x02 \x01(\r\x12\x0f\n\x07startxh\x18\x03 \x01(\r\x12\x0b\n\x03num\x18\x04 \x01(\r\"C\n\x08tick_ans\x12\x0c\n\x04nkey\x18\x01 \x01(\x04\x12)\n\x05ticks\x18\x02 \x03(\x0b\x32\x1a.wuhan.jcl.report.TickInfo\"z\n\x0chis_tick_req\x12\r\n\x05ldate\x18\x01 \x01(\r\x12/\n\x04\x63ode\x18\x02 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\x0c\n\x04type\x18\x03 \x01(\r\x12\x0f\n\x07startxh\x18\x04 \x01(\r\x12\x0b\n\x03num\x18\x05 \x01(\r\"V\n\x0chis_tick_ans\x12\x0c\n\x04nkey\x18\x01 \x01(\x04\x12\r\n\x05\x63lose\x18\x02 \x01(\x01\x12)\n\x05ticks\x18\x03 \x03(\x0b\x32\x1a.wuhan.jcl.report.TickInfo\"9\n\x08gsResult\x12\x0e\n\x06gsName\x18\x01 \x01(\t\x12\r\n\x05\x63ount\x18\x02 \x01(\r\x12\x0e\n\x06isLast\x18\x03 \x01(\x08\"\x85\x01\n\x0f\x63loudCalcResult\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\x10\n\x08\x63\x61lcTime\x18\x02 \x01(\x04\x12\r\n\x05\x43lose\x18\x03 \x01(\x01\x12\x0b\n\x03Now\x18\x04 \x01(\x01\x12\x13\n\x0bMaxZaf5Days\x18\x05 \x01(\x01\"#\n\x13\x63loud_calc_sort_req\x12\x0c\n\x04\x64\x61te\x18\x01 \x01(\r\"\x82\x01\n\x13\x63loud_calc_sort_ans\x12\x0c\n\x04\x64\x61te\x18\x01 \x01(\r\x12*\n\x06gsInfo\x18\x02 \x03(\x0b\x32\x1a.wuhan.jcl.report.gsResult\x12\x31\n\x06result\x18\x03 \x03(\x0b\x32!.wuhan.jcl.report.cloudCalcResult\"P\n\x0f\x61uctiondata_req\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\x0c\n\x04type\x18\x02 \x01(\r\"J\n\x0f\x61uctiondata_ans\x12\x0c\n\x04nkey\x18\x01 \x01(\x04\x12)\n\x05ticks\x18\x02 \x03(\x0b\x32\x1a.wuhan.jcl.report.TickInfo\"B\n\x0f\x61\x66ter_trade_req\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\"C\n\x0f\x61\x66ter_trade_ans\x12\x30\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32\".wuhan.jcl.report.CurrStockDataBEx\"\x1c\n\x0cjson_req_ans\x12\x0c\n\x04json\x18\x01 \x01(\t\"\xc9\x01\n\x16StrategyCenterList_Req\x12\x10\n\x08typecode\x18\x01 \x01(\t\x12\x0f\n\x07groupid\x18\x02 \x01(\t\x12/\n\x04\x63ode\x18\x03 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\x0c\n\x04\x64\x61te\x18\x04 \x01(\r\x12\x0e\n\x06maxnum\x18\x05 \x01(\r\x12\x0e\n\x06offset\x18\x06 \x01(\r\x12\x0b\n\x03num\x18\x07 \x01(\r\x12\x0e\n\x06period\x18\x08 \x01(\r\x12\x10\n\x08keytypes\x18\t \x03(\t\"5\n\x14StrategyMMOnekey_Req\x12\x0c\n\x04\x64\x61te\x18\x01 \x01(\r\x12\x0f\n\x07groupid\x18\x02 \x03(\t\"1\n\x11StrategyGS_Result\x12\x0c\n\x04\x64\x61te\x18\x01 \x01(\r\x12\x0e\n\x06result\x18\x02 \x03(\x01\"H\n\x12StrategyGS_AnsInfo\x12\x32\n\x05\x66ield\x18\x01 \x03(\x0b\x32#.wuhan.jcl.report.StrategyGS_Result\"\\\n\x18\x43\x61lcStatics_Agfd_AnsInfo\x12\r\n\x05\x66ield\x18\x01 \x03(\r\x12\x0f\n\x07\x66ieldsh\x18\x02 \x03(\r\x12\x0f\n\x07\x66ieldsz\x18\x03 \x03(\r\x12\x0f\n\x07\x66ieldcy\x18\x04 \x03(\r\"\xd6\x01\n\x17StrategyBS_List_AnsInfo\x12\x0f\n\x07groupid\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x0e\n\x06outwin\x18\x03 \x01(\x01\x12\x0f\n\x07outlose\x18\x04 \x01(\x01\x12\x0e\n\x06profit\x18\x05 \x01(\x01\x12\x0e\n\x06\x64\x61yzaf\x18\x06 \x01(\x01\x12\x0f\n\x07success\x18\x07 \x01(\x01\x12\x0c\n\x04\x64\x61te\x18\x08 \x01(\r\x12\r\n\x05\x63ount\x18\t \x01(\r\x12\x0f\n\x07setcode\x18\n \x01(\r\x12\x0c\n\x04\x63ode\x18\x0b \x01(\t\x12\x0e\n\x06maxfit\x18\x0c \x01(\x01\"\x8f\x01\n\x15StrategyOnekey_Result\x12\x0f\n\x07groupid\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x10\n\x08typecode\x18\x03 \x01(\t\x12\x0e\n\x06profit\x18\x04 \x01(\x01\x12\n\n\x02up\x18\x05 \x01(\r\x12\x0c\n\x04\x64own\x18\x06 \x01(\r\x12\r\n\x05total\x18\x07 \x01(\r\x12\x0c\n\x04\x64\x61te\x18\x08 \x01(\r\"+\n\x0bStrategyImg\x12\x0f\n\x07groupid\x18\x01 \x01(\t\x12\x0b\n\x03img\x18\x02 \x01(\t\"5\n\x15StrategyOnekey_Profit\x12\x0c\n\x04\x64\x61te\x18\x01 \x01(\r\x12\x0e\n\x06profit\x18\x02 \x01(\x01\"C\n\x11StrategyBS_Profit\x12\x0c\n\x04\x64\x61te\x18\x01 \x01(\r\x12\x0e\n\x06profit\x18\x02 \x01(\x01\x12\x10\n\x08zsprofit\x18\x03 \x01(\x01\"E\n\x11StrategyBS_Result\x12\x0f\n\x07\x61vedays\x18\x01 \x01(\r\x12\x0e\n\x06profit\x18\x02 \x01(\x01\x12\x0f\n\x07sucrate\x18\x03 \x01(\x01\"\xe5\x01\n\x12StrategyBS_XG_Data\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\x0b\n\x03\x62In\x18\x02 \x01(\r\x12\x0e\n\x06\x64otime\x18\x03 \x01(\x04\x12\r\n\x05price\x18\x04 \x01(\x01\x12\x10\n\x08maxprice\x18\x05 \x01(\x01\x12\x10\n\x08minprice\x18\x06 \x01(\x01\x12\x0f\n\x07tqprice\x18\x07 \x01(\x01\x12\x0f\n\x07outtime\x18\x08 \x01(\x04\x12\x10\n\x08outprice\x18\t \x01(\x01\x12\x0b\n\x03zaf\x18\n \x01(\x01\x12\r\n\x05inzaf\x18\x0b \x01(\x01\"\xcc\x01\n\x1dStrategyPoolCxqnJhjjZtqd_List\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\x12\n\nselecttime\x18\x02 \x01(\x04\x12\x13\n\x0bselectprice\x18\x03 \x01(\x01\x12\x11\n\tselectzaf\x18\x04 \x01(\x01\x12\x13\n\x0b\x66ivedayszaf\x18\x05 \x01(\x01\x12\x0e\n\x06\x66State\x18\x06 \x01(\x01\x12\x0c\n\x04\x64\x61ys\x18\x07 \x01(\r\x12\x0b\n\x03\x66\x65x\x18\x08 \x01(\x01\"\x96\x01\n\x1fStrategyPoolCxqnJhjjZtqd_Result\x12\x0f\n\x07groupid\x18\x01 \x01(\t\x12\x10\n\x08typecode\x18\x02 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x42\n\tpoollists\x18\x04 \x03(\x0b\x32/.wuhan.jcl.report.StrategyPoolCxqnJhjjZtqd_List\"@\n\x17Strategy_HisSignal_Data\x12\x0c\n\x04\x64\x61te\x18\x01 \x01(\r\x12\x0b\n\x03hms\x18\x02 \x01(\r\x12\n\n\x02\x62s\x18\x03 \x01(\r\"\x85\x01\n\x16Strategy_HisSignal_Ans\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12:\n\x07signals\x18\x02 \x03(\x0b\x32).wuhan.jcl.report.Strategy_HisSignal_Data\"G\n\x13StrategyMixPool_Req\x12\x0c\n\x04\x64\x61te\x18\x01 \x01(\r\x12\x10\n\x08typecode\x18\x02 \x01(\t\x12\x10\n\x08keytypes\x18\x03 \x03(\t\"\x83\x02\n\x14StrategyMixPool_Data\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\x0f\n\x07groupid\x18\x02 \x01(\t\x12\x12\n\nselecttime\x18\x03 \x01(\x04\x12\x13\n\x0bselectprice\x18\x04 \x01(\x01\x12\x12\n\nzaf_select\x18\x05 \x01(\x01\x12\x11\n\tzaf_5days\x18\x06 \x01(\x01\x12\x10\n\x08zaf_open\x18\x07 \x01(\x01\x12\x0b\n\x03zaf\x18\x08 \x01(\x01\x12\x0c\n\x04\x64\x61ys\x18\t \x01(\r\x12\x0e\n\x06\x65xdata\x18\n \x01(\x01\x12\x0c\n\x04\x64\x61te\x18\x0b \x01(\r\x12\x0e\n\x06\x64param\x18\x0c \x03(\x01\"7\n\x0fStrategyAny_Ans\x12$\n\x06\x63ldata\x18\x01 \x03(\x0b\x32\x14.google.protobuf.Any\"Y\n\rStrategy_SGZH\x12\x0c\n\x04type\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x0f\n\x07groupid\x18\x03 \x01(\t\x12\r\n\x05gssrc\x18\x04 \x01(\t\x12\x0c\n\x04time\x18\x05 \x01(\r\"/\n\x0eJobImport_Data\x12\x0f\n\x07groupid\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\"u\n\nIncomeLine\x12\x0c\n\x04\x64\x61te\x18\x01 \x01(\r\x12\r\n\x05state\x18\x02 \x01(\t\x12\x13\n\x0bzstotalrate\x18\x03 \x01(\x01\x12\x11\n\ttotalrate\x18\x04 \x01(\x01\x12\x11\n\tzsdayrate\x18\x05 \x01(\x01\x12\x0f\n\x07\x64\x61yrate\x18\x06 \x01(\x01\"\xa2\x01\n\x10IncomeStatistics\x12\x0c\n\x04type\x18\x01 \x01(\r\x12\r\n\x05total\x18\x02 \x01(\x01\x12\x0c\n\x04year\x18\x03 \x01(\x01\x12\x11\n\tsharprate\x18\x04 \x01(\x01\x12\x0f\n\x07\x62\x61\x63kset\x18\x05 \x01(\x01\x12\x10\n\x08\x62\x61udrate\x18\x06 \x01(\x01\x12\x10\n\x08inforate\x18\x07 \x01(\x01\x12\x0c\n\x04\x62\x65ta\x18\x08 \x01(\x01\x12\r\n\x05\x61lpha\x18\t \x01(\x01\"O\n\x0fStock_Calc_Info\x12\x0c\n\x04nkey\x18\x01 \x01(\x04\x12\r\n\x05score\x18\x02 \x01(\x01\x12\x11\n\tearnvalue\x18\x03 \x01(\x01\x12\x0c\n\x04rank\x18\x04 \x01(\r\"\x8e\x02\n\x0eStrategyRemark\x12\x0e\n\x06\x61uthor\x18\x01 \x01(\t\x12\x12\n\ncreatedate\x18\x02 \x01(\r\x12\x10\n\x08nextdate\x18\x03 \x01(\r\x12\x11\n\tbackdates\x18\x04 \x01(\r\x12\x11\n\tbackdatee\x18\x05 \x01(\r\x12\x0f\n\x07potimer\x18\x06 \x01(\t\x12\x10\n\x08realtime\x18\x07 \x01(\r\x12\x0e\n\x06period\x18\x08 \x01(\r\x12\r\n\x05model\x18\t \x01(\t\x12\x10\n\x08stocknum\x18\n \x01(\r\x12\r\n\x05\x63hose\x18\x0b \x01(\r\x12\x12\n\nstaticpool\x18\x0c \x01(\r\x12\x0c\n\x04\x62\x61se\x18\r \x01(\t\x12\x0b\n\x03\x66\x65\x65\x18\x0e \x01(\t\x12\x0e\n\x06remark\x18\x0f \x01(\t\"_\n\rStrategyScore\x12\x0c\n\x04\x65\x61rn\x18\x01 \x01(\x01\x12\x0b\n\x03kfx\x18\x02 \x01(\x01\x12\x0b\n\x03ldx\x18\x03 \x01(\x01\x12\x0b\n\x03wdx\x18\x04 \x01(\x01\x12\n\n\x02sp\x18\x05 \x01(\x01\x12\r\n\x05score\x18\x06 \x01(\x01\"\xf2\x01\n\x0eStockTradeInfo\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x11\n\tintrutype\x18\x03 \x01(\t\x12\x13\n\x0bintrusecond\x18\x04 \x01(\t\x12\x0f\n\x07\x62uydate\x18\x05 \x01(\r\x12\x10\n\x08selldate\x18\x06 \x01(\r\x12\x10\n\x08\x62uyprice\x18\x07 \x01(\x01\x12\x11\n\tsellprice\x18\x08 \x01(\x01\x12\x0b\n\x03zaf\x18\t \x01(\x01\x12\x12\n\ncloseprice\x18\n \x01(\x01\x12\x10\n\x08position\x18\x0b \x01(\x01\"\xa8\x01\n\x0e\x43hangePosition\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x11\n\tintrutype\x18\x03 \x01(\t\x12\x13\n\x0bintrusecond\x18\x04 \x01(\t\x12\x0e\n\x06signal\x18\x05 \x01(\r\x12\x10\n\x08position\x18\x06 \x01(\x01\x12\r\n\x05price\x18\x07 \x01(\x01\"\xf3\x01\n\x0fTransStatistics\x12\x0c\n\x04year\x18\x01 \x01(\x01\x12\x0f\n\x07\x61vehold\x18\x02 \x01(\x01\x12\x0e\n\x06\x61veday\x18\x03 \x01(\x01\x12\x11\n\taveincome\x18\x04 \x01(\x01\x12\x13\n\x0b\x61vepoincome\x18\x05 \x01(\x01\x12\x13\n\x0b\x61veneincome\x18\x06 \x01(\x01\x12\x0f\n\x07winrate\x18\x07 \x01(\x01\x12\x11\n\tcloserate\x18\x08 \x01(\x01\x12\x0e\n\x06\x64orate\x18\t \x01(\x01\x12\r\n\x05times\x18\n \x01(\r\x12\x12\n\nyearincome\x18\x0b \x01(\x01\x12\r\n\x05month\x18\x0c \x01(\x01\x12\x0e\n\x06zsdiss\x18\r \x01(\x01\"\xbc\x01\n\x07KCBDATA\x12\x0c\n\x04nkey\x18\x01 \x01(\x04\x12\x0f\n\x07jclTime\x18\x02 \x01(\x03\x12\x0e\n\x06status\x18\x03 \x01(\x05\x12\x11\n\ttickcount\x18\x04 \x01(\x05\x12\x0e\n\x06\x66Price\x18\x05 \x01(\x01\x12\x0c\n\x04\x66\x41mt\x18\x06 \x01(\x01\x12\x0c\n\x04\x66Vol\x18\x07 \x01(\x01\x12\x0f\n\x07\x66NowAmt\x18\x08 \x01(\x01\x12\x0f\n\x07\x66NowVol\x18\t \x01(\x01\x12\x0f\n\x07\x66\x42uyvol\x18\n \x01(\x01\x12\x10\n\x08\x66Sellvol\x18\x0b \x01(\x01\"\x93\x01\n\tKCBDataEx\x12,\n\tp_kcbdate\x18\x01 \x01(\x0b\x32\x19.wuhan.jcl.report.KCBDATA\x12\x10\n\x08\x62sDirect\x18\x02 \x01(\x05\x12\x0f\n\x07\x64Volume\x18\x03 \x01(\x01\x12\x12\n\norderNumer\x18\x04 \x01(\x05\x12\x0f\n\x07orderNo\x18\x05 \x01(\x05\x12\x10\n\x08orderVol\x18\x06 \x03(\x01\"8\n\nKCBDataAns\x12*\n\x05\x66ield\x18\x01 \x03(\x0b\x32\x1b.wuhan.jcl.report.KCBDataEx\">\n\x0c\x41uctionDataB\x12\x0f\n\x07jclTime\x18\x01 \x01(\x03\x12\x0c\n\x04\x66Now\x18\x02 \x01(\x01\x12\x0f\n\x07\x66NowVol\x18\x03 \x01(\x01\"o\n\x0e\x41uctionDataBEx\x12\x36\n\x0ep_auctiondatab\x18\x01 \x01(\x0b\x32\x1e.wuhan.jcl.report.AuctionDataB\x12\x12\n\nfUnsuitVol\x18\x02 \x01(\x01\x12\x11\n\tnUnsuitBS\x18\x03 \x01(\x05\"\x1f\n\x0f\x42inary_Data_Ans\x12\x0c\n\x04\x64\x61ta\x18\x01 \x01(\x0c\"]\n\rmoneyflow_req\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\x0e\n\x06offset\x18\x02 \x01(\r\x12\x0b\n\x03num\x18\x03 \x01(\r\"\xa5\x01\n\x0emoneyflow_data\x12\x0f\n\x07jcltime\x18\x01 \x01(\x03\x12\x0f\n\x07superIn\x18\x02 \x01(\x01\x12\x10\n\x08superOut\x18\x03 \x01(\x01\x12\r\n\x05\x62igIn\x18\x04 \x01(\x01\x12\x0e\n\x06\x62igOut\x18\x05 \x01(\x01\x12\r\n\x05midIn\x18\x06 \x01(\x01\x12\x0e\n\x06midOut\x18\x07 \x01(\x01\x12\x0f\n\x07smallIn\x18\x08 \x01(\x01\x12\x10\n\x08smallOut\x18\t \x01(\x01\"\x9c\x01\n\rmoneyflow_ans\x12-\n\x03\x61mt\x18\x01 \x03(\x0b\x32 .wuhan.jcl.report.moneyflow_data\x12-\n\x03vol\x18\x02 \x03(\x0b\x32 .wuhan.jcl.report.moneyflow_data\x12-\n\x03\x63nt\x18\x03 \x03(\x0b\x32 .wuhan.jcl.report.moneyflow_data\"\xe6\x01\n\tdxjl_data\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\x0c\n\x04type\x18\x02 \x01(\r\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x0f\n\x07jcltime\x18\x04 \x01(\x03\x12\r\n\x05price\x18\x05 \x01(\x01\x12\x0b\n\x03vol\x18\x06 \x01(\x01\x12\x0b\n\x03zaf\x18\x07 \x01(\x01\x12\x0c\n\x04rate\x18\x08 \x01(\x01\x12\x10\n\x08\x62uyprice\x18\t \x01(\x01\x12\x0e\n\x06\x62uyvol\x18\n \x01(\x01\x12\x11\n\tsellprice\x18\x0b \x01(\x01\x12\x0f\n\x07sellvol\x18\x0c \x01(\x01\"6\n\tdxjl_push\x12)\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32\x1b.wuhan.jcl.report.dxjl_data\"5\n\x08\x44xjl_Req\x12\x0e\n\x06offset\x18\x01 \x01(\r\x12\x0b\n\x03num\x18\x02 \x01(\r\x12\x0c\n\x04type\x18\x03 \x01(\r\"g\n\x07RSPDATA\x12\x0c\n\x04type\x18\x01 \x01(\r\x12/\n\x04\x63ode\x18\x02 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\x0f\n\x07jcltime\x18\x03 \x01(\x03\x12\x0c\n\x04\x64\x61te\x18\x04 \x03(\x01\"4\n\x08\x44xjl_ans\x12(\n\x05\x66ield\x18\x01 \x03(\x0b\x32\x19.wuhan.jcl.report.RSPDATA\"I\n\tztfx_data\x12\x0f\n\x07jcltime\x18\x01 \x01(\x04\x12\x0c\n\x04rate\x18\x02 \x01(\x01\x12\r\n\x05\x66irst\x18\x03 \x01(\r\x12\x0e\n\x06second\x18\x04 \x01(\r\"6\n\x08zafx_ans\x12*\n\x05\x66ield\x18\x01 \x03(\x0b\x32\x1b.wuhan.jcl.report.ztfx_data\",\n\rbkyd_top_data\x12\x0c\n\x04nkey\x18\x01 \x01(\x04\x12\r\n\x05ratio\x18\x02 \x01(\x01\"\x8d\x01\n\x10\x62kyd_single_data\x12\x0c\n\x04nkey\x18\x01 \x01(\x04\x12\r\n\x05ratio\x18\x02 \x01(\x01\x12\r\n\x05index\x18\x03 \x01(\r\x12\x0c\n\x04time\x18\x04 \x01(\t\x12\x10\n\x08\x62usiness\x18\x05 \x01(\x01\x12-\n\x04tops\x18\x06 \x03(\x0b\x32\x1f.wuhan.jcl.report.bkyd_top_data\"A\n\x0c\x62kyd_all_ans\x12\x31\n\x05\x66ield\x18\x01 \x03(\x0b\x32\".wuhan.jcl.report.bkyd_single_data\"\x1d\n\rbkyd_date_req\x12\x0c\n\x04\x64\x61te\x18\x01 \x01(\x05\")\n\nbkfb_stock\x12\x0c\n\x04nkey\x18\x01 \x01(\x04\x12\r\n\x05ratio\x18\x02 \x01(\x01\"G\n\tbkfb_area\x12\r\n\x05\x63ount\x18\x01 \x01(\x05\x12+\n\x05stock\x18\x02 \x03(\x0b\x32\x1c.wuhan.jcl.report.bkfb_stock\"L\n\x0c\x62kfb_all_ans\x12\x11\n\tareaCount\x18\x01 \x01(\r\x12)\n\x04\x64\x61ta\x18\x02 \x03(\x0b\x32\x1b.wuhan.jcl.report.bkfb_area\"\x1d\n\rbkfb_nkey_req\x12\x0c\n\x04nkey\x18\x01 \x01(\x04\":\n\rzd_number_ans\x12\x0b\n\x03nUp\x18\x01 \x01(\r\x12\r\n\x05nFlat\x18\x02 \x01(\r\x12\r\n\x05nDown\x18\x03 \x01(\r\"a\n\x10\x66inance_down_req\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\r\n\x05sdate\x18\x02 \x01(\r\x12\r\n\x05\x65\x64\x61te\x18\x03 \x01(\r\"\xca\x11\n\x11\x66inance_down_data\x12\x0c\n\x04nkey\x18\x01 \x01(\x03\x12\x0c\n\x04\x44\x61te\x18\x02 \x01(\x03\x12\x1b\n\x13\x43urrentTotalCapital\x18\x03 \x01(\x01\x12\x1c\n\x14\x43urrentActiveCapital\x18\x04 \x01(\x01\x12\x14\n\x0cTotalCapital\x18\x05 \x01(\x01\x12\x0e\n\x06Market\x18\x06 \x01(\x01\x12\x11\n\tStockType\x18\x07 \x01(\x01\x12\x14\n\x0cIndustryCode\x18\x08 \x01(\x01\x12\x0f\n\x07\x42_Stock\x18\t \x01(\x01\x12\x0f\n\x07H_Stock\x18\n \x01(\x01\x12\x15\n\rActiveCapital\x18\x0b \x01(\x01\x12\x14\n\x0cShareHolders\x18\x0c \x01(\x01\x12\x1b\n\x13\x41ssetLiabilityRatio\x18\r \x01(\x01\x12\x12\n\nTotalAsset\x18\x0e \x01(\x01\x12\x14\n\x0c\x43urrentAsset\x18\x0f \x01(\x01\x12\x12\n\nFixedAsset\x18\x10 \x01(\x01\x12\x17\n\x0fIntangibleAsset\x18\x11 \x01(\x01\x12\x18\n\x10\x43urrentLiability\x18\x12 \x01(\x01\x12\x1a\n\x12MinoritySHInterest\x18\x13 \x01(\x01\x12\x13\n\x0b\x43\x61pitalFund\x18\x14 \x01(\x01\x12\x14\n\x0cPerShareFund\x18\x15 \x01(\x01\x12\x12\n\nSHInterest\x18\x16 \x01(\x01\x12\x16\n\x0e\x42usinessIncome\x18\x17 \x01(\x01\x12\x14\n\x0c\x42usinessCost\x18\x18 \x01(\x01\x12\x13\n\x0bReceivables\x18\x19 \x01(\x01\x12\x16\n\x0e\x42usinessProfit\x18\x1a \x01(\x01\x12\x18\n\x10InvestmentIncome\x18\x1b \x01(\x01\x12\x18\n\x10\x42usinessCashFlow\x18\x1c \x01(\x01\x12\x15\n\rTotalCashFlow\x18\x1d \x01(\x01\x12\x11\n\tInventory\x18\x1e \x01(\x01\x12\x13\n\x0bTotalProfit\x18\x1f \x01(\x01\x12\x16\n\x0e\x41\x66terTaxProfit\x18  \x01(\x01\x12\x11\n\tNetProfit\x18! \x01(\x01\x12\x1b\n\x13UndistributedProfit\x18\" \x01(\x01\x12\x1a\n\x12PerShareProfit_wfp\x18# \x01(\x01\x12\x1b\n\x13PerShareProfit_qnzs\x18$ \x01(\x01\x12\x18\n\x10PerShareNetAsset\x18% \x01(\x01\x12\x19\n\x11SesonallyNetAsset\x18& \x01(\x01\x12\x17\n\x0fSHInterestRatio\x18\' \x01(\x01\x12\x1a\n\x12QuarterlyReportNum\x18( \x01(\x01\x12\x1f\n\x17PerSharEarningEnterpris\x18) \x01(\x01\x12\x14\n\x0cTimeToMarket\x18* \x01(\x01\x12\x13\n\x0bProfitRatio\x18+ \x01(\x01\x12\x13\n\x0bIncomeRatio\x18, \x01(\x01\x12\x1a\n\x12\x44ividendYieldRatio\x18- \x01(\x01\x12\x1a\n\x12\x46reeCurrentCapital\x18. \x01(\x01\x12\x13\n\x0bTodayQXMark\x18/ \x01(\x01\x12\x15\n\rBelongToHS300\x18\x30 \x01(\x01\x12\x13\n\x0b\x43ontainsKZZ\x18\x31 \x01(\x01\x12\x10\n\x08IsRZRQBD\x18\x32 \x01(\x01\x12\x18\n\x10PerShareDividend\x18\x33 \x01(\x01\x12\x13\n\x0bKFNetProfit\x18\x34 \x01(\x01\x12\x13\n\x0b\x44\x65velopCost\x18\x35 \x01(\x01\x12\x13\n\x0b\x45mployeeNum\x18\x36 \x01(\x01\x12\x17\n\x0f\x43urrencyCapital\x18\x37 \x01(\x01\x12\x1d\n\x15Receivables_InAdvance\x18\x38 \x01(\x01\x12\x15\n\rExerciseRatio\x18\x39 \x01(\x01\x12\x15\n\rExercisePrice\x18: \x01(\x01\x12\x12\n\nLeverRatio\x18; \x01(\x01\x12\x16\n\x0eIntrinsicValue\x18< \x01(\x01\x12\x13\n\x0bPremiumRate\x18= \x01(\x01\x12\x11\n\tTimeValue\x18> \x01(\x01\x12\x10\n\x08\x44\x61ysLeft\x18? \x01(\x01\x12\x1a\n\x12\x43ontractMultiplier\x18@ \x01(\x01\x12\r\n\x05\x44\x65lta\x18\x41 \x01(\x01\x12\x11\n\tSubscribe\x18\x42 \x01(\x01\x12\x12\n\nFinance_65\x18\x43 \x01(\x01\x12\x12\n\nFinance_66\x18\x44 \x01(\x01\x12\x12\n\nFinance_67\x18\x45 \x01(\x01\x12\x12\n\nFinance_68\x18\x46 \x01(\x01\x12\x12\n\nFinance_69\x18G \x01(\x01\x12\x12\n\nFinance_70\x18H \x01(\x01\x12\x12\n\nFinance_71\x18I \x01(\x01\x12\x12\n\nFinance_72\x18J \x01(\x01\x12\x12\n\nFinance_73\x18K \x01(\x01\x12\x12\n\nFinance_74\x18L \x01(\x01\x12\x12\n\nFinance_75\x18M \x01(\x01\x12\x12\n\nFinance_76\x18N \x01(\x01\x12\x12\n\nFinance_77\x18O \x01(\x01\x12\x12\n\nFinance_78\x18P \x01(\x01\x12\x12\n\nFinance_79\x18Q \x01(\x01\x12\x12\n\nFinance_80\x18R \x01(\x01\x12\x12\n\nFinance_81\x18S \x01(\x01\x12\x12\n\nFinance_82\x18T \x01(\x01\x12\x12\n\nFinance_83\x18U \x01(\x01\x12\x12\n\nFinance_84\x18V \x01(\x01\x12\x12\n\nFinance_85\x18W \x01(\x01\x12\x12\n\nFinance_86\x18X \x01(\x01\x12\x12\n\nFinance_87\x18Y \x01(\x01\x12\x12\n\nFinance_88\x18Z \x01(\x01\x12\x12\n\nFinance_89\x18[ \x01(\x01\x12\x12\n\nFinance_90\x18\\ \x01(\x01\x12\x12\n\nFinance_91\x18] \x01(\x01\x12\x12\n\nFinance_92\x18^ \x01(\x01\x12\x12\n\nFinance_93\x18_ \x01(\x01\x12\x12\n\nFinance_94\x18` \x01(\x01\x12\x12\n\nFinance_95\x18\x61 \x01(\x01\x12\x12\n\nFinance_96\x18\x62 \x01(\x01\x12\x12\n\nFinance_97\x18\x63 \x01(\x01\x12\x12\n\nFinance_98\x18\x64 \x01(\x01\x12\x12\n\nFinance_99\x18\x65 \x01(\x01\x12\x13\n\x0b\x46inance_100\x18\x66 \x01(\x01\"\xf4w\n\x12\x66invalue_down_data\x12\x0c\n\x04nkey\x18\x01 \x01(\x03\x12\x0c\n\x04\x44\x61te\x18\x02 \x01(\x03\x12\x12\n\nReportTime\x18\x03 \x01(\x01\x12\x1b\n\x13PerShareProfitBasic\x18\x04 \x01(\x01\x12\x1f\n\x17PerShareProfit_kcfjcxsy\x18\x05 \x01(\x01\x12\x1a\n\x12PerShareProfit_wfp\x18\x06 \x01(\x01\x12\x18\n\x10PerShareNetAsset\x18\x07 \x01(\x01\x12\x1b\n\x13PerShareCapitalFund\x18\x08 \x01(\x01\x12\x19\n\x11NetAssetYieldRate\x18\t \x01(\x01\x12 \n\x18PerShareBusinessCashFlow\x18\n \x01(\x01\x12\x17\n\x0f\x43urrencyCapital\x18\x0b \x01(\x01\x12\x1e\n\x16TradableFinancialAsset\x18\x0c \x01(\x01\x12\x17\n\x0fReceivablesBill\x18\r \x01(\x01\x12\x13\n\x0bReceivables\x18\x0e \x01(\x01\x12\x12\n\nPrePayment\x18\x0f \x01(\x01\x12\x18\n\x10OtherReceivables\x18\x10 \x01(\x01\x12!\n\x19RelatedCompanyReceivables\x18\x11 \x01(\x01\x12\x1b\n\x13InterestReceivables\x18\x12 \x01(\x01\x12\x1b\n\x13\x44ividendReceivables\x18\x13 \x01(\x01\x12\x11\n\tInventory\x18\x14 \x01(\x01\x12#\n\x1bInclude_BiologicalAsset_xhx\x18\x15 \x01(\x01\x12\x19\n\x11OtherCurrentAsset\x18\x16 \x01(\x01\x12\x19\n\x11TotalCurrentAsset\x18\x17 \x01(\x01\x12\x1d\n\x15SalableFinancialAsset\x18\x18 \x01(\x01\x12\x18\n\x10OnTimeInvestment\x18\x19 \x01(\x01\x12\x1b\n\x13LongTermReceivables\x18\x1a \x01(\x01\x12 \n\x18LongTermEquityInvestment\x18\x1b \x01(\x01\x12\x1c\n\x14RealEstateInvestment\x18\x1c \x01(\x01\x12\x12\n\nFixedAsset\x18\x1d \x01(\x01\x12\x1d\n\x15\x43onstructionInProcess\x18\x1e \x01(\x01\x12\x1b\n\x13\x45ngineeringMaterial\x18\x1f \x01(\x01\x12\x17\n\x0f\x46ixedAssetClear\x18  \x01(\x01\x12\x1b\n\x13\x42iologicalAsset_scx\x18! \x01(\x01\x12\x10\n\x08GasAsset\x18\" \x01(\x01\x12\x17\n\x0fIntangibleAsset\x18# \x01(\x01\x12\x1e\n\x16\x44\x65velopmentExpenditure\x18$ \x01(\x01\x12\x1a\n\x12\x42usinessReputation\x18% \x01(\x01\x12\"\n\x1aLongTermUnamortizedExpense\x18& \x01(\x01\x12\x1e\n\x16\x44\x65\x66\x65rredIncomeTaxAsset\x18\' \x01(\x01\x12\x1c\n\x14OtherNonCurrentAsset\x18( \x01(\x01\x12\x1a\n\x12TotalIlliquidAsset\x18) \x01(\x01\x12\x12\n\nTotalAsset\x18* \x01(\x01\x12\x17\n\x0fShortTermBorrow\x18+ \x01(\x01\x12\"\n\x1aTradableFinancialLiability\x18, \x01(\x01\x12\x13\n\x0bPayableBill\x18- \x01(\x01\x12\x10\n\x08Payables\x18. \x01(\x01\x12\x18\n\x10\x41\x64vancedReceived\x18/ \x01(\x01\x12\x16\n\x0ePayrollPayable\x18\x30 \x01(\x01\x12\x12\n\nTaxPayable\x18\x31 \x01(\x01\x12\x17\n\x0fInterestPayable\x18\x32 \x01(\x01\x12\x17\n\x0f\x44ividendPayable\x18\x33 \x01(\x01\x12\x14\n\x0cOtherPayable\x18\x34 \x01(\x01\x12\x1d\n\x15RelatedCompanyPayable\x18\x35 \x01(\x01\x12$\n\x1cNonCurrentLiabilityInOneYear\x18\x36 \x01(\x01\x12\x1d\n\x15OtherCurrentLiability\x18\x37 \x01(\x01\x12\x1d\n\x15TotalCurrentLiability\x18\x38 \x01(\x01\x12\x16\n\x0eLongTermBorrow\x18\x39 \x01(\x01\x12\x14\n\x0cPayablesLoan\x18: \x01(\x01\x12\x18\n\x10LongTermPayables\x18; \x01(\x01\x12\x17\n\x0fSpecialPayables\x18< \x01(\x01\x12\x1d\n\x15\x41nticipationLiability\x18= \x01(\x01\x12!\n\x19\x44\x65\x66\x65rredIncomTaxLiability\x18> \x01(\x01\x12 \n\x18OtherNonCurrentLiability\x18? \x01(\x01\x12 \n\x18TotalNonCurrentLiability\x18@ \x01(\x01\x12\x16\n\x0eTotalLiability\x18\x41 \x01(\x01\x12\x15\n\rPaidUpCapital\x18\x42 \x01(\x01\x12\x16\n\x0e\x43\x61pitalReserve\x18\x43 \x01(\x01\x12\x16\n\x0eSurplusReserve\x18\x44 \x01(\x01\x12\x1a\n\x12Sub_InventoryShare\x18\x45 \x01(\x01\x12\x1b\n\x13UndistributedProfit\x18\x46 \x01(\x01\x12\x1a\n\x12MinoritySHInterest\x18G \x01(\x01\x12\x1c\n\x14\x46oreignCurrency_zsjc\x18H \x01(\x01\x12\x1f\n\x17\x41\x62normalBusinessEarning\x18I \x01(\x01\x12\x18\n\x10OwnerTotalRights\x18J \x01(\x01\x12\x1b\n\x13OwnerTotalLiability\x18K \x01(\x01\x12\x1e\n\x16Include_BusinessIncome\x18L \x01(\x01\x12\x1c\n\x14Include_BusinessCost\x18M \x01(\x01\x12\x17\n\x0f\x42usinessTaxPlus\x18N \x01(\x01\x12\x16\n\x0eSellingExpense\x18O \x01(\x01\x12\x15\n\rManageExpense\x18P \x01(\x01\x12\x1a\n\x12\x45xplorationExpense\x18Q \x01(\x01\x12\x18\n\x10\x46inancialExpense\x18R \x01(\x01\x12\x12\n\nAssetsLoss\x18S \x01(\x01\x12\x1c\n\x14\x41\x64\x64_NetProfit_gyjzbd\x18T \x01(\x01\x12\x18\n\x10InvestmentIncome\x18U \x01(\x01\x12 \n\x18Include_InvestmentProfit\x18V \x01(\x01\x12\x1b\n\x13\x42usinessProfit_qtkm\x18W \x01(\x01\x12\x1c\n\x14Third_BusinessProfit\x18X \x01(\x01\x12\x19\n\x11\x41\x64\x64_SubsidyIncome\x18Y \x01(\x01\x12\x19\n\x11NonbusinessIncome\x18Z \x01(\x01\x12\"\n\x1aSub_NonbusinessExpenditure\x18[ \x01(\x01\x12&\n\x1eInclude_NonCurrentAssetNetLoss\x18\\ \x01(\x01\x12\x1c\n\x14\x41\x64\x64_TotalProfit_qtkm\x18] \x01(\x01\x12\x1a\n\x12\x46ourth_TotalProfit\x18^ \x01(\x01\x12\x15\n\rSub_IncomeTax\x18_ \x01(\x01\x12\x1a\n\x12\x41\x64\x64_NetProfit_qtkm\x18` \x01(\x01\x12\x17\n\x0f\x46ifth_NetProfit\x18\x61 \x01(\x01\x12\x18\n\x10NetProfit_mgssyz\x18\x62 \x01(\x01\x12\x1d\n\x15MinorityInterestsLoss\x18\x63 \x01(\x01\x12\x16\n\x0e\x43\x61sh_xxsp_tglw\x18\x64 \x01(\x01\x12\x0c\n\x04sffh\x18\x65 \x01(\x01\x12\x1a\n\x12\x43\x61shGet_Other_jjhd\x18\x66 \x01(\x01\x12\x1f\n\x17\x43\x61shFlowInSubtotal_jjhd\x18g \x01(\x01\x12\x1a\n\x12\x43\x61shPaid_gmsh_jslw\x18h \x01(\x01\x12\x13\n\x0b\x43\x61shPaid_zg\x18i \x01(\x01\x12\x10\n\x08TaxPaid_\x18j \x01(\x01\x12\x15\n\rCashPaid_jjhd\x18k \x01(\x01\x12 \n\x18\x43\x61shFlowOutSubtotal_jjhd\x18l \x01(\x01\x12\x1a\n\x12\x43\x61shNetAmount_jjhd\x18m \x01(\x01\x12\x1a\n\x12\x43\x61shGet_Investment\x18n \x01(\x01\x12 \n\x18\x43\x61shGet_InvestmentProfit\x18o \x01(\x01\x12\x1b\n\x13\x43\x61shNetAmountGet_zc\x18p \x01(\x01\x12$\n\x1c\x43\x61shNetAmountGet_zgs_yydw_cz\x18q \x01(\x01\x12\x1f\n\x17\x43\x61shGet_OtherInvestment\x18r \x01(\x01\x12%\n\x1d\x43\x61shFlowInSubtotal_Investment\x18s \x01(\x01\x12\x13\n\x0b\x43\x61shPaid_zc\x18t \x01(\x01\x12\x1b\n\x13\x43\x61shPaid_Investment\x18u \x01(\x01\x12!\n\x19\x43\x61shNetAmountGet_zgs_yydw\x18v \x01(\x01\x12 \n\x18\x43\x61shPaid_OtherInvestment\x18w \x01(\x01\x12&\n\x1e\x43\x61shFlowOutSubtotal_Investment\x18x \x01(\x01\x12 \n\x18\x43\x61shNetAmount_Investment\x18y \x01(\x01\x12\x1d\n\x15\x43\x61shAbsorb_Investment\x18z \x01(\x01\x12\x16\n\x0e\x43\x61shGet_Borrow\x18{ \x01(\x01\x12\x1a\n\x12\x43\x61shGet_Other_czhd\x18| \x01(\x01\x12\x1f\n\x17\x43\x61shFlowInSubtotal_czhd\x18} \x01(\x01\x12\x15\n\rCashPaid_Debt\x18~ \x01(\x01\x12\x1d\n\x15\x43\x61shPaid_fpgl_lr_cflx\x18\x7f \x01(\x01\x12\x1c\n\x13\x43\x61shPaid_Other_czhd\x18\x80\x01 \x01(\x01\x12!\n\x18\x43\x61shFlowOutSubtotal_czhd\x18\x81\x01 \x01(\x01\x12\x1f\n\x16\x43\x61shFlowNetAmount_czhd\x18\x82\x01 \x01(\x01\x12\"\n\x19\x46ourth_CashInfluence_hlbd\x18\x83\x01 \x01(\x01\x12%\n\x1c\x46ourth_2_CashInfluence_Other\x18\x84\x01 \x01(\x01\x12%\n\x1c\x46ifth_CashNetAmount_Increase\x18\x85\x01 \x01(\x01\x12 \n\x17\x43\x61shBalance_Equav_Begin\x18\x86\x01 \x01(\x01\x12\x1e\n\x15\x43\x61shBalance_Equav_End\x18\x87\x01 \x01(\x01\x12\x12\n\tNetProfit\x18\x88\x01 \x01(\x01\x12\x13\n\nAdd_zcjzzb\x18\x89\x01 \x01(\x01\x12\'\n\x1e\x44\x65preciation_gdzc_yqzc_scxswzc\x18\x8a\x01 \x01(\x01\x12\x1a\n\x11\x41mortization_wxzc\x18\x8b\x01 \x01(\x01\x12\x1c\n\x13\x41mortization_cqdtfy\x18\x8c\x01 \x01(\x01\x12\x1c\n\x13Loss_gdzc_wxzc_cqzc\x18\x8d\x01 \x01(\x01\x12\x14\n\x0bLoss_gdzcbf\x18\x8e\x01 \x01(\x01\x12\x14\n\x0bLoss_gyjzbd\x18\x8f\x01 \x01(\x01\x12\x16\n\rFinancialCost\x18\x90\x01 \x01(\x01\x12\x18\n\x0fLoss_Investment\x18\x91\x01 \x01(\x01\x12\'\n\x1e\x44\x65\x63rease_DeferredIncomTaxAsset\x18\x92\x01 \x01(\x01\x12\'\n\x1eIncrease_DeferredIncomeTaxDebt\x18\x93\x01 \x01(\x01\x12\x1b\n\x12\x44\x65\x63rease_Inventory\x18\x94\x01 \x01(\x01\x12\x19\n\x10\x44\x65\x63rease_jyxysxm\x18\x95\x01 \x01(\x01\x12\x19\n\x10Increase_jyxyfxm\x18\x96\x01 \x01(\x01\x12\x0e\n\x05Other\x18\x97\x01 \x01(\x01\x12\x1f\n\x16\x43\x61shFlowNetAmount_jyhd\x18\x98\x01 \x01(\x01\x12\x1b\n\x12\x44\x65\x62tConvertToAsset\x18\x99\x01 \x01(\x01\x12\x1f\n\x16\x43onvertibleBonds_ynndq\x18\x9a\x01 \x01(\x01\x12\x18\n\x0f\x46ixedAsset_rzzr\x18\x9b\x01 \x01(\x01\x12\x18\n\x0f\x43\x61shBalance_End\x18\x9c\x01 \x01(\x01\x12\x1c\n\x13Sub_CashBalance_End\x18\x9d\x01 \x01(\x01\x12\x16\n\rAdd_xjdjw_End\x18\x9e\x01 \x01(\x01\x12\x18\n\x0fSub_xjdjw_Begin\x18\x9f\x01 \x01(\x01\x12\x1f\n\x16\x43\x61shIncrease_NetAmount\x18\xa0\x01 \x01(\x01\x12\x16\n\rRatio_Current\x18\xa1\x01 \x01(\x01\x12\x15\n\x0cRation_Quick\x18\xa2\x01 \x01(\x01\x12\x13\n\nRatio_Cash\x18\xa3\x01 \x01(\x01\x12\x1f\n\x16Ratio_InterestCoverage\x18\xa4\x01 \x01(\x01\x12\x1d\n\x14Ratio_NonCurrentDebt\x18\xa5\x01 \x01(\x01\x12\x1a\n\x11Ratio_CurrentDebt\x18\xa6\x01 \x01(\x01\x12\x18\n\x0fRatio_Debt_xjdq\x18\xa7\x01 \x01(\x01\x12\x1a\n\x11Ratio_Debt_yxzcjz\x18\xa8\x01 \x01(\x01\x12\x19\n\x10\x45quityMultiplier\x18\xa9\x01 \x01(\x01\x12\x17\n\x0egdqy_TotalDebt\x18\xaa\x01 \x01(\x01\x12\x17\n\x0eyxzc_TotalDebt\x18\xab\x01 \x01(\x01\x12)\n CashFlowNetAmount_TotalDebt_jyhd\x18\xac\x01 \x01(\x01\x12\x19\n\x10\x45\x42ITDA_TotalDebt\x18\xad\x01 \x01(\x01\x12\x1a\n\x11TurnoverRate_yszk\x18\xae\x01 \x01(\x01\x12\x1f\n\x16TurnoverRate_Inventory\x18\xaf\x01 \x01(\x01\x12\x1a\n\x11TurnoverRate_yyzj\x18\xb0\x01 \x01(\x01\x12 \n\x17TurnoverRate_TotalAsset\x18\xb1\x01 \x01(\x01\x12\x1a\n\x11TurnoverRate_gdzc\x18\xb2\x01 \x01(\x01\x12\x1a\n\x11TurnoverDays_yszk\x18\xb3\x01 \x01(\x01\x12\x1f\n\x16TurnoverDays_Inventory\x18\xb4\x01 \x01(\x01\x12\x1a\n\x11TurnoverRate_ldzc\x18\xb5\x01 \x01(\x01\x12\x1a\n\x11TurnoverDays_ldzc\x18\xb6\x01 \x01(\x01\x12!\n\x18TurnoverDays_TotalAssets\x18\xb7\x01 \x01(\x01\x12(\n\x1fTurnoverRate_ShareholdersEquity\x18\xb8\x01 \x01(\x01\x12#\n\x1aGrowthRate_Operatingincome\x18\xb9\x01 \x01(\x01\x12\x1d\n\x14GrowthRate_NetProfit\x18\xba\x01 \x01(\x01\x12\x1e\n\x15GrowthRate_NetaAssets\x18\xbb\x01 \x01(\x01\x12\x1f\n\x16GrowthRate_FixedAssets\x18\xbc\x01 \x01(\x01\x12\x1f\n\x16GrowthRate_TotalAssets\x18\xbd\x01 \x01(\x01\x12$\n\x1bGrowthRate_InvestmentIncome\x18\xbe\x01 \x01(\x01\x12#\n\x1aGrowthRate_OperatingProfit\x18\xbf\x01 \x01(\x01\x12\x11\n\x08kfmgsytb\x18\xc0\x01 \x01(\x01\x12\x10\n\x07kfjlrtb\x18\xc1\x01 \x01(\x01\x12\r\n\x04None\x18\xc2\x01 \x01(\x01\x12\x1d\n\x14ProfitRate_CostCosts\x18\xc3\x01 \x01(\x01\x12\x1c\n\x13ProfitRate_Business\x18\xc4\x01 \x01(\x01\x12\x19\n\x10TaxRate_Business\x18\xc5\x01 \x01(\x01\x12\x1a\n\x11\x43ostRate_Business\x18\xc6\x01 \x01(\x01\x12\x1c\n\x13YieldRate_NetAssets\x18\xc7\x01 \x01(\x01\x12\x1c\n\x13YieldRat_Investment\x18\xc8\x01 \x01(\x01\x12\x1e\n\x15NetInterestRate_Sales\x18\xc9\x01 \x01(\x01\x12\x1c\n\x13PayRate_TotalAssets\x18\xca\x01 \x01(\x01\x12\x16\n\rNetProfitRate\x18\xcb\x01 \x01(\x01\x12\x1e\n\x15GrossProfitRate_Sales\x18\xcc\x01 \x01(\x01\x12\r\n\x04sfbz\x18\xcd\x01 \x01(\x01\x12\x1c\n\x13\x43ostRate_Management\x18\xce\x01 \x01(\x01\x12\x19\n\x10\x43ostRate_Finance\x18\xcf\x01 \x01(\x01\x12\x1b\n\x12NetProfit_kcfjcxsy\x18\xd0\x01 \x01(\x01\x12\r\n\x04\x45\x42IT\x18\xd1\x01 \x01(\x01\x12\x0f\n\x06\x45\x42ITDA\x18\xd2\x01 \x01(\x01\x12\x1b\n\x12\x45\x42ITDA_TotalIncome\x18\xd3\x01 \x01(\x01\x12\x19\n\x10\x44\x65\x62tRatio_Assets\x18\xd4\x01 \x01(\x01\x12\x1c\n\x13Ratio_CurrentAssets\x18\xd5\x01 \x01(\x01\x12\x1c\n\x13Ratio_MonetaryFunds\x18\xd6\x01 \x01(\x01\x12\x18\n\x0fRatio_Inventory\x18\xd7\x01 \x01(\x01\x12\x1a\n\x11Ratio_FixedAssets\x18\xd8\x01 \x01(\x01\x12\x13\n\nRatio_fzjg\x18\xd9\x01 \x01(\x01\x12\x1a\n\x11gsymgsgdqy_qbtrzb\x18\xda\x01 \x01(\x01\x12\x12\n\tgdqy_dxzw\x18\xdb\x01 \x01(\x01\x12\x1f\n\x16TangibleAssets_NetDebt\x18\xdc\x01 \x01(\x01\x12\x17\n\x0e\x43\x61shFlow_mgjyx\x18\xdd\x01 \x01(\x01\x12\x19\n\x10\x43\x61shContent_yysr\x18\xde\x01 \x01(\x01\x12#\n\x1aNetCashFlow_NetIncome_jjhd\x18\xdf\x01 \x01(\x01\x12\x1b\n\x12\x43\x61sh_yysr_xssptglw\x18\xe0\x01 \x01(\x01\x12\x1e\n\x15NetCashFlow_yysr_jjhd\x18\xe1\x01 \x01(\x01\x12\x12\n\tzbcc_zktx\x18\xe2\x01 \x01(\x01\x12\"\n\x19\x43\x61shFlowPerShareNetAmount\x18\xe3\x01 \x01(\x01\x12$\n\x1bRatio_NetOperatingCashShort\x18\xe4\x01 \x01(\x01\x12#\n\x1aRatio_NetOperatingCashFull\x18\xe5\x01 \x01(\x01\x12(\n\x1fNetCashFlow_NetProfitRatio_jjhd\x18\xe6\x01 \x01(\x01\x12!\n\x18\x41llAssetCashRecoveryRate\x18\xe7\x01 \x01(\x01\x12\x18\n\x0fOperatingIncome\x18\xe8\x01 \x01(\x01\x12\x18\n\x0fOperatingProfit\x18\xe9\x01 \x01(\x01\x12\x1c\n\x13NetProfit_gsymgssyz\x18\xea\x01 \x01(\x01\x12\x1b\n\x12NetProfit_kcfjjxsy\x18\xeb\x01 \x01(\x01\x12\x19\n\x10NetCashFlow_jyhd\x18\xec\x01 \x01(\x01\x12\x19\n\x10NetCashFlow_tzhd\x18\xed\x01 \x01(\x01\x12\x19\n\x10NetCashFlow_czhd\x18\xee\x01 \x01(\x01\x12%\n\x1cNetIncreaseAmount_Cash_xjdjw\x18\xef\x01 \x01(\x01\x12\x1a\n\x11TotalShareCapital\x18\xf0\x01 \x01(\x01\x12\x14\n\x0b\x41Shares_ysh\x18\xf1\x01 \x01(\x01\x12\x14\n\x0b\x42Shares_ysh\x18\xf2\x01 \x01(\x01\x12\x14\n\x0bHShares_ysh\x18\xf3\x01 \x01(\x01\x12\x16\n\rShareHolders_\x18\xf4\x01 \x01(\x01\x12\x1d\n\x14HoldingsNumber_dydgd\x18\xf5\x01 \x01(\x01\x12\x1b\n\x12TotalNumber_sdltgd\x18\xf6\x01 \x01(\x01\x12\x19\n\x10TotalNumber_sdgd\x18\xf7\x01 \x01(\x01\x12\x11\n\x08jg_Total\x18\xf8\x01 \x01(\x01\x12\x19\n\x10TotalHoldings_jg\x18\xf9\x01 \x01(\x01\x12\x10\n\x07jg_QFII\x18\xfa\x01 \x01(\x01\x12\x19\n\x10Holdings_QFII_jg\x18\xfb\x01 \x01(\x01\x12\x0e\n\x05jg_qs\x18\xfc\x01 \x01(\x01\x12\x17\n\x0eHoldings_jg_qs\x18\xfd\x01 \x01(\x01\x12\x15\n\x0cjg_Insurance\x18\xfe\x01 \x01(\x01\x12\x1e\n\x15Holdings_jg_Insurance\x18\xff\x01 \x01(\x01\x12\x10\n\x07jg_Fund\x18\x80\x02 \x01(\x01\x12\x19\n\x10Holdings_jg_Fund\x18\x81\x02 \x01(\x01\x12\x0e\n\x05jg_sb\x18\x82\x02 \x01(\x01\x12\x17\n\x0eHoldings_jg_sb\x18\x83\x02 \x01(\x01\x12\x0e\n\x05jg_sm\x18\x84\x02 \x01(\x01\x12\x17\n\x0eHoldings_jg_sm\x18\x85\x02 \x01(\x01\x12\x10\n\x07jg_cwgs\x18\x86\x02 \x01(\x01\x12\x19\n\x10Holdings_jg_cwgs\x18\x87\x02 \x01(\x01\x12\x0e\n\x05jg_nj\x18\x88\x02 \x01(\x01\x12\x17\n\x0eHoldings_jg_nj\x18\x89\x02 \x01(\x01\x12\x1f\n\x16Holdings_AStock_sdltgd\x18\x8a\x02 \x01(\x01\x12\x19\n\x10Holdings_dydltgd\x18\x8b\x02 \x01(\x01\x12\x1b\n\x12\x46reeTradableShares\x18\x8c\x02 \x01(\x01\x12\x14\n\x0b\x41Stock_sxlt\x18\x8d\x02 \x01(\x01\x12\x1f\n\x16GeneralRiskPreparation\x18\x8e\x02 \x01(\x01\x12\x16\n\rIncome_qtzhsy\x18\x8f\x02 \x01(\x01\x12\x14\n\x0bTotalIncome\x18\x90\x02 \x01(\x01\x12\x13\n\ngsymgsgdqy\x18\x91\x02 \x01(\x01\x12\x10\n\x07jg_Bank\x18\x92\x02 \x01(\x01\x12\x19\n\x10Holdings_jg_Bank\x18\x93\x02 \x01(\x01\x12\x10\n\x07jg_ybfr\x18\x94\x02 \x01(\x01\x12\x19\n\x10Holdings_jg_ybfr\x18\x95\x02 \x01(\x01\x12\x16\n\rNetProfitYear\x18\x96\x02 \x01(\x01\x12\x0e\n\x05jg_xt\x18\x97\x02 \x01(\x01\x12\x17\n\x0eHoldings_jg_xt\x18\x98\x02 \x01(\x01\x12\x10\n\x07jg_tsfr\x18\x99\x02 \x01(\x01\x12\x19\n\x10Holdings_jg_tsfr\x18\x9a\x02 \x01(\x01\x12\x15\n\x0cNetProfit_jq\x18\x9b\x02 \x01(\x01\x12\x1a\n\x11PerShareProfit_kf\x18\x9c\x02 \x01(\x01\x12\x12\n\tzjyn_yysr\x18\x9d\x02 \x01(\x01\x12\x15\n\x0cHoldings_gjd\x18\x9e\x02 \x01(\x01\x12\x17\n\x0eywyg_jlrtbzfxx\x18\x9f\x02 \x01(\x01\x12\x17\n\x0eyjyg_jlrtbzfsx\x18\xa0\x02 \x01(\x01\x12\x13\n\nyjkb_gmjlr\x18\xa1\x02 \x01(\x01\x12\x13\n\nyjkb_kfjlr\x18\xa2\x02 \x01(\x01\x12\x11\n\x08yjkb_zzc\x18\xa3\x02 \x01(\x01\x12\x11\n\x08yjkb_jzc\x18\xa4\x02 \x01(\x01\x12\x12\n\tyjkb_mgsy\x18\xa5\x02 \x01(\x01\x12\x16\n\ryjkb_tbjzcsyl\x18\xa6\x02 \x01(\x01\x12\x16\n\ryjkb_jqjzcsyl\x18\xa7\x02 \x01(\x01\x12\x13\n\nyjkb_mgjzc\x18\xa8\x02 \x01(\x01\x12\x12\n\tyfpj_yfzk\x18\xa9\x02 \x01(\x01\x12\x12\n\tyspj_yszk\x18\xaa\x02 \x01(\x01\x12\x1c\n\x13\x44\x65\x66\x65rredIncome_zcfz\x18\xab\x02 \x01(\x01\x12\x0f\n\x06qtzhsy\x18\xac\x02 \x01(\x01\x12\x0f\n\x06qtyqgj\x18\xad\x02 \x01(\x01\x12\x13\n\nOtherGains\x18\xae\x02 \x01(\x01\x12\x0f\n\x06zcczsy\x18\xaf\x02 \x01(\x01\x12\x17\n\x0eNetProfit_cxjy\x18\xb0\x02 \x01(\x01\x12\x17\n\x0eNetProfit_zzjy\x18\xb1\x02 \x01(\x01\x12\x16\n\rCosts_Develop\x18\xb2\x02 \x01(\x01\x12\x15\n\x0cInclude_lxfy\x18\xb3\x02 \x01(\x01\x12\x15\n\x0cInclude_lxsr\x18\xb4\x02 \x01(\x01\x12\x18\n\x0fNetCash_jynjjhd\x18\xb5\x02 \x01(\x01\x12\x18\n\x0fNetProfit_jyngm\x18\xb6\x02 \x01(\x01\x12\x18\n\x0fNetProfit_jynkf\x18\xb7\x02 \x01(\x01\x12\x18\n\x0fNetCashFlow_jyn\x18\xb8\x02 \x01(\x01\x12\x1b\n\x12PerShareProfit_djd\x18\xb9\x02 \x01(\x01\x12\x12\n\tyyzsr_djd\x18\xba\x02 \x01(\x01\x12\x12\n\tyjyg_ggrq\x18\xbb\x02 \x01(\x01\x12\x10\n\x07\x63w_ggrq\x18\xbc\x02 \x01(\x01\x12\x12\n\tyjkb_ggrq\x18\xbd\x02 \x01(\x01\x12\x1c\n\x13NetCashFlow_jyntzhd\x18\xbe\x02 \x01(\x01\x12\x15\n\x0cyjyg_bqjlrxx\x18\xbf\x02 \x01(\x01\x12\x15\n\x0cyjyg_bqjlrsx\x18\xc0\x02 \x01(\x01\x12\x12\n\tyyzsr_TTM\x18\xc1\x02 \x01(\x01\x12\x1e\n\x15TotalNumber_Employees\x18\xc2\x02 \x01(\x01\x12\x1f\n\x16\x46reCashFlowPerShareEnp\x18\xc3\x02 \x01(\x01\x12\x1f\n\x16\x46reCashFlowPerShareHol\x18\xc4\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_323\x18\xc5\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_324\x18\xc6\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_325\x18\xc7\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_326\x18\xc8\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_327\x18\xc9\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_328\x18\xca\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_329\x18\xcb\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_330\x18\xcc\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_331\x18\xcd\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_332\x18\xce\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_333\x18\xcf\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_334\x18\xd0\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_335\x18\xd1\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_336\x18\xd2\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_337\x18\xd3\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_338\x18\xd4\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_339\x18\xd5\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_340\x18\xd6\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_341\x18\xd7\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_342\x18\xd8\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_343\x18\xd9\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_344\x18\xda\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_345\x18\xdb\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_346\x18\xdc\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_347\x18\xdd\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_348\x18\xde\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_349\x18\xdf\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_350\x18\xe0\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_351\x18\xe1\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_352\x18\xe2\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_353\x18\xe3\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_354\x18\xe4\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_355\x18\xe5\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_356\x18\xe6\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_357\x18\xe7\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_358\x18\xe8\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_359\x18\xe9\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_360\x18\xea\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_361\x18\xeb\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_362\x18\xec\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_363\x18\xed\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_364\x18\xee\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_365\x18\xef\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_366\x18\xf0\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_367\x18\xf1\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_368\x18\xf2\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_369\x18\xf3\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_370\x18\xf4\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_371\x18\xf5\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_372\x18\xf6\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_373\x18\xf7\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_374\x18\xf8\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_375\x18\xf9\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_376\x18\xfa\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_377\x18\xfb\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_378\x18\xfc\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_379\x18\xfd\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_380\x18\xfe\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_381\x18\xff\x02 \x01(\x01\x12\x15\n\x0c\x46inValue_382\x18\x80\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_383\x18\x81\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_384\x18\x82\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_385\x18\x83\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_386\x18\x84\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_387\x18\x85\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_388\x18\x86\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_389\x18\x87\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_390\x18\x88\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_391\x18\x89\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_392\x18\x8a\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_393\x18\x8b\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_394\x18\x8c\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_395\x18\x8d\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_396\x18\x8e\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_397\x18\x8f\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_398\x18\x90\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_399\x18\x91\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_400\x18\x92\x03 \x01(\x01\x12\x18\n\x0fSpecialReserves\x18\x93\x03 \x01(\x01\x12\x1b\n\x12SettlementReserves\x18\x94\x03 \x01(\x01\x12\x15\n\x0c\x44isfundFunds\x18\x95\x03 \x01(\x01\x12\x1b\n\x12MakeLoans_Advances\x18\x96\x03 \x01(\x01\x12\"\n\x19\x44\x65rivativeFinancialAssets\x18\x97\x03 \x01(\x01\x12\x1a\n\x11PremiumReceivable\x18\x98\x03 \x01(\x01\x12\x1b\n\x12\x41\x63\x63ountsReceivable\x18\x99\x03 \x01(\x01\x12\x12\n\tysfbhtzbj\x18\x9a\x03 \x01(\x01\x12\x11\n\x08mrfsjrzc\x18\x9b\x03 \x01(\x01\x12\x0f\n\x06\x63ydszc\x18\x9c\x03 \x01(\x01\x12\x0f\n\x06\x66\x66\x64kdk\x18\x9d\x03 \x01(\x01\x12\x1c\n\x13\x42orrowingMoney_zyyh\x18\x9e\x03 \x01(\x01\x12\x12\n\txsck_tycf\x18\x9f\x03 \x01(\x01\x12\r\n\x04\x63rzj\x18\xa0\x03 \x01(\x01\x12\x0f\n\x06ysjrfz\x18\xa1\x03 \x01(\x01\x12\x12\n\tmchgjrzck\x18\xa2\x03 \x01(\x01\x12\x13\n\nyfsxf_yfyj\x18\xa3\x03 \x01(\x01\x12\x0f\n\x06yffbzk\x18\xa4\x03 \x01(\x01\x12\x19\n\x10InsuranceReserve\x18\xa5\x03 \x01(\x01\x12\x10\n\x07\x64lmmzqk\x18\xa6\x03 \x01(\x01\x12\x10\n\x07\x64lcxzqk\x18\xa7\x03 \x01(\x01\x12\x10\n\x07wcydsfz\x18\xa8\x03 \x01(\x01\x12\r\n\x04yjfz\x18\xa9\x03 \x01(\x01\x12\x17\n\x0e\x44\x65\x66\x65rredIncome\x18\xaa\x03 \x01(\x01\x12\x1a\n\x11Include_yxg_fldfz\x18\xab\x03 \x01(\x01\x12\x1c\n\x13SustainedDebt_fldfz\x18\xac\x03 \x01(\x01\x12\x11\n\x08\x63qyfzgxc\x18\xad\x03 \x01(\x01\x12\x1a\n\x11Include_yxg_syzqy\x18\xae\x03 \x01(\x01\x12\x1c\n\x13SustainedDebt_syzqy\x18\xaf\x03 \x01(\x01\x12\x17\n\x0e\x44\x65\x62tInvestment\x18\xb0\x03 \x01(\x01\x12\x1d\n\x14OtherDebtInvestments\x18\xb1\x03 \x01(\x01\x12\x11\n\x08qtqygjtz\x18\xb2\x03 \x01(\x01\x12\x12\n\tqtfldjrzc\x18\xb3\x03 \x01(\x01\x12\x15\n\x0c\x43ontractDebt\x18\xb4\x03 \x01(\x01\x12\x17\n\x0e\x43ontractAssets\x18\xb5\x03 \x01(\x01\x12\x14\n\x0bOtherAssets\x18\xb6\x03 \x01(\x01\x12\x1d\n\x14ReceivablesFinancing\x18\xb7\x03 \x01(\x01\x12\x17\n\x0eUseRightAssets\x18\xb8\x03 \x01(\x01\x12\x1b\n\x12LeasingLiabilities\x18\xb9\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_440\x18\xba\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_441\x18\xbb\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_442\x18\xbc\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_443\x18\xbd\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_444\x18\xbe\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_445\x18\xbf\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_446\x18\xc0\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_447\x18\xc1\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_448\x18\xc2\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_449\x18\xc3\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_450\x18\xc4\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_451\x18\xc5\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_452\x18\xc6\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_453\x18\xc7\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_454\x18\xc8\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_455\x18\xc9\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_456\x18\xca\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_457\x18\xcb\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_458\x18\xcc\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_459\x18\xcd\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_460\x18\xce\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_461\x18\xcf\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_462\x18\xd0\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_463\x18\xd1\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_464\x18\xd2\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_465\x18\xd3\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_466\x18\xd4\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_467\x18\xd5\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_468\x18\xd6\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_469\x18\xd7\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_470\x18\xd8\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_471\x18\xd9\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_472\x18\xda\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_473\x18\xdb\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_474\x18\xdc\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_475\x18\xdd\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_476\x18\xde\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_477\x18\xdf\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_478\x18\xe0\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_479\x18\xe1\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_480\x18\xe2\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_481\x18\xe3\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_482\x18\xe4\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_483\x18\xe5\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_484\x18\xe6\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_485\x18\xe7\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_486\x18\xe8\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_487\x18\xe9\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_488\x18\xea\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_489\x18\xeb\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_490\x18\xec\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_491\x18\xed\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_492\x18\xee\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_493\x18\xef\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_494\x18\xf0\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_495\x18\xf1\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_496\x18\xf2\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_497\x18\xf3\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_498\x18\xf4\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_499\x18\xf5\x03 \x01(\x01\x12\x15\n\x0c\x46inValue_500\x18\xf6\x03 \x01(\x01\x12\x1a\n\x11PerShareProfit_xs\x18\xf7\x03 \x01(\x01\x12\x0e\n\x05yyzsr\x18\xf8\x03 \x01(\x01\x12\x16\n\rExchangProfit\x18\xf9\x03 \x01(\x01\x12\x1b\n\x12Include_gsymgszhsy\x18\xfa\x03 \x01(\x01\x12\x1c\n\x13Include_gsyssgdzhsy\x18\xfb\x03 \x01(\x01\x12\r\n\x04lxsr\x18\xfc\x03 \x01(\x01\x12\x16\n\rEarnedPremium\x18\xfd\x03 \x01(\x01\x12\x11\n\x08sxf_yjsr\x18\xfe\x03 \x01(\x01\x12\r\n\x04lxzc\x18\xff\x03 \x01(\x01\x12\x11\n\x08sxf_yjzc\x18\x80\x04 \x01(\x01\x12\x12\n\tSurrender\x18\x81\x04 \x01(\x01\x12\x0f\n\x06pfzcje\x18\x82\x04 \x01(\x01\x12\x14\n\x0btqbxhtzbjje\x18\x83\x04 \x01(\x01\x12\x0f\n\x06\x62\x64hlzc\x18\x84\x04 \x01(\x01\x12\x19\n\x10SubinsuranceCost\x18\x85\x04 \x01(\x01\x12\x1a\n\x11Include_fldzcczld\x18\x86\x04 \x01(\x01\x12\x13\n\nCreditLoss\x18\x87\x04 \x01(\x01\x12\x10\n\x07jcktqsy\x18\x88\x04 \x01(\x01\x12\x0e\n\x05yyzcb\x18\x89\x04 \x01(\x01\x12\x17\n\x0e\x43reditLoss_old\x18\x8a\x04 \x01(\x01\x12\x17\n\x0e\x41ssetsLoss_old\x18\x8b\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_522\x18\x8c\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_523\x18\x8d\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_524\x18\x8e\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_525\x18\x8f\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_526\x18\x90\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_527\x18\x91\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_528\x18\x92\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_529\x18\x93\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_530\x18\x94\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_531\x18\x95\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_532\x18\x96\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_533\x18\x97\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_534\x18\x98\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_535\x18\x99\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_536\x18\x9a\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_537\x18\x9b\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_538\x18\x9c\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_539\x18\x9d\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_540\x18\x9e\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_541\x18\x9f\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_542\x18\xa0\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_543\x18\xa1\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_544\x18\xa2\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_545\x18\xa3\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_546\x18\xa4\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_547\x18\xa5\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_548\x18\xa6\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_549\x18\xa7\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_550\x18\xa8\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_551\x18\xa9\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_552\x18\xaa\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_553\x18\xab\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_554\x18\xac\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_555\x18\xad\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_556\x18\xae\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_557\x18\xaf\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_558\x18\xb0\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_559\x18\xb1\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_560\x18\xb2\x04 \x01(\x01\x12\x15\n\x0c\x41\x64\x64_qtyyyxxj\x18\xb3\x04 \x01(\x01\x12\x1f\n\x16NetIncrease_khck_tycfk\x18\xb4\x04 \x01(\x01\x12\x1b\n\x12NetIncrease_zyyhjk\x18\xb5\x04 \x01(\x01\x12\x1d\n\x14NetIncrease_jrjgcrzj\x18\xb6\x04 \x01(\x01\x12\x1d\n\x14\x43\x61shReceived_ybxhtbf\x18\xb7\x04 \x01(\x01\x12\x1c\n\x13NetCashReceived_zbx\x18\xb8\x04 \x01(\x01\x12\x1d\n\x14NetIncrease_bhcj_tzk\x18\xb9\x04 \x01(\x01\x12#\n\x1aNetIncrease_gyjz_dqsy_jrzc\x18\xba\x04 \x01(\x01\x12\x1f\n\x16\x43\x61shReceived_lx_sxf_yj\x18\xbb\x04 \x01(\x01\x12\x19\n\x10NetIncrease_crzj\x18\xbc\x04 \x01(\x01\x12\x1b\n\x12NetIncrease_hgywzj\x18\xbd\x04 \x01(\x01\x12\x1b\n\x12NetIncrease_khdkdk\x18\xbe\x04 \x01(\x01\x12\x1f\n\x16NetIncrease_cfzyyh_tyk\x18\xbf\x04 \x01(\x01\x12\x1a\n\x11\x43\x61shPaid_ybxhtphk\x18\xc0\x04 \x01(\x01\x12\x1b\n\x12\x43\x61shPaid_lx_sxf_yj\x18\xc1\x04 \x01(\x01\x12\x16\n\rCashPaid_bdhl\x18\xc2\x04 \x01(\x01\x12)\n Include_CashReceived_zgsxsssgdtz\x18\xc3\x04 \x01(\x01\x12 \n\x17Include_zgszfgssgd_gllr\x18\xc4\x04 \x01(\x01\x12\x13\n\ntzxfdczjtx\x18\xc5\x04 \x01(\x01\x12\x17\n\x0e\x43reditLoss_New\x18\xc6\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_581\x18\xc7\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_582\x18\xc8\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_583\x18\xc9\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_584\x18\xca\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_585\x18\xcb\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_586\x18\xcc\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_587\x18\xcd\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_588\x18\xce\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_589\x18\xcf\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_590\x18\xd0\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_591\x18\xd1\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_592\x18\xd2\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_593\x18\xd3\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_594\x18\xd4\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_595\x18\xd5\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_596\x18\xd6\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_597\x18\xd7\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_598\x18\xd8\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_599\x18\xd9\x04 \x01(\x01\x12\x15\n\x0c\x46inValue_600\x18\xda\x04 \x01(\x01\"\xe0\x07\n\x11\x66ingpjy_down_data\x12\x0c\n\x04nkey\x18\x01 \x01(\x03\x12\x0c\n\x04\x44\x61te\x18\x02 \x01(\x03\x12\x14\n\x0cShareholders\x18\x03 \x01(\x01\x12\x12\n\nTotalBuyIn\x18\x04 \x01(\x01\x12\x14\n\x0cTotalSoldOut\x18\x05 \x01(\x01\x12\x12\n\nrzye_rzrq1\x18\x06 \x01(\x01\x12\x12\n\nrqyl_rzrq1\x18\x07 \x01(\x01\x12\x0e\n\x06rzrqye\x18\x08 \x01(\x01\x12\x11\n\tcjjj_dzjy\x18\t \x01(\x01\x12\x10\n\x08\x63je_dzjy\x18\n \x01(\x01\x12\x10\n\x08\x63jjj_zjc\x18\x0b \x01(\x01\x12\x18\n\x10ShareChanged_zjc\x18\x0c \x01(\x01\x12\x19\n\x11ShareHoldings_lgt\x18\r \x01(\x01\x12\x14\n\x0cNetBuyIn_lgt\x18\x0e \x01(\x01\x12\x11\n\tjg_Seller\x18\x0f \x01(\x01\x12\x19\n\x11SoldOut_jg_Seller\x18\x10 \x01(\x01\x12\x10\n\x08jg_Buyer\x18\x11 \x01(\x01\x12\x16\n\x0e\x42uyIn_jg_Buyer\x18\x12 \x01(\x01\x12\x11\n\tjsyjgdycs\x18\x13 \x01(\x01\x12\x11\n\tjsydyjgsl\x18\x14 \x01(\x01\x12\x10\n\x08\x42uyIn_rz\x18\x15 \x01(\x01\x12\r\n\x05rzche\x18\x16 \x01(\x01\x12\x12\n\nSoldOut_rq\x18\x17 \x01(\x01\x12\r\n\x05rqchl\x18\x18 \x01(\x01\x12\x11\n\tNetBuy_rz\x18\x19 \x01(\x01\x12\x12\n\nNetSale_rq\x18\x1a \x01(\x01\x12\x0c\n\x04None\x18\x1b \x01(\x01\x12\x11\n\tZDTStatus\x18\x1c \x01(\x01\x12\x10\n\x08ZDTFDNum\x18\x1d \x01(\x01\x12\x0b\n\x03zsz\x18\x1e \x01(\x01\x12\x18\n\x10lhbyybData_BuyIN\x18\x1f \x01(\x01\x12\x1a\n\x12lhbyybData_SellOut\x18  \x01(\x01\x12\x19\n\x11lhbhsgtData_BuyIN\x18! \x01(\x01\x12\x1b\n\x13lhbhsgtData_SellOut\x18\" \x01(\x01\x12\x17\n\x0fwx_StockPerWeek\x18# \x01(\x01\x12\x17\n\x0fyx_StockPerWeek\x18$ \x01(\x01\x12\x10\n\x08zy_Ratio\x18% \x01(\x01\x12\x14\n\x0cGPJYValue_21\x18& \x01(\x01\x12\x14\n\x0cGPJYValue_22\x18\' \x01(\x01\x12\x14\n\x0cGPJYValue_23\x18( \x01(\x01\x12\x14\n\x0cGPJYValue_24\x18) \x01(\x01\x12\x14\n\x0cGPJYValue_25\x18* \x01(\x01\x12\x14\n\x0cGPJYValue_26\x18+ \x01(\x01\x12\x14\n\x0cGPJYValue_27\x18, \x01(\x01\x12\x14\n\x0cGPJYValue_28\x18- \x01(\x01\x12\x14\n\x0cGPJYValue_29\x18. \x01(\x01\x12\x14\n\x0cGPJYValue_30\x18/ \x01(\x01\"\xa7\n\n\x11\x66inscjy_down_data\x12\x0c\n\x04nkey\x18\x01 \x01(\x03\x12\x0c\n\x04\x44\x61te\x18\x02 \x01(\x03\x12\x0e\n\x06hsrzye\x18\x03 \x01(\x01\x12\x0e\n\x06hsrqye\x18\x04 \x01(\x01\x12\x12\n\nFlowIn_hgt\x18\x05 \x01(\x01\x12\x12\n\nFlowIn_sgt\x18\x06 \x01(\x01\x12\x10\n\x08Stock_zt\x18\x07 \x01(\x01\x12\x11\n\tStock_zzt\x18\x08 \x01(\x01\x12\x10\n\x08Stock_dt\x18\t \x01(\x01\x12\x11\n\tStock_zdt\x18\n \x01(\x01\x12\x17\n\x0fNetHolding_sz50\x18\x0b \x01(\x01\x12\x18\n\x10NetHolding_hs300\x18\x0c \x01(\x01\x12\x18\n\x10NetHolding_zz500\x18\r \x01(\x01\x12\x14\n\x0c\x45TF_FundSize\x18\x0e \x01(\x01\x12\x0f\n\x07\x45TF_jss\x18\x0f \x01(\x01\x12\x14\n\x0cNewInvestors\x18\x10 \x01(\x01\x12\x14\n\x0cIncreaseHold\x18\x11 \x01(\x01\x12\x14\n\x0c\x44\x65\x63reaseHold\x18\x12 \x01(\x01\x12\x0f\n\x07yjdzjye\x18\x13 \x01(\x01\x12\x0f\n\x07zjdzjye\x18\x14 \x01(\x01\x12\x0f\n\x07xsjjjhe\x18\x15 \x01(\x01\x12\x14\n\x0cxsjjgfsjssje\x18\x16 \x01(\x01\x12\x0e\n\x06sczfhe\x18\x17 \x01(\x01\x12\x0e\n\x06sczmze\x18\x18 \x01(\x01\x12\x0e\n\x06\x66\x62\x63gzj\x18\x19 \x01(\x01\x12\x0e\n\x06\x66\x62sbzj\x18\x1a \x01(\x01\x12\x16\n\x0e\x42uyInTotal_lhb\x18\x1b \x01(\x01\x12\x18\n\x10SellOutTotal_lhb\x18\x1c \x01(\x01\x12\x18\n\x10\x42uyInTotal_lhbjg\x18\x1d \x01(\x01\x12\x1a\n\x12SellOutTotal_lhbjg\x18\x1e \x01(\x01\x12\x19\n\x11\x42uyInTotal_lhbyyb\x18\x1f \x01(\x01\x12\x1b\n\x13SellOutTotal_lhbyyb\x18  \x01(\x01\x12\x1a\n\x12\x42uyInTotal_lhbhsgt\x18! \x01(\x01\x12\x1c\n\x14SellOutTotal_lhbhsgt\x18\" \x01(\x01\x12\x19\n\x11\x42uyInTotal_lhblgt\x18# \x01(\x01\x12\x1b\n\x13SellOutTotal_lhblgt\x18$ \x01(\x01\x12\x10\n\x08sszyl_wx\x18% \x01(\x01\x12\x10\n\x08hszyl_wx\x18& \x01(\x01\x12\x10\n\x08sszyl_yx\x18\' \x01(\x01\x12\x10\n\x08hszyl_yx\x18( \x01(\x01\x12\x12\n\nlbg_WithST\x18) \x01(\x01\x12\x15\n\rlbg_WithOutST\x18* \x01(\x01\x12\x15\n\rztg_WithOutST\x18+ \x01(\x01\x12\x15\n\rdtg_WithOutST\x18, \x01(\x01\x12\x12\n\nBuyIn_hsrz\x18- \x01(\x01\x12\x14\n\x0cSellOut_hsrz\x18. \x01(\x01\x12\x14\n\x0czyRatio_mzsc\x18/ \x01(\x01\x12\x14\n\x0cSCJYValue_27\x18\x30 \x01(\x01\x12\x14\n\x0cSCJYValue_28\x18\x31 \x01(\x01\x12\x14\n\x0cSCJYValue_29\x18\x32 \x01(\x01\x12\x14\n\x0cSCJYValue_30\x18\x33 \x01(\x01\x12\x14\n\x0cSCJYValue_31\x18\x34 \x01(\x01\x12\x14\n\x0cSCJYValue_32\x18\x35 \x01(\x01\x12\x14\n\x0cSCJYValue_33\x18\x36 \x01(\x01\x12\x14\n\x0cSCJYValue_34\x18\x37 \x01(\x01\x12\x14\n\x0cSCJYValue_35\x18\x38 \x01(\x01\x12\x14\n\x0cSCJYValue_36\x18\x39 \x01(\x01\x12\x14\n\x0cSCJYValue_37\x18: \x01(\x01\x12\x14\n\x0cSCJYValue_38\x18; \x01(\x01\x12\x14\n\x0cSCJYValue_39\x18< \x01(\x01\x12\x14\n\x0cSCJYValue_40\x18= \x01(\x01\"\xbc\x08\n\x16\x66ingponedate_down_data\x12\x0c\n\x04nkey\x18\x01 \x01(\x03\x12\x0c\n\x04\x44\x61te\x18\x02 \x01(\x03\x12\x0b\n\x03\x66xj\x18\x03 \x01(\x01\x12\r\n\x05zfxsl\x18\x04 \x01(\x01\x12\x18\n\x10TargetPrice_yzyq\x18\x05 \x01(\x01\x12\x12\n\nTYear_yzyq\x18\x06 \x01(\x01\x12!\n\x19PerShareProfit_TYear_yzyq\x18\x07 \x01(\x01\x12\"\n\x1aPerShareProfit_T1Year_yzyq\x18\x08 \x01(\x01\x12\"\n\x1aPerShareProfit_T2Year_yzyq\x18\t \x01(\x01\x12\x1c\n\x14NetProfit_TYear_yzyq\x18\n \x01(\x01\x12\x1d\n\x15NetProfit_T1Year_yzyq\x18\x0b \x01(\x01\x12\x1d\n\x15NetProfit_T2Year_yzyq\x18\x0c \x01(\x01\x12\x1a\n\x12Receipt_TYear_yzyq\x18\r \x01(\x01\x12\x1b\n\x13Receipt_T1Year_yzyq\x18\x0e \x01(\x01\x12\x1b\n\x13Receipt_T2Year_yzyq\x18\x0f \x01(\x01\x12\x19\n\x11Profit_TYear_yzyq\x18\x10 \x01(\x01\x12\x1a\n\x12Profit_T1Year_yzyq\x18\x11 \x01(\x01\x12\x1a\n\x12Profit_T2Year_yzyq\x18\x12 \x01(\x01\x12#\n\x1bNetAssetPerShare_TYear_yzyq\x18\x13 \x01(\x01\x12$\n\x1cNetAssetPerShare_T1Year_yzyq\x18\x14 \x01(\x01\x12$\n\x1cNetAssetPerShare_T2Year_yzyq\x18\x15 \x01(\x01\x12\x1c\n\x14RateYield_TYear_yzyq\x18\x16 \x01(\x01\x12\x1d\n\x15RateYield_T1Year_yzyq\x18\x17 \x01(\x01\x12\x1d\n\x15RateYield_T2Year_yzyq\x18\x18 \x01(\x01\x12\x15\n\rPE_TYear_yzyq\x18\x19 \x01(\x01\x12\x16\n\x0ePE_T1Year_yzyq\x18\x1a \x01(\x01\x12\x16\n\x0ePE_T2Year_yzyq\x18\x1b \x01(\x01\x12\x12\n\njjr_Latest\x18\x1c \x01(\x01\x12\x13\n\x0bjjsl_Latest\x18\x1d \x01(\x01\x12\x13\n\x0bGPONEDat_28\x18\x1e \x01(\x01\x12\x13\n\x0bGPONEDat_29\x18\x1f \x01(\x01\x12\x13\n\x0bGPONEDat_30\x18  \x01(\x01\x12\x13\n\x0bGPONEDat_31\x18! \x01(\x01\x12\x13\n\x0bGPONEDat_32\x18\" \x01(\x01\x12\x13\n\x0bGPONEDat_33\x18# \x01(\x01\x12\x13\n\x0bGPONEDat_34\x18$ \x01(\x01\x12\x13\n\x0bGPONEDat_35\x18% \x01(\x01\x12\x13\n\x0bGPONEDat_36\x18& \x01(\x01\x12\x13\n\x0bGPONEDat_37\x18\' \x01(\x01\x12\x13\n\x0bGPONEDat_38\x18( \x01(\x01\x12\x13\n\x0bGPONEDat_39\x18) \x01(\x01\x12\x13\n\x0bGPONEDat_40\x18* \x01(\x01\"F\n\x10\x66inance_down_ans\x12\x32\n\x05\x66ield\x18\x01 \x03(\x0b\x32#.wuhan.jcl.report.finance_down_data\"H\n\x11\x66invalue_down_ans\x12\x33\n\x05\x66ield\x18\x01 \x03(\x0b\x32$.wuhan.jcl.report.finvalue_down_data\"F\n\x10\x66ingpjy_down_ans\x12\x32\n\x05\x66ield\x18\x01 \x03(\x0b\x32#.wuhan.jcl.report.fingpjy_down_data\"F\n\x10\x66inscjy_down_ans\x12\x32\n\x05\x66ield\x18\x01 \x03(\x0b\x32#.wuhan.jcl.report.finscjy_down_data\"P\n\x15\x66ingponedate_down_ans\x12\x37\n\x05\x66ield\x18\x01 \x03(\x0b\x32(.wuhan.jcl.report.fingponedate_down_data\"/\n\x0f\x66inance_zip_req\x12\r\n\x05sdate\x18\x01 \x01(\r\x12\r\n\x05\x65\x64\x61te\x18\x02 \x01(\r\".\n\x10\x66inance_zip_data\x12\x0c\n\x04\x64\x61te\x18\x01 \x01(\r\x12\x0c\n\x04\x64\x61ta\x18\x02 \x01(\t\"F\n\x0f\x66inance_zip_ans\x12\x33\n\x07zipdata\x18\x01 \x03(\x0b\x32\".wuhan.jcl.report.finance_zip_data\"5\n\x08pzyd_ans\x12)\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32\x1b.wuhan.jcl.report.dxjl_data\"8\n\x0coldname_data\x12\x0c\n\x04nkey\x18\x01 \x01(\x04\x12\x0c\n\x04\x64\x61te\x18\x02 \x01(\r\x12\x0c\n\x04name\x18\x03 \x01(\t\"<\n\x0boldname_ans\x12-\n\x05\x66ield\x18\x01 \x03(\x0b\x32\x1e.wuhan.jcl.report.oldname_data\"L\n\x0fsnmoneyflow_req\x12\x0c\n\x04type\x18\x01 \x01(\r\x12\x0e\n\x06period\x18\x02 \x01(\r\x12\x0e\n\x06offset\x18\x03 \x01(\r\x12\x0b\n\x03num\x18\x04 \x01(\r\")\n\x0bsnmoney_req\x12\x0c\n\x04type\x18\x01 \x01(\r\x12\x0c\n\x04\x64\x61te\x18\x02 \x01(\r\"Z\n\x0fsnmoneyhold_req\x12\x0c\n\x04type\x18\x01 \x01(\r\x12\x0c\n\x04\x64\x61te\x18\x02 \x01(\r\x12\x0e\n\x06period\x18\x03 \x01(\r\x12\x0e\n\x06offset\x18\x04 \x01(\r\x12\x0b\n\x03num\x18\x05 \x01(\r\"8\n\thsizs_req\x12\x0e\n\x06period\x18\x01 \x01(\r\x12\x0e\n\x06offset\x18\x02 \x01(\r\x12\x0b\n\x03num\x18\x03 \x01(\r\")\n\nhsizs_data\x12\x0c\n\x04\x64\x61te\x18\x01 \x01(\r\x12\r\n\x05price\x18\x02 \x01(\x01\"7\n\thsizs_ans\x12*\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32\x1c.wuhan.jcl.report.hsizs_data\"-\n\x10snmoneyflow_data\x12\x0c\n\x04\x64\x61te\x18\x01 \x01(\r\x12\x0b\n\x03\x61mt\x18\x02 \x01(\x01\"C\n\x0fsnmoneyflow_ans\x12\x30\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32\".wuhan.jcl.report.snmoneyflow_data\"\xb5\x01\n\x0fsnmoneybase_ans\x12\x0c\n\x04\x64\x61te\x18\x01 \x01(\r\x12\x0e\n\x06\x62uyamt\x18\x02 \x01(\x01\x12\x0f\n\x07sellamt\x18\x03 \x01(\x01\x12\x12\n\ntotalquota\x18\x04 \x01(\x01\x12\x14\n\x0c\x62\x61lancequota\x18\x05 \x01(\x01\x12\x0f\n\x07userate\x18\x06 \x01(\x01\x12\x10\n\x08\x66low5day\x18\x07 \x01(\x01\x12\x12\n\nflow20days\x18\x08 \x01(\x01\x12\x12\n\nflow60days\x18\t \x01(\x01\"q\n\x0fsnrankinfo_data\x12\x0f\n\x07setcode\x18\x01 \x01(\r\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x0e\n\x06\x62uyamt\x18\x03 \x01(\x01\x12\x0f\n\x07sellamt\x18\x04 \x01(\x01\x12\x10\n\x08totalamt\x18\x05 \x01(\x01\x12\x0c\n\x04name\x18\x06 \x01(\t\"A\n\x0esnrankinfo_ans\x12/\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32!.wuhan.jcl.report.snrankinfo_data\"[\n\x10snmoneyhold_data\x12\x0f\n\x07setcode\x18\x01 \x01(\r\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x0c\n\x04hold\x18\x03 \x01(\x03\x12\x0c\n\x04rate\x18\x04 \x01(\x01\x12\x0c\n\x04name\x18\x05 \x01(\t\"C\n\x0fsnmoneyhold_ans\x12\x30\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32\".wuhan.jcl.report.snmoneyhold_data\"G\n\x0chqexdata_ans\x12\x0f\n\x07suspend\x18\x01 \x01(\r\x12\x11\n\tnflimitup\x18\x02 \x01(\r\x12\x13\n\x0blimitupopen\x18\x03 \x01(\r\"g\n\x0cltlbdata_ans\x12\r\n\x05yzbzt\x18\x01 \x01(\r\x12\n\n\x02zt\x18\x02 \x01(\r\x12\x0b\n\x03\x66\x62l\x18\x03 \x01(\x01\x12\x0b\n\x03lbl\x18\x04 \x01(\x01\x12\n\n\x02lb\x18\x05 \x01(\r\x12\n\n\x02zb\x18\x06 \x01(\r\x12\n\n\x02tp\x18\x07 \x01(\r\"H\n\x07minltlb\x12\x0f\n\x07jcltime\x18\x01 \x01(\x03\x12,\n\x04\x64\x61ta\x18\x02 \x01(\x0b\x32\x1e.wuhan.jcl.report.ltlbdata_ans\"6\n\x0bminltlb_ans\x12\'\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32\x19.wuhan.jcl.report.minltlb\"7\n\x07ltlbarr\x12\x0c\n\x04nkey\x18\x01 \x01(\x04\x12\r\n\x05state\x18\x02 \x01(\r\x12\x0f\n\x07volrate\x18\x03 \x01(\x01\"6\n\x0bltlbarr_ans\x12\'\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32\x19.wuhan.jcl.report.ltlbarr\"l\n\x08ltbj_ans\x12/\n\x07yesdata\x18\x01 \x01(\x0b\x32\x1e.wuhan.jcl.report.ltlbdata_ans\x12/\n\x07\x63urdata\x18\x02 \x01(\x0b\x32\x1e.wuhan.jcl.report.ltlbdata_ans\"8\n\x08\x62\x64qr_req\x12\x0c\n\x04type\x18\x01 \x01(\r\x12\x0f\n\x07\x62lknkey\x18\x02 \x01(\x04\x12\r\n\x05nkeys\x18\x03 \x03(\x04\"%\n\tbdqr_data\x12\x0c\n\x04nkey\x18\x01 \x01(\x04\x12\n\n\x02qr\x18\x02 \x01(\r\"5\n\x08\x62\x64qr_ans\x12)\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32\x1b.wuhan.jcl.report.bdqr_data\"q\n\x11ReCalcParamInfoEx\x12\x0e\n\x06szName\x18\x01 \x01(\t\x12\x13\n\x0bszParamInfo\x18\x02 \x01(\t\x12\x0c\n\x04\x66Min\x18\x03 \x01(\x01\x12\x0c\n\x04\x66Max\x18\x04 \x01(\x01\x12\x0c\n\x04\x64Val\x18\x05 \x01(\x01\x12\r\n\x05\x66Step\x18\x06 \x01(\x01\"\xbd\x01\n\x12RFormularSelfIndex\x12\x0c\n\x04nSet\x18\x01 \x01(\r\x12\r\n\x05szKey\x18\x02 \x01(\t\x12\x0e\n\x06szName\x18\x03 \x01(\t\x12\r\n\x05\x64wKey\x18\x04 \x01(\x04\x12\x0f\n\x07\x64wGroup\x18\x05 \x01(\r\x12\x11\n\tnDrawMode\x18\x06 \x01(\r\x12\x12\n\ndGridInfos\x18\x07 \x03(\x01\x12\x33\n\x06params\x18\x08 \x03(\x0b\x32#.wuhan.jcl.report.ReCalcParamInfoEx\"\\\n\x12RFormularGroupList\x12\r\n\x05group\x18\x01 \x01(\t\x12\x37\n\tformulars\x18\x02 \x03(\x0b\x32$.wuhan.jcl.report.RFormularSelfIndex\"5\n\x16RFormularSelfIndex_Req\x12\x0c\n\x04nSet\x18\x01 \x01(\r\x12\r\n\x05szKey\x18\x02 \x01(\t\" \n\x10RFormularSet_Req\x12\x0c\n\x04nSet\x18\x01 \x01(\r\"^\n\x10RFormularSet_Ans\x12\x0c\n\x04nSet\x18\x01 \x01(\r\x12<\n\x0egroupformulars\x18\x02 \x03(\x0b\x32$.wuhan.jcl.report.RFormularGroupList\"\xb6\x01\n\x11ReCalcFomular_Req\x12\x0c\n\x04nSet\x18\x01 \x01(\r\x12\r\n\x05szKey\x18\x02 \x01(\t\x12/\n\x04\x63ode\x18\x03 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\x0e\n\x06period\x18\x04 \x01(\r\x12\x0e\n\x06tqflag\x18\x05 \x01(\r\x12\x33\n\x06params\x18\x06 \x03(\x0b\x32#.wuhan.jcl.report.ReCalcParamInfoEx\"\xf4\x01\n\x10ReCalcOutVarInfo\x12\r\n\x05nType\x18\x01 \x01(\r\x12\x0e\n\x06szWord\x18\x02 \x01(\t\x12\x11\n\tszSpecial\x18\x03 \x01(\t\x12\x0e\n\x06nColor\x18\x04 \x01(\r\x12\x11\n\tnDrawMode\x18\x05 \x01(\r\x12\x12\n\nnLineThick\x18\x06 \x01(\r\x12\x12\n\nnPrecision\x18\x07 \x01(\r\x12\x0e\n\x06lineno\x18\x08 \x01(\r\x12\x11\n\texterndat\x18\t \x03(\x01\x12\x0c\n\x04\x64Val\x18\n \x01(\x01\x12\x0f\n\x07lpArray\x18\x0b \x03(\x01\x12\r\n\x05szVal\x18\x0c \x01(\t\x12\x12\n\nszValArray\x18\r \x03(\t\"\xdc\x01\n\x11ReCalcFomular_Ans\x12\x0c\n\x04nSet\x18\x01 \x01(\r\x12\r\n\x05szKey\x18\x02 \x01(\t\x12/\n\x04\x63ode\x18\x03 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\x0e\n\x06period\x18\x04 \x01(\r\x12\x33\n\x06params\x18\x05 \x03(\x0b\x32#.wuhan.jcl.report.ReCalcParamInfoEx\x12\x34\n\x08outinfos\x18\x06 \x03(\x0b\x32\".wuhan.jcl.report.ReCalcOutVarInfo\"\xe2\x01\n\x0bMrfp_hqinfo\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\x0e\n\x06yclose\x18\x02 \x01(\x01\x12\x10\n\x08nowprice\x18\x03 \x01(\x01\x12\r\n\x05upnum\x18\x04 \x01(\r\x12\x0f\n\x07\x64ownnum\x18\x05 \x01(\r\x12\x0e\n\x06\x65qunum\x18\x06 \x01(\r\x12\x0b\n\x03\x61mt\x18\x07 \x01(\x01\x12\x0b\n\x03vol\x18\x08 \x01(\x01\x12\x11\n\topenprice\x18\t \x01(\x01\x12\x11\n\thighPrice\x18\n \x01(\x01\x12\x10\n\x08lowprice\x18\x0b \x01(\x01\">\n\x0fMrfp_hqinfo_ans\x12+\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32\x1d.wuhan.jcl.report.Mrfp_hqinfo\"}\n\rMrfp_agtj_ans\x12\r\n\x05upnum\x18\x01 \x01(\r\x12\x0f\n\x07\x64ownnum\x18\x02 \x01(\r\x12\x0e\n\x06\x65qunum\x18\x03 \x01(\r\x12\r\n\x05ztnum\x18\x04 \x01(\r\x12\r\n\x05\x64tnum\x18\x05 \x01(\r\x12\x0e\n\x06hztnum\x18\x06 \x01(\r\x12\x0e\n\x06ldtnum\x18\x07 \x01(\r\"t\n\rQsjg_sort_req\x12/\n\x04\x63ode\x18\x01 \x03(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\x0f\n\x07startxh\x18\x02 \x01(\r\x12\x0f\n\x07wantnum\x18\x03 \x01(\r\x12\x10\n\x08sorttype\x18\x04 \x01(\r\"(\n\rQsjg_strategy\x12\x0c\n\x04type\x18\x01 \x01(\r\x12\t\n\x01s\x18\x02 \x01(\t\"B\n\x11Qsjg_strategy_ans\x12-\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32\x1f.wuhan.jcl.report.Qsjg_strategy\"q\n\t_ployline\x12\x33\n\tcondition\x18\x01 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12/\n\x05price\x18\x02 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\"q\n\t_polyline\x12\x33\n\tcondition\x18\x01 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12/\n\x05price\x18\x02 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\"\x8d\x02\n\t_drawline\x12\x34\n\ncondition1\x18\x01 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\x30\n\x06price1\x18\x02 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\x34\n\ncondition2\x18\x03 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\x30\n\x06price2\x18\x04 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\x30\n\x06\x65xpand\x18\x05 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\"\xcc\x01\n\n_drawkline\x12.\n\x04high\x18\x01 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12.\n\x04open\x18\x02 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12-\n\x03low\x18\x03 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12/\n\x05\x63lose\x18\x04 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\"\xda\x01\n\x0b_drawnumber\x12\x33\n\tcondition\x18\x01 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12/\n\x05price\x18\x02 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\x30\n\x06number\x18\x03 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\x33\n\tprecision\x18\x04 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\"\x9c\x01\n\x0f_drawnumber_fix\x12\x33\n\tcondition\x18\x01 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\t\n\x01x\x18\x02 \x01(\x01\x12\t\n\x01y\x18\x03 \x01(\x01\x12\x0c\n\x04type\x18\x04 \x01(\x01\x12\x30\n\x06number\x18\x05 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\"t\n\x0b_drawtextex\x12\x33\n\tcondition\x18\x01 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\x0c\n\x04type\x18\x02 \x01(\x01\x12\t\n\x01x\x18\x03 \x01(\x01\x12\t\n\x01y\x18\x04 \x01(\x01\x12\x0c\n\x04text\x18\x05 \x01(\t\"\x7f\n\t_drawtext\x12\x33\n\tcondition\x18\x01 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12/\n\x05price\x18\x02 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\x0c\n\x04text\x18\x03 \x01(\t\"v\n\r_drawtext_fix\x12\x33\n\tcondition\x18\x01 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\t\n\x01x\x18\x02 \x01(\x01\x12\t\n\x01y\x18\x03 \x01(\x01\x12\x0c\n\x04type\x18\x04 \x01(\x01\x12\x0c\n\x04text\x18\x05 \x01(\t\"\x81\x01\n\x08_drawbmp\x12\x33\n\tcondition\x18\x01 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12/\n\x05price\x18\x02 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\x0f\n\x07\x62mpfile\x18\x03 \x01(\t\"\x8b\x01\n\t_drawband\x12.\n\x04var1\x18\x01 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\x0e\n\x06\x63olor1\x18\x02 \x01(\x01\x12.\n\x04var2\x18\x03 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\x0e\n\x06\x63olor2\x18\x04 \x01(\x01\"\xcf\x01\n\t_drawicon\x12\x33\n\tcondition\x18\x01 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12/\n\x05price\x18\x02 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12.\n\x04type\x18\x03 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12,\n\x02up\x18\x04 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\"2\n\x0c_drawtextabs\x12\t\n\x01x\x18\x01 \x01(\x01\x12\t\n\x01y\x18\x02 \x01(\x01\x12\x0c\n\x04text\x18\x03 \x01(\t\"2\n\x0c_drawtextrel\x12\t\n\x01x\x18\x01 \x01(\x01\x12\t\n\x01y\x18\x02 \x01(\x01\x12\x0c\n\x04text\x18\x03 \x01(\t\"\xd4\x01\n\x08_fillrgn\x12\x30\n\x06price1\x18\x01 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\x30\n\x06price2\x18\x02 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\x33\n\tcondition\x18\x03 \x03(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12/\n\x05\x63olor\x18\x04 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\"\xd5\x01\n\t_floatrgn\x12\x30\n\x06price1\x18\x01 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\x30\n\x06price2\x18\x02 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\x33\n\tcondition\x18\x03 \x03(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12/\n\x05\x63olor\x18\x04 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\"\xc7\x01\n\t_fillback\x12\x34\n\ncondition1\x18\x01 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\x34\n\ncondition2\x18\x02 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12.\n\x04type\x18\x03 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\x0e\n\x06\x63olor1\x18\x04 \x01(\t\x12\x0e\n\x06\x63olor2\x18\x05 \x01(\t\"\xa2\x01\n\x0c_fillallback\x12.\n\x04type\x18\x01 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\x30\n\x06\x63olor1\x18\x02 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\x30\n\x06\x63olor2\x18\x03 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\"\xa2\x01\n\t_partline\x12/\n\x05price\x18\x01 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\x33\n\tcondition\x18\x02 \x03(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12/\n\x05\x63olor\x18\x03 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\"\x9f\x02\n\x08_drawgbk\x12\x33\n\tcondition\x18\x01 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\x30\n\x06\x63olor1\x18\x02 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\x30\n\x06\x63olor2\x18\x03 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\x35\n\x0b\x62horizontal\x18\x04 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\x0f\n\x07\x62mpfile\x18\x05 \x01(\t\x12\x32\n\x08\x62stretch\x18\x06 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\"\x81\x02\n\x07_drawsl\x12\x33\n\tcondition\x18\x01 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12/\n\x05price\x18\x02 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12/\n\x05slope\x18\x03 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12-\n\x03len\x18\x04 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\x30\n\x06\x64irect\x18\x05 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\"\x87\x02\n\n_stickline\x12\x33\n\tcondition\x18\x01 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\x30\n\x06price1\x18\x02 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\x30\n\x06price2\x18\x03 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12/\n\x05width\x18\x04 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12/\n\x05\x65mpty\x18\x05 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\"\x89\x02\n\x0c_stickline3d\x12\x33\n\tcondition\x18\x01 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\x30\n\x06price1\x18\x02 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\x30\n\x06price2\x18\x03 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12/\n\x05width\x18\x04 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12/\n\x05\x65mpty\x18\x05 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\"\x9a\x02\n\x0e_stickline3dex\x12\x33\n\tcondition\x18\x01 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\x30\n\x06price1\x18\x02 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\x30\n\x06price2\x18\x03 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12/\n\x05width\x18\x04 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12/\n\x05\x65mpty\x18\x05 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\r\n\x05\x63olor\x18\x06 \x01(\t\"\xd1\x01\n\x07_drawox\x12\x33\n\tcondition\x18\x01 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12/\n\x05price\x18\x02 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12/\n\x05width\x18\x03 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12/\n\x05\x65mpty\x18\x04 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\"1\n\x06_strip\x12\x0c\n\x04rgb1\x18\x01 \x01(\x01\x12\x0c\n\x04rgb2\x18\x02 \x01(\x01\x12\x0b\n\x03\x64ir\x18\x03 \x01(\x01\"W\n\x0c_drawrectrel\x12\x0c\n\x04left\x18\x01 \x01(\x01\x12\x0b\n\x03top\x18\x02 \x01(\x01\x12\r\n\x05right\x18\x03 \x01(\x01\x12\x0e\n\x06\x62ottom\x18\x04 \x01(\x01\x12\r\n\x05\x63olor\x18\x05 \x01(\x01\"M\n\x08_explain\x12\x33\n\tcondition\x18\x01 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\x0c\n\x04text\x18\x02 \x01(\t\"\xe8\x01\n\n_explainex\x12\x33\n\tcondition\x18\x01 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\x0c\n\x04text\x18\x02 \x01(\t\x12-\n\x03num\x18\x03 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\x33\n\tprecision\x18\x04 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12\x33\n\tskiplines\x18\x05 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\"s\n\x0c_explainicon\x12\x33\n\tcondition\x18\x01 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\x12.\n\x04type\x18\x02 \x01(\x0b\x32 .wuhan.jcl.report.tagDoubleArray\"\x8c\x0c\n\n_drawfuncs\x12/\n\x08ployline\x18\x01 \x01(\x0b\x32\x1b.wuhan.jcl.report._ploylineH\x00\x12/\n\x08polyline\x18\x02 \x01(\x0b\x32\x1b.wuhan.jcl.report._polylineH\x00\x12/\n\x08\x64rawline\x18\x03 \x01(\x0b\x32\x1b.wuhan.jcl.report._drawlineH\x00\x12\x31\n\tdrawkline\x18\x04 \x01(\x0b\x32\x1c.wuhan.jcl.report._drawklineH\x00\x12\x33\n\ndrawnumber\x18\x05 \x01(\x0b\x32\x1d.wuhan.jcl.report._drawnumberH\x00\x12;\n\x0e\x64rawnumber_fix\x18\x06 \x01(\x0b\x32!.wuhan.jcl.report._drawnumber_fixH\x00\x12\x33\n\ndrawtextex\x18\x07 \x01(\x0b\x32\x1d.wuhan.jcl.report._drawtextexH\x00\x12/\n\x08\x64rawtext\x18\x08 \x01(\x0b\x32\x1b.wuhan.jcl.report._drawtextH\x00\x12\x37\n\x0c\x64rawtext_fix\x18\t \x01(\x0b\x32\x1f.wuhan.jcl.report._drawtext_fixH\x00\x12-\n\x07\x64rawbmp\x18\n \x01(\x0b\x32\x1a.wuhan.jcl.report._drawbmpH\x00\x12/\n\x08\x64rawband\x18\x0b \x01(\x0b\x32\x1b.wuhan.jcl.report._drawbandH\x00\x12/\n\x08\x64rawicon\x18\x0c \x01(\x0b\x32\x1b.wuhan.jcl.report._drawiconH\x00\x12\x35\n\x0b\x64rawtextabs\x18\r \x01(\x0b\x32\x1e.wuhan.jcl.report._drawtextabsH\x00\x12\x35\n\x0b\x64rawtextrel\x18\x0e \x01(\x0b\x32\x1e.wuhan.jcl.report._drawtextrelH\x00\x12-\n\x07\x66illrgn\x18\x0f \x01(\x0b\x32\x1a.wuhan.jcl.report._fillrgnH\x00\x12/\n\x08\x66loatrgn\x18\x10 \x01(\x0b\x32\x1b.wuhan.jcl.report._floatrgnH\x00\x12/\n\x08\x66illback\x18\x11 \x01(\x0b\x32\x1b.wuhan.jcl.report._fillbackH\x00\x12\x35\n\x0b\x66illallback\x18\x12 \x01(\x0b\x32\x1e.wuhan.jcl.report._fillallbackH\x00\x12/\n\x08partline\x18\x13 \x01(\x0b\x32\x1b.wuhan.jcl.report._partlineH\x00\x12-\n\x07\x64rawgbk\x18\x14 \x01(\x0b\x32\x1a.wuhan.jcl.report._drawgbkH\x00\x12+\n\x06\x64rawsl\x18\x15 \x01(\x0b\x32\x19.wuhan.jcl.report._drawslH\x00\x12\x31\n\tstickline\x18\x16 \x01(\x0b\x32\x1c.wuhan.jcl.report._sticklineH\x00\x12\x35\n\x0bstickline3d\x18\x17 \x01(\x0b\x32\x1e.wuhan.jcl.report._stickline3dH\x00\x12\x39\n\rstickline3dex\x18\x18 \x01(\x0b\x32 .wuhan.jcl.report._stickline3dexH\x00\x12+\n\x06\x64rawox\x18\x19 \x01(\x0b\x32\x19.wuhan.jcl.report._drawoxH\x00\x12)\n\x05strip\x18\x1a \x01(\x0b\x32\x18.wuhan.jcl.report._stripH\x00\x12\x35\n\x0b\x64rawrectrel\x18\x1b \x01(\x0b\x32\x1e.wuhan.jcl.report._drawrectrelH\x00\x12-\n\x07\x65xplain\x18\x1c \x01(\x0b\x32\x1a.wuhan.jcl.report._explainH\x00\x12\x31\n\texplainex\x18\x1d \x01(\x0b\x32\x1c.wuhan.jcl.report._explainexH\x00\x12\x35\n\x0b\x65xplainicon\x18\x1e \x01(\x0b\x32\x1e.wuhan.jcl.report._explainiconH\x00\x42\x06\n\x04\x64raw\"\xfd\x02\n\rgs_outvarinfo\x12\r\n\x05nType\x18\x01 \x01(\r\x12\x0e\n\x06szWord\x18\x02 \x01(\t\x12\x13\n\x0bnSpecialOut\x18\x03 \x01(\r\x12\x11\n\tszSpecial\x18\x04 \x01(\t\x12\x0e\n\x06nPType\x18\x05 \x01(\t\x12\x11\n\tnProperty\x18\x06 \x03(\r\x12\x0e\n\x06nColor\x18\x07 \x01(\r\x12\x11\n\tnDrawMode\x18\x08 \x01(\r\x12\x12\n\nnLineThick\x18\t \x01(\r\x12\x12\n\nnPrecision\x18\n \x01(\r\x12\x13\n\x0bisFloatUnit\x18\x0b \x01(\r\x12\x0e\n\x06szUnit\x18\x0c \x01(\t\x12\x12\n\nnPlaySound\x18\r \x01(\r\x12\x0f\n\x07\x61Return\x18\x0e \x01(\t\x12\x0c\n\x04\x64Val\x18\x0f \x01(\x01\x12\x0f\n\x07lpArray\x18\x10 \x03(\x01\x12\r\n\x05szVal\x18\x11 \x01(\t\x12\x0e\n\x06lineno\x18\x12 \x01(\x05\x12/\n\tdrawfuncs\x18\x13 \x01(\x0b\x32\x1c.wuhan.jcl.report._drawfuncs\"\'\n\x08gs_param\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x01\"\xa6\x01\n\ngscalc_req\x12\x0f\n\x07setcode\x18\x01 \x01(\x05\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x0e\n\x06period\x18\x03 \x01(\x05\x12\r\n\x05gskey\x18\x04 \x01(\t\x12)\n\x05param\x18\x05 \x03(\x0b\x32\x1a.wuhan.jcl.report.gs_param\x12\x0f\n\x07wantnum\x18\x06 \x01(\x05\x12\x0e\n\x06tqtype\x18\x07 \x01(\x05\x12\x0e\n\x06offset\x18\x08 \x01(\x05\"=\n\ngscalc_ans\x12/\n\x06outvar\x18\x01 \x03(\x0b\x32\x1f.wuhan.jcl.report.gs_outvarinfo\"\xe4\x02\n\x0b\x63\x61lczafdata\x12\x0f\n\x07setcode\x18\x01 \x01(\x05\x12\x0c\n\x04nkey\x18\x02 \x01(\x04\x12\x10\n\x08zaf_5min\x18\x03 \x01(\x01\x12\x11\n\tzaf_3days\x18\x04 \x01(\x01\x12\x11\n\tzaf_5days\x18\x05 \x01(\x01\x12\x12\n\nzaf_20days\x18\x06 \x01(\x01\x12\x11\n\tzang_days\x18\x07 \x01(\x01\x12\x10\n\x08\x64ie_days\x18\x08 \x01(\x01\x12\x13\n\x0bhigh_20days\x18\t \x01(\x01\x12\x12\n\nlow_20days\x18\n \x01(\x01\x12\x10\n\x08high_his\x18\x0b \x01(\x01\x12\x0f\n\x07low_his\x18\x0c \x01(\x01\x12\x12\n\nzaf_90days\x18\r \x01(\x01\x12\x13\n\x0bzaf_180days\x18\x0e \x01(\x01\x12\x13\n\x0bzaf_365days\x18\x0f \x01(\x01\x12\x12\n\nzaf_10days\x18\x10 \x01(\x01\x12\x12\n\nzaf_60days\x18\x11 \x01(\x01\x12\x13\n\x0bzaf_curyear\x18\x12 \x01(\x01\"\"\n\x0f\x63\x61lczafdata_req\x12\x0f\n\x07setcode\x18\x01 \x01(\x05\">\n\x0f\x63\x61lczafdata_ans\x12+\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32\x1d.wuhan.jcl.report.calczafdata\"$\n\x10\x63\x61lczdfaread_ans\x12\x10\n\x08stocknum\x18\x01 \x03(\x05\" \n\x11\x63\x61lczdfresult_req\x12\x0b\n\x03idx\x18\x01 \x01(\x05\"D\n\x11\x63\x61lczdfresult_ans\x12/\n\x04\x63ode\x18\x01 \x03(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\",\n\tzdfprofit\x12\x0f\n\x07jcltime\x18\x01 \x01(\x03\x12\x0e\n\x06profit\x18\x02 \x01(\x01\"=\n\x11\x63\x61lczdfprofit_ans\x12(\n\x03zdf\x18\x01 \x03(\x0b\x32\x1b.wuhan.jcl.report.zdfprofit\";\n\x06zdfzdt\x12\x0f\n\x07jcltime\x18\x01 \x01(\x03\x12\x0e\n\x06num_up\x18\x02 \x01(\x05\x12\x10\n\x08num_down\x18\x03 \x01(\x05\":\n\x11\x63\x61lczdfzdtnum_ans\x12%\n\x03zdt\x18\x01 \x03(\x0b\x32\x18.wuhan.jcl.report.zdfzdt\"\x1f\n\x0f\x63\x61lczdtdata_req\x12\x0c\n\x04type\x18\x01 \x01(\x05\"B\n\x0f\x63\x61lczdtdata_ans\x12/\n\x04\x63ode\x18\x01 \x03(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\"I\n\x16\x63\x61lcyesonelinecode_ans\x12/\n\x04\x63ode\x18\x01 \x03(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\"M\n\x0bHisChip_Req\x12\x0c\n\x04nkey\x18\x01 \x01(\x03\x12\r\n\x05sDate\x18\x02 \x01(\x04\x12\r\n\x05\x65\x44\x61te\x18\x03 \x01(\x04\x12\x12\n\nchipPeriod\x18\x04 \x01(\r\"\xe5\x01\n\x0bHisChipData\x12\x0c\n\x04\x64\x61te\x18\x01 \x01(\x04\x12\x0b\n\x03\x61vg\x18\x02 \x01(\x01\x12\x0e\n\x06winner\x18\x03 \x01(\x01\x12\r\n\x05\x63r_70\x18\x04 \x01(\x01\x12\x13\n\x0b\x63ost_70_min\x18\x05 \x01(\x01\x12\x13\n\x0b\x63ost_70_max\x18\x06 \x01(\x01\x12\r\n\x05\x63r_90\x18\x07 \x01(\x01\x12\x13\n\x0b\x63ost_90_min\x18\x08 \x01(\x01\x12\x13\n\x0b\x63ost_90_max\x18\t \x01(\x01\x12\x0b\n\x03low\x18\n \x01(\x01\x12\x0c\n\x04high\x18\x0b \x01(\x01\x12\x0c\n\x04step\x18\x0c \x01(\x01\x12\x10\n\x08\x63hipData\x18\r \x03(\r\";\n\x0bHisChip_Ans\x12,\n\x05\x66ield\x18\x01 \x03(\x0b\x32\x1d.wuhan.jcl.report.HisChipData\"=\n\x0emoneyrank_data\x12\x0c\n\x04rank\x18\x01 \x01(\r\x12\x0f\n\x07posrate\x18\x02 \x01(\x01\x12\x0c\n\x04\x64\x61ys\x18\x03 \x01(\r\"?\n\rmoneyrank_ans\x12.\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32 .wuhan.jcl.report.moneyrank_data\"L\n\nlbdata_ans\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\r\n\x05lbnum\x18\x02 \x01(\x05\"<\n\x0elbdatadata_ans\x12*\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32\x1c.wuhan.jcl.report.lbdata_ans\"Q\n\x0c\x46inanceValue\x12\x10\n\x08\x46uncName\x18\x01 \x01(\t\x12\x0e\n\x06Value1\x18\x02 \x01(\x01\x12\x0e\n\x06Value2\x18\x03 \x01(\x01\x12\x0f\n\x07String3\x18\x04 \x01(\t\"\xce\x02\n\x10\x46inanceDateValue\x12\x0c\n\x04\x44\x61te\x18\x01 \x01(\x03\x12/\n\x07\x66inance\x18\x02 \x03(\x0b\x32\x1e.wuhan.jcl.report.FinanceValue\x12\x30\n\x08\x66invalue\x18\x03 \x03(\x0b\x32\x1e.wuhan.jcl.report.FinanceValue\x12\x31\n\tgpjyvalue\x18\x04 \x03(\x0b\x32\x1e.wuhan.jcl.report.FinanceValue\x12\x31\n\tbkjyvalue\x18\x05 \x03(\x0b\x32\x1e.wuhan.jcl.report.FinanceValue\x12\x31\n\tscjyvalue\x18\x06 \x03(\x0b\x32\x1e.wuhan.jcl.report.FinanceValue\x12\x30\n\x08gponedat\x18\x07 \x03(\x0b\x32\x1e.wuhan.jcl.report.FinanceValue\"J\n\x07\x46inance\x12\x0c\n\x04nkey\x18\x01 \x01(\x03\x12\x31\n\x05value\x18\x02 \x03(\x0b\x32\".wuhan.jcl.report.FinanceDateValue\"<\n\x18StrategySystempoolResult\x12\x0f\n\x07groupid\x18\x01 \x01(\t\x12\x0f\n\x07jsonstr\x18\x02 \x01(\t\"(\n\x15StrategySystempoolReq\x12\x0f\n\x07groupid\x18\x01 \x01(\t\"R\n\x16StrategySystempoolList\x12\x38\n\x04list\x18\x01 \x03(\x0b\x32*.wuhan.jcl.report.StrategySystempoolResult\"A\n\x0e\x46inanceNow_req\x12/\n\x04\x63ode\x18\x01 \x03(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\"9\n\x0e\x46inanceNow_ans\x12\'\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32\x19.wuhan.jcl.report.Finance\"S\n\x0eNewFinance_req\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\x10\n\x08\x46uncName\x18\x02 \x03(\t\"9\n\x0eNewFinance_ans\x12\'\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32\x19.wuhan.jcl.report.Finance\"O\n\x11StatictisTradeNum\x12\x0f\n\x07jcltime\x18\x01 \x01(\x04\x12\x13\n\x0b\x62uyTradeNum\x18\x02 \x01(\r\x12\x14\n\x0csellTradeNum\x18\x03 \x01(\r\"J\n\x15StatictisTradeNum_Ans\x12\x31\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32#.wuhan.jcl.report.StatictisTradeNum\"\x85\x01\n\x15StatictisTradeNum_Req\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\x0e\n\x06period\x18\x02 \x01(\r\x12\x0e\n\x06offset\x18\x03 \x01(\r\x12\x0b\n\x03num\x18\x04 \x01(\r\x12\x0e\n\x06mulnum\x18\x05 \x01(\r\"]\n\x0eminute_new_req\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\x0c\n\x04\x64\x61ys\x18\x02 \x01(\r\x12\x0c\n\x04\x65xhq\x18\x03 \x01(\r\"n\n\x0eminute_new_ans\x12,\n\x06minute\x18\x01 \x03(\x0b\x32\x1c.wuhan.jcl.report.MinuteInfo\x12.\n\x02hq\x18\x02 \x01(\x0b\x32\".wuhan.jcl.report.CurrStockDataBEx\"\x9b\x01\n\rkline_new_req\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\x0e\n\x06period\x18\x02 \x01(\r\x12\x0e\n\x06mulnum\x18\x03 \x01(\r\x12\x0e\n\x06offset\x18\x04 \x01(\r\x12\x0b\n\x03num\x18\x05 \x01(\r\x12\x0e\n\x06tqflag\x18\x06 \x01(\r\x12\x0c\n\x04\x65xhq\x18\x07 \x01(\r\"i\n\rkline_new_ans\x12(\n\x02\x61K\x18\x01 \x03(\x0b\x32\x1c.wuhan.jcl.report.AnalyDataB\x12.\n\x02hq\x18\x02 \x01(\x0b\x32\".wuhan.jcl.report.CurrStockDataBEx\"*\n\x19multi_today_moneyflow_req\x12\r\n\x05nkeys\x18\x01 \x03(\x04\"\xc1\x01\n\x13nkey_moneyflow_data\x12\x0c\n\x04nkey\x18\x01 \x01(\x04\x12\x0f\n\x07jcltime\x18\x02 \x01(\x03\x12-\n\x03\x61mt\x18\x03 \x01(\x0b\x32 .wuhan.jcl.report.moneyflow_data\x12-\n\x03vol\x18\x04 \x01(\x0b\x32 .wuhan.jcl.report.moneyflow_data\x12-\n\x03\x63nt\x18\x05 \x01(\x0b\x32 .wuhan.jcl.report.moneyflow_data\"P\n\x19multi_today_moneyflow_ans\x12\x33\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32%.wuhan.jcl.report.nkey_moneyflow_data\"W\n\x07\x63\x66gdata\x12\x0c\n\x04nkey\x18\x01 \x01(\x04\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x30\n\x07\x63\x66gnkey\x18\x03 \x01(\x0b\x32\x1f.wuhan.jcl.report.tagInt64Array\"A\n\x08\x62igblock\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\'\n\x04\x64\x61ta\x18\x02 \x03(\x0b\x32\x19.wuhan.jcl.report.cfgdata\"N\n\x0c\x62lockxmldata\x12\x11\n\tupdatetag\x18\x01 \x01(\r\x12+\n\x07\x61lldata\x18\x02 \x03(\x0b\x32\x1a.wuhan.jcl.report.bigblock\"K\n\x0czscfgxmldata\x12\x11\n\tupdatetag\x18\x01 \x01(\r\x12(\n\x04\x64\x61ta\x18\x02 \x01(\x0b\x32\x1a.wuhan.jcl.report.bigblock\"[\n\x18StrategyOnekey_commentEx\x12\x0f\n\x07groupid\x18\x01 \x01(\t\x12\x0f\n\x07\x63omment\x18\x02 \x01(\t\x12\x0c\n\x04tags\x18\x03 \x01(\t\x12\x0f\n\x07version\x18\x04 \x01(\t\"\x84\x01\n\rCalc_zdqj_req\x12\x0e\n\x06minzaf\x18\x01 \x01(\x01\x12\x0e\n\x06maxzaf\x18\x02 \x01(\x01\x12\x0f\n\x07\x63mptype\x18\x03 \x01(\r\x12\x0f\n\x07\x63oltype\x18\x04 \x01(\r\x12\x10\n\x08sorttype\x18\x05 \x01(\r\x12\x0e\n\x06offset\x18\x06 \x01(\r\x12\x0f\n\x07wantnum\x18\x07 \x01(\r\"O\n\rCalc_zdqj_ans\x12\r\n\x05nkeys\x18\x01 \x03(\x04\x12/\n\x03hqs\x18\x02 \x03(\x0b\x32\".wuhan.jcl.report.CurrStockDataBEx\"C\n\x12statics_exdata_req\x12\x0c\n\x04nkey\x18\x01 \x01(\x04\x12\x0e\n\x06offset\x18\x02 \x01(\r\x12\x0f\n\x07wantnum\x18\x03 \x01(\r\"I\n\x13statics_exdata_reqs\x12\x32\n\x04reqs\x18\x01 \x03(\x0b\x32$.wuhan.jcl.report.statics_exdata_req\"\xe5\x01\n\x0estatics_exdata\x12\x13\n\x0bupdatetimer\x18\x01 \x01(\x04\x12\x0f\n\x07ztmoney\x18\x02 \x01(\x01\x12\x0f\n\x07kbtimes\x18\x03 \x01(\r\x12\x10\n\x08zdtstate\x18\x04 \x01(\r\x12\x0f\n\x07\x66\x64money\x18\x05 \x01(\x01\x12\x0e\n\x06\x66\x63rate\x18\x06 \x01(\x01\x12\x0e\n\x06\x66lrate\x18\x07 \x01(\x01\x12\x0f\n\x07zttimer\x18\x08 \x01(\x04\x12\x12\n\nmaxztmoney\x18\t \x01(\x01\x12\x12\n\nmaxdtmoney\x18\n \x01(\x01\x12\x0f\n\x07kpmoney\x18\x0b \x01(\x01\x12\x0f\n\x07phmoney\x18\x0c \x01(\x01\"R\n\x12statics_exdata_ans\x12\x0c\n\x04nkey\x18\x01 \x01(\x04\x12.\n\x04\x64\x61ta\x18\x02 \x03(\x0b\x32 .wuhan.jcl.report.statics_exdata\"H\n\x13statics_exdata_anss\x12\x31\n\x03\x61ns\x18\x01 \x03(\x0b\x32$.wuhan.jcl.report.statics_exdata_ans\"\x1f\n\x0fmany_minute_req\x12\x0c\n\x04nkey\x18\x01 \x03(\x04\"I\n\x0bminute_data\x12\x0c\n\x04nkey\x18\x01 \x01(\x04\x12,\n\x06minute\x18\x02 \x03(\x0b\x32\x1c.wuhan.jcl.report.MinuteInfo\">\n\x0fmany_minute_ans\x12+\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32\x1d.wuhan.jcl.report.minute_data\"/\n\x0e\x62\x66\x64\x61y_data_req\x12\x0c\n\x04nkey\x18\x01 \x01(\x04\x12\x0f\n\x07jcltime\x18\x02 \x01(\x04\"I\n\x0e\x62\x66\x64\x61y_data_ans\x12\x0c\n\x04nkey\x18\x01 \x01(\x04\x12\x0f\n\x07jcltime\x18\x02 \x01(\x04\x12\x0b\n\x03\x61mt\x18\x03 \x01(\x01\x12\x0b\n\x03vol\x18\x04 \x01(\x01\"&\n\x08rankinfo\x12\x0c\n\x04nkey\x18\x01 \x01(\x04\x12\x0c\n\x04rank\x18\x02 \x01(\x05\"Z\n\x0brankinfoarr\x12\x0f\n\x07\x63oltype\x18\x01 \x01(\r\x12\x10\n\x08sorttype\x18\x02 \x01(\r\x12(\n\x04rank\x18\x03 \x03(\x0b\x32\x1a.wuhan.jcl.report.rankinfo\",\n\x07rankreq\x12\x0f\n\x07\x63oltype\x18\x01 \x01(\r\x12\x10\n\x08sorttype\x18\x02 \x01(\r\"\xbf\x01\n\x0fsort_rankex_req\x12\x11\n\tsetDomain\x18\x01 \x01(\r\x12\x0f\n\x07\x63oltype\x18\x02 \x01(\r\x12\x0f\n\x07startxh\x18\x03 \x01(\r\x12\x0f\n\x07wantnum\x18\x04 \x01(\r\x12\x10\n\x08sorttype\x18\x05 \x01(\r\x12\x10\n\x08\x66ieldids\x18\x06 \x03(\r\x12+\n\x08longrank\x18\x07 \x03(\x0b\x32\x19.wuhan.jcl.report.rankreq\x12\x15\n\rneedhyzafrank\x18\x08 \x01(\r\"\xbb\x01\n\x0fsort_rankex_ans\x12\x10\n\x08\x66ieldids\x18\x01 \x03(\r\x12\x30\n\x06\x66ields\x18\x02 \x03(\x0b\x32 .wuhan.jcl.report.hq_with_fields\x12\x30\n\trankfield\x18\x03 \x03(\x0b\x32\x1d.wuhan.jcl.report.rankinfoarr\x12\x32\n\x0ehyzafrankfiled\x18\x04 \x03(\x0b\x32\x1a.wuhan.jcl.report.rankinfo\"Q\n\x0eKlineLabel_req\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\x0e\n\x06offset\x18\x02 \x01(\r\"U\n\x12KlineLabel_Arr_req\x12\x0f\n\x07setcode\x18\x01 \x01(\r\x12.\n\x04\x64\x61ta\x18\x02 \x03(\x0b\x32 .wuhan.jcl.report.KlineLabel_req\"=\n\x0fKlineLabel_data\x12\x0c\n\x04\x64\x61te\x18\x01 \x01(\r\x12\x0c\n\x04\x66lag\x18\x02 \x01(\r\x12\x0e\n\x06remark\x18\x03 \x01(\t\"A\n\x0eKlineLabel_ans\x12/\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32!.wuhan.jcl.report.KlineLabel_data\"V\n\x17KlineLabelWithNkey_data\x12\x0c\n\x04nkey\x18\x01 \x01(\x04\x12-\n\x03\x61ns\x18\x02 \x01(\x0b\x32 .wuhan.jcl.report.KlineLabel_ans\"P\n\x12KlineLabel_Arr_ans\x12:\n\x07\x61rrdata\x18\x01 \x03(\x0b\x32).wuhan.jcl.report.KlineLabelWithNkey_data\"\x86\x01\n\x0clv2OrderTick\x12\x10\n\x08sequence\x18\x01 \x01(\x04\x12\x14\n\x0c\x62uy_order_no\x18\x02 \x01(\x04\x12\x15\n\rsell_order_no\x18\x03 \x01(\x04\x12\r\n\x05price\x18\x04 \x01(\x03\x12\x0b\n\x03qty\x18\x05 \x01(\x03\x12\x0c\n\x04type\x18\x06 \x01(\r\x12\r\n\x05timer\x18\x07 \x01(\r\"\x8c\x01\n\x10lv2OrderTick_req\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\x0e\n\x06offset\x18\x02 \x01(\r\x12\x0b\n\x03num\x18\x03 \x01(\r\x12\n\n\x02\x62\x61\x18\x04 \x01(\r\x12\x0c\n\x04type\x18\x05 \x01(\r\x12\x10\n\x08sequence\x18\x06 \x01(\x04\"N\n\x10lv2OrderTick_ans\x12\x0c\n\x04nkey\x18\x01 \x01(\x04\x12,\n\x04\x64\x61ta\x18\x02 \x03(\x0b\x32\x1e.wuhan.jcl.report.lv2OrderTick\"s\n\x13lv2SigOrderTick_req\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\x14\n\x0c\x62uy_order_no\x18\x02 \x01(\x04\x12\x15\n\rsell_order_no\x18\x03 \x01(\x04\"?\n\x0clv2Mulpk_req\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\"R\n\rlv2Mulpk_data\x12\r\n\x05price\x18\x01 \x01(\x03\x12\x0b\n\x03qty\x18\x02 \x01(\x03\x12\x11\n\tnumtrades\x18\x03 \x01(\x05\x12\x12\n\nnummore500\x18\x04 \x01(\x05\"\x83\x01\n\x0clv2Mulpk_ans\x12\x0c\n\x04nkey\x18\x01 \x01(\x04\x12\x31\n\x08\x64\x61ta_buy\x18\x02 \x03(\x0b\x32\x1f.wuhan.jcl.report.lv2Mulpk_data\x12\x32\n\tdata_sell\x18\x03 \x03(\x0b\x32\x1f.wuhan.jcl.report.lv2Mulpk_data\"Z\n\x0clv2Sigpk_req\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\n\n\x02\x62s\x18\x02 \x01(\r\x12\r\n\x05price\x18\x03 \x01(\x03\"D\n\x0clv2Sigpk_ans\x12\x0c\n\x04nkey\x18\x01 \x01(\x04\x12\n\n\x02\x62s\x18\x02 \x01(\r\x12\r\n\x05price\x18\x03 \x01(\x03\x12\x0b\n\x03qty\x18\x04 \x03(\x03\"?\n\x0clv2Kcbph_req\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\"@\n\x0fmulstk_aday_req\x12\x0c\n\x04nkey\x18\x01 \x03(\x04\x12\x0f\n\x07jcltime\x18\x02 \x01(\x04\x12\x0e\n\x06period\x18\x03 \x01(\x05\"K\n\x10mulstk_aday_data\x12\x0c\n\x04nkey\x18\x01 \x01(\x04\x12)\n\x03\x61na\x18\x02 \x01(\x0b\x32\x1c.wuhan.jcl.report.AnalyDataB\"C\n\x0fmulstk_aday_ans\x12\x30\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32\".wuhan.jcl.report.mulstk_aday_data\"\xfb\x01\n\x0fsort_filter_req\x12\x0e\n\x06oldreq\x18\x01 \x01(\r\x12\x0f\n\x07setcode\x18\x02 \x01(\r\x12\x11\n\tsetDomain\x18\x03 \x01(\r\x12\x0f\n\x07\x63oltype\x18\x04 \x01(\r\x12\x0f\n\x07startxh\x18\x05 \x01(\r\x12\x0f\n\x07wantnum\x18\x06 \x01(\r\x12\x10\n\x08sorttype\x18\x07 \x01(\r\x12/\n\x04\x63ode\x18\x08 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\x0c\n\x04keys\x18\t \x03(\x04\x12\x0e\n\x06szkeys\x18\n \x03(\t\x12\x10\n\x08\x66ieldids\x18\x0b \x03(\r\x12\x0e\n\x06\x66ilter\x18\x0c \x01(\r\"R\n\x10\x62lock_score_data\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\r\n\x05score\x18\x02 \x01(\x05\"C\n\x0f\x62lock_score_ans\x12\x30\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32\".wuhan.jcl.report.block_score_data\"\'\n\tjtjb_data\x12\x0c\n\x04nkey\x18\x01 \x01(\x04\x12\x0c\n\x04jtjb\x18\x02 \x01(\x04\":\n\rjtjb_data_ans\x12)\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32\x1b.wuhan.jcl.report.jtjb_data\"*\n\x0b\x66\x61\x63tor_data\x12\x0c\n\x04type\x18\x01 \x01(\x05\x12\r\n\x05value\x18\x02 \x01(\x01\"\"\n\x12statics_factor_req\x12\x0c\n\x04type\x18\x01 \x03(\x05\"A\n\x12statics_factor_ans\x12+\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32\x1d.wuhan.jcl.report.factor_data\"*\n\x06lbdata\x12\n\n\x02lb\x18\x01 \x01(\x05\x12\x14\n\x0cstock_counts\x18\x02 \x01(\x05\"L\n\x0bhislb_stock\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\x0c\n\x04isLb\x18\x02 \x01(\x08\"\x89\x01\n\x10his_lb_date_line\x12\x0c\n\x04\x64\x61te\x18\x01 \x01(\x05\x12\x35\n\x0elb_line_stocks\x18\x02 \x03(\x0b\x32\x1d.wuhan.jcl.report.hislb_stock\x12\x30\n\x0elb_line_counts\x18\x03 \x03(\x0b\x32\x18.wuhan.jcl.report.lbdata\"3\n\rhislbdata_req\x12\x11\n\tstartDate\x18\x01 \x01(\x05\x12\x0f\n\x07\x65ndDate\x18\x02 \x01(\x05\"J\n\rhislbdata_ans\x12\x39\n\rlb_line_datas\x18\x01 \x03(\x0b\x32\".wuhan.jcl.report.his_lb_date_line\"A\n\x10static_blocks_zt\x12\x0c\n\x04nkey\x18\x01 \x01(\x04\x12\x11\n\tsetDomain\x18\x02 \x01(\x05\x12\x0c\n\x04nums\x18\x03 \x01(\x05\"H\n\x14statics_block_zt_ans\x12\x30\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32\".wuhan.jcl.report.static_blocks_zt\"k\n\x12hot_block_sort_req\x12\x0f\n\x07\x63oltype\x18\x01 \x01(\r\x12\x10\n\x08sorttype\x18\x02 \x01(\r\x12\x0f\n\x07startxh\x18\x03 \x01(\r\x12\x0f\n\x07wantnum\x18\x04 \x01(\r\x12\x10\n\x08\x66ieldids\x18\x05 \x03(\r\"v\n\x12hot_block_sort_ans\x12\r\n\x05total\x18\x01 \x01(\r\x12\r\n\x05\x63ount\x18\x02 \x01(\r\x12\x10\n\x08\x66ieldids\x18\x03 \x03(\r\x12\x30\n\x06\x66ields\x18\x04 \x03(\x0b\x32 .wuhan.jcl.report.hq_with_fields\"L\n\x0bjjyzb_stock\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\x0c\n\x04zanf\x18\x02 \x01(\x01\"A\n\x10jjyzb_stocks_ans\x12-\n\x06stocks\x18\x01 \x03(\x0b\x32\x1d.wuhan.jcl.report.jjyzb_stock\"+\n\rlbsbdb_stocks\x12\x0c\n\x04type\x18\x01 \x01(\x05\x12\x0c\n\x04nkey\x18\x02 \x03(\x04\"-\n\x1dstaticsdata_lbdbsb_stocks_req\x12\x0c\n\x04type\x18\x01 \x01(\x05\"P\n\x1dstaticsdata_lbdbsb_stocks_ans\x12/\n\x06stocks\x18\x01 \x03(\x0b\x32\x1f.wuhan.jcl.report.lbsbdb_stocks\"Q\n\x1estaticsdata_ztlb_stockinfo_req\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\"p\n\x1estaticsdata_ztlb_stockinfo_ans\x12/\n\x04\x63ode\x18\x01 \x01(\x0b\x32!.wuhan.jcl.report.tagCodeWithNkey\x12\x0e\n\x06lbnums\x18\x02 \x01(\x05\x12\r\n\x05money\x18\x03 \x01(\x01\"\'\n\trank_data\x12\x0c\n\x04nkey\x18\x01 \x01(\x04\x12\x0c\n\x04rank\x18\x02 \x01(\r\"A\n\x08rank_arr\x12\n\n\x02id\x18\x01 \x01(\r\x12)\n\x04\x64\x61ta\x18\x02 \x03(\x0b\x32\x1b.wuhan.jcl.report.rank_data*\xab!\n\x0creport_reqno\x12\r\n\tERROR_REQ\x10\x00\x12\x0f\n\x0bMONITOR_REQ\x10\x15\x12\x10\n\x0c\x41UTOGBBQ_REQ\x10\x16\x12\x10\n\x0c\x41UTOBASE_REQ\x10\x17\x12\x10\n\x0cHOSTMORE_REQ\x10\x1f\x12\x10\n\x0bPUSH_HQ_SUB\x10\x86\x01\x12\x12\n\rPUSH_HQ_UNSUB\x10\x87\x01\x12\x14\n\x0fPUSH_STR_HQ_SUB\x10\x90\x01\x12\x16\n\x11PUSH_STR_HQ_UNSUB\x10\x91\x01\x12\x10\n\x0bPUSH_DX_SUB\x10\x96\x01\x12\x12\n\rPUSH_DX_UNSUB\x10\x97\x01\x12\x12\n\rPUSH_PZYD_SUB\x10\x98\x01\x12\x14\n\x0fPUSH_PZYD_UNSUB\x10\x99\x01\x12\x11\n\x0c\x43ODE_NEW_REQ\x10\xd7\x08\x12\x0f\n\nCODE6_NREQ\x10\xd6\x08\x12\x10\n\x0bOLDNAME_REQ\x10\xe0\x08\x12\x0f\n\nZHSORT_REQ\x10\xb5\t\x12\x15\n\x10\x43OMBAVERAGE_NREQ\x10\xcf\n\x12\x13\n\x0e\x43OMBBLOCK_NREQ\x10\xd0\n\x12\x0c\n\x07\x46JB_REQ\x10\xd5\n\x12\x14\n\x0f\x41NALY_RANGE_REQ\x10\xd7\n\x12\x0f\n\nMINUTE_REQ\x10\xd9\n\x12\x11\n\x0c\x41NALY_TQ_REQ\x10\xda\n\x12\x10\n\x0bSORT_HQ_REQ\x10\xbf\r\x12\x11\n\x0cMULTI_HQ_REQ\x10\xc0\r\x12\x14\n\x0fMULTI_EX_HQ_REQ\x10\xc1\r\x12\x11\n\x0c\x46ULL_MMP_REQ\x10\xc2\r\x12\x10\n\x0bL2_TICK_REQ\x10\xc4\r\x12\x12\n\rSTOCK_GPS_REQ\x10\xc5\r\x12\x12\n\rSORT_CODE_REQ\x10\xc6\r\x12\x11\n\x0cMMP_TICK_REQ\x10\xc7\r\x12\x16\n\x11\x41LL_MONEYFLOW_REQ\x10\x89\x0e\x12\x16\n\x11ONE_MONEYFLOW_REQ\x10\x8a\x0e\x12\x16\n\x11HIS_MONEYFLOW_REQ\x10\x8d\x0e\x12\x1c\n\x17\x41LL_MONEYFLOW_XDAYS_REQ\x10\x8e\x0e\x12\x1a\n\x15HIS_MONEYFLOW_MIN_REQ\x10\x8f\x0e\x12\x1b\n\x16HIS_MONEYFLOW_TIME_REQ\x10\x90\x0e\x12\x1b\n\x16ONE_MONEYFLOW_XDAY_REQ\x10\x9a\x0e\x12\x15\n\x10\x43\x41LC_HISDATA_REQ\x10\xed\x0e\x12\x15\n\x10\x43\x41LC_CURDATA_REQ\x10\xee\x0e\x12\x11\n\x0c\x46UND_HIS_REQ\x10\xd1\x0f\x12\x14\n\x0f\x46UND_ZHSORT_REQ\x10\xd6\x0f\x12\x16\n\x11\x42LOCK_SORT_HQ_REQ\x10\xd8\x0f\x12\x12\n\rHSAG_ZDQJ_REQ\x10\xd9\x0f\x12\x12\n\rBOND_CODE_REQ\x10\xda\x0f\x12\x16\n\x11\x46UND_CODELIST_REQ\x10\xdb\x0f\x12\x13\n\x0eSORT_EX_HQ_REQ\x10\xdf\x0f\x12\x14\n\x0fSORT_SHQ_EX_REQ\x10\xe1\x0f\x12\x1c\n\x17SORT_STOCK_CONBLOCK_REQ\x10\xe2\x0f\x12\x19\n\x14SORT_STOCK_BLOCK_REQ\x10\xe3\x0f\x12\x15\n\x10SORT_NOHQ_EX_REQ\x10\xe5\x0f\x12\x16\n\x11SORT_NOSHQ_EX_REQ\x10\xe7\x0f\x12\x18\n\x13SORT_MONEY_RANK_REQ\x10\xe8\x0f\x12\x17\n\x12HOT_BLOCK_SORT_REQ\x10\xee\x0f\x12\x14\n\x0fLBDBSB_SORT_REQ\x10\xef\x0f\x12\x16\n\x11\x46ORMULAR_LIST_REQ\x10\xd6\x17\x12\x16\n\x11\x46ORMULAR_CALC_REQ\x10\xd7\x17\x12\x17\n\x12\x46ORMULAR_ONEXG_REQ\x10\xd8\x17\x12\x17\n\x12\x46ORMULAR_ALLXG_REQ\x10\xd9\x17\x12\x16\n\x11\x46ORMULAR_BKXG_REQ\x10\xda\x17\x12\x16\n\x11\x46ORMULAR_TPXG_REQ\x10\xdb\x17\x12\x1b\n\x16\x46ORMULAR_ALLXG_HIS_REQ\x10\xdf\x17\x12\x1b\n\x16\x46ORMULAR_ALLXG_ONE_REQ\x10\xe0\x17\x12\x1a\n\x15\x46ORMULAR_CODELIST_REQ\x10\xeb\x17\x12\x1d\n\x18\x46ORMULAR_CODELIST_MODIFY\x10\xec\x17\x12\x1d\n\x18\x46ORMULAR_CODELIST_CREATE\x10\xed\x17\x12\x1d\n\x18\x46ORMULAR_CODELIST_DELETE\x10\xee\x17\x12\x13\n\x0eHIS_MINUTE_REQ\x10\xe4\x1f\x12\r\n\x08TICK_REQ\x10\xe5\x1f\x12\x11\n\x0cHIS_TICK_REQ\x10\xe6\x1f\x12\x18\n\x13\x43LOUD_CALC_SORT_REQ\x10\x95\'\x12\x1a\n\x15\x46ORMULARSELFINDEX_REQ\x10\xf0.\x12\x14\n\x0f\x46ORMULARSET_REQ\x10\xf1.\x12\x16\n\x11RECALCFOMULAR_REQ\x10\xf2.\x12\x14\n\x0f\x41UCTIONDATA_REQ\x10\xadN\x12\x14\n\x0f\x41\x46TER_TRADE_REQ\x10\xb0N\x12\x1a\n\x15\x41UCTIONDATA_CCALL_REQ\x10\xb4N\x12\x0e\n\tZSCFG_REQ\x10\xf4N\x12\x16\n\x11\x43OMBBLOCK_EX_NREQ\x10\xa0j\x12\x15\n\x0f\x43\x41LCZAFDATA_REQ\x10\xa0\x9c\x01\x12\x14\n\x0eZDFSECTION_REQ\x10\xa1\x9c\x01\x12\x13\n\rZDFRESULT_REQ\x10\xa2\x9c\x01\x12\x13\n\rZDFPROFIT_REQ\x10\xa3\x9c\x01\x12\x13\n\rZDFZDTNUM_REQ\x10\xa4\x9c\x01\x12\x16\n\x10\x46YZBZTRESULT_REQ\x10\xa5\x9c\x01\x12\x13\n\rZDTRESULT_REQ\x10\xa6\x9c\x01\x12\x17\n\x11ZDFSECTION_TD_REQ\x10\xa7\x9c\x01\x12\x1d\n\x17STRATEGYCENTER_LIST_REQ\x10\x84\x9d\x01\x12\x1f\n\x19STRATEGYCENTER_PROFIT_REQ\x10\x85\x9d\x01\x12 \n\x1aSTRATEGYCENTER_SUCCESS_REQ\x10\x86\x9d\x01\x12\x18\n\x12STRATEGYONEKEY_REQ\x10\x88\x9d\x01\x12\x1f\n\x19STRATEGYONEKEY_PROFIT_REQ\x10\x89\x9d\x01\x12\x1f\n\x19STRATEGYONEKEY_RESULT_REQ\x10\x8a\x9d\x01\x12\x16\n\x10STRATEGYPOOL_REQ\x10\x8b\x9d\x01\x12\x16\n\x10\x42IGDATA_CXQN_REQ\x10\x8c\x9d\x01\x12\x16\n\x10\x42IGDATA_JHJJ_REQ\x10\x8d\x9d\x01\x12\x16\n\x10\x42IGDATA_ZTZY_REQ\x10\x8e\x9d\x01\x12\x1d\n\x17STRATEGYCENTER_JRTC_REQ\x10\x8f\x9d\x01\x12\x1d\n\x17STRATEGYCENTER_DQCC_REQ\x10\x90\x9d\x01\x12\x1d\n\x17STRATEGYCENTER_CLNG_REQ\x10\x91\x9d\x01\x12\x1f\n\x19STRATEGYCENTER_SIGNAL_REQ\x10\x92\x9d\x01\x12\x1c\n\x16STRATEGY_RMQL_DTXG_REQ\x10\x93\x9d\x01\x12\x17\n\x11STRATEGY_JZTZ_REQ\x10\x94\x9d\x01\x12\x1d\n\x17STRATEGY_GSCALCLIST_REQ\x10\x95\x9d\x01\x12\x1b\n\x15STRATEGY_GSRESULT_REQ\x10\x96\x9d\x01\x12\x1c\n\x16KSXG_MUL_STRATEGY_NREQ\x10\x97\x9d\x01\x12&\n SRV_STRATESGZH_ONEKEYCOMMENT_REQ\x10\xb1\x9d\x01\x12\x18\n\x12STRATEGY_LEVEL_REQ\x10\xb6\x9d\x01\x12\"\n\x1cSTRATEGY_SYSTEMPOOL_LIST_REQ\x10\xc0\x9d\x01\x12$\n\x1eSTRATEGY_SYSTEMPOOL_RESULT_REQ\x10\xc1\x9d\x01\x12\x11\n\x0bRPSDATA_REQ\x10\xe9\x9d\x01\x12\x11\n\x0bGSKDATA_REQ\x10\xea\x9d\x01\x12\x1d\n\x17NEW_PROTOCOL_LBDATA_REQ\x10\x81\xb4\x01\x12\x1c\n\x16STATICTISDATA_HIS_ZTLB\x10\x83\xb4\x01\x12\x1a\n\x14STATICTISDATA_FACTOR\x10\x84\xb4\x01\x12\x19\n\x13STATICTISDATA_BLOCK\x10\x85\xb4\x01\x12\x1f\n\x19STATICTISDATA_BLOCK_SCORE\x10\x86\xb4\x01\x12\x19\n\x13STATICTISDATA_JJYZB\x10\x87\xb4\x01\x12\x18\n\x12STATICTISDATA_JTJB\x10\x88\xb4\x01\x12!\n\x1bSTATICTISDATA_LBSBDB_STOCKS\x10\x89\xb4\x01\x12\x1e\n\x18STATICTISDATA_ZT_LB_INFO\x10\x8a\xb4\x01\x12\x0e\n\x08\x44XJL_REQ\x10\xb0\x9f\x01\x12\x0e\n\x08HQFD_REQ\x10\xb1\x9f\x01\x12\x13\n\rZTFX_ZDTB_REQ\x10\xb2\x9f\x01\x12\x12\n\x0cZTFX_ZBL_REQ\x10\xb3\x9f\x01\x12\x13\n\rZTFX_JRZF_REQ\x10\xb4\x9f\x01\x12\x0e\n\x08\x42KYD_REQ\x10\xb5\x9f\x01\x12\x0e\n\x08\x42KFB_REQ\x10\xb6\x9f\x01\x12\x0f\n\tZDNUM_REQ\x10\xba\x9f\x01\x12\x12\n\x0cHQEXDATA_REQ\x10\xbb\x9f\x01\x12\x12\n\x0cLTLBDATA_REQ\x10\xbc\x9f\x01\x12\x11\n\x0bLTLBMIN_REQ\x10\xbd\x9f\x01\x12\x11\n\x0bLTLBARR_REQ\x10\xbe\x9f\x01\x12\x12\n\x0cLBTJDATA_REQ\x10\xbf\x9f\x01\x12\x14\n\x0eMONEY_FLOW_REQ\x10\x94\xa0\x01\x12\x11\n\x0b\x46INANCE_REQ\x10\x88\xa4\x01\x12\x11\n\x0b\x46INDOWN_REQ\x10\x89\xa4\x01\x12\x12\n\x0c\x46INVALUE_REQ\x10\x8a\xa4\x01\x12\x11\n\x0b\x46INGPJY_REQ\x10\x8b\xa4\x01\x12\x11\n\x0b\x46INSCJY_REQ\x10\x8c\xa4\x01\x12\x12\n\x0c\x46INGPONE_REQ\x10\x8d\xa4\x01\x12\x15\n\x0fSNMONEYFLOW_REQ\x10\x8e\xa4\x01\x12\x15\n\x0fSNMONEYBASE_REQ\x10\x8f\xa4\x01\x12\x16\n\x10SNMONEYTOP10_REQ\x10\x90\xa4\x01\x12\x15\n\x0fSNMONEYHOLD_REQ\x10\x91\xa4\x01\x12\x0f\n\tHSIZS_REQ\x10\x92\xa4\x01\x12\x12\n\x0c\x46INANCE_NREQ\x10\x9a\xa4\x01\x12\x12\n\x0c\x46INBKJY_NREQ\x10\x9b\xa4\x01\x12\x13\n\rFINVALUE_NREQ\x10\x9c\xa4\x01\x12\x12\n\x0c\x46INGPJY_NREQ\x10\x9d\xa4\x01\x12\x12\n\x0c\x46INSCJY_NREQ\x10\x9e\xa4\x01\x12\x13\n\rFINGPONE_NREQ\x10\x9f\xa4\x01\x12\x16\n\x10\x46INANCE_NOW_NREQ\x10\xa0\xa4\x01\x12\x15\n\x0fNEWFINANCE_NREQ\x10\xa1\xa4\x01\x12#\n\x1dNEW_PROTOCOL_KLINE_OFFSET_REQ\x10\xfa\xab\x01\x12\x1d\n\x17NEW_PROTOCOL_MINUTE_REQ\x10\x84\xac\x01\x12\"\n\x1cNEW_PROTOCOL_MANY_MINUTE_REQ\x10\x89\xac\x01\x12\x12\n\x0cNEW_HQEX_REQ\x10\xe5\xac\x01\x12\x14\n\x0eNEW_NOHQEX_REQ\x10\xeb\xac\x01\x12\x16\n\x10NEW_NOHQ_ZDY_REQ\x10\xec\xac\x01\x12\x19\n\x13NEW_SORT_FILTER_REQ\x10\xed\xac\x01\x12\x1d\n\x17NEW_PROTOCOL_EXDATA_REQ\x10\xd6\xb3\x01\x12!\n\x1bSRV_STATICTIS_MONEY_DAY_REQ\x10\xa1\xb5\x01\x12!\n\x1bSRV_STATICTIS_MONEY_MIN_REQ\x10\xa2\xb5\x01\x12\'\n!SRV_STATICTIS_MILTI_MONEY_DAY_REQ\x10\xa3\xb5\x01\x12\x11\n\x0bHISCHIP_REQ\x10\xc0\xbb\x01\x12\x0e\n\x08JSON_REQ\x10\xb0\xea\x01\x12\x0f\n\tCOMBO_REQ\x10\xb1\xea\x01\x12\x14\n\x0e\x42\x46\x44\x41Y_DATA_REQ\x10\x94\xeb\x01\x12\x15\n\x0fKLINE_LABEL_REQ\x10\x95\xeb\x01\x12\x19\n\x13KLINE_LABEL_ARR_REQ\x10\x96\xeb\x01\x12\x1b\n\x15KLINE_MULSTK_ADAY_REQ\x10\x97\xeb\x01\x12\x15\n\x0fSORT_EXRANK_REQ\x10\xf8\xeb\x01\x12\x19\n\x13\x44ZLV2_ORDERTICK_REQ\x10\xdc\xec\x01\x12\x19\n\x13\x44ZLV2_SIGORTICK_REQ\x10\xdd\xec\x01\x12\x15\n\x0f\x44ZLV2_MULPK_REQ\x10\xde\xec\x01\x12\x19\n\x13\x44ZLV2_SIGPKINFO_REQ\x10\xdf\xec\x01\x12\x19\n\x13\x44ZLV2_KCBPHTICK_REQ\x10\xe0\xec\x01*\xd3\x14\n\ndomain_idx\x12\x0b\n\x07\x44I_SHAG\x10\x00\x12\x0b\n\x07\x44I_SHBG\x10\x01\x12\x0b\n\x07\x44I_SZAG\x10\x02\x12\x0b\n\x07\x44I_SZBG\x10\x03\x12\r\n\tDI_SHBOUD\x10\x04\x12\r\n\tDI_SZBOUD\x10\x05\x12\t\n\x05\x44I_AG\x10\x06\x12\t\n\x05\x44I_BG\x10\x07\x12\x0b\n\x07\x44I_BOND\x10\x08\x12\x0b\n\x07\x44I_FUND\x10\t\x12\x0c\n\x08\x44I_ALLGP\x10\n\x12\x0f\n\x0b\x44I_ALLINDEX\x10\x0b\x12\x10\n\x0c\x44I_ZHONGXIAO\x10\x0c\x12\n\n\x06\x44I_GEM\x10\r\x12\t\n\x05\x44I_SB\x10\x0e\x12\x10\n\x0c\x44I_DELISTING\x10\x0f\x12\r\n\tDI_OPTION\x10\x11\x12\t\n\x05\x44I_BH\x10\x13\x12\x0e\n\nDI_QH_DLSP\x10\x14\x12\x0e\n\nDI_QH_ZZSP\x10\x15\x12\x0e\n\nDI_QH_SHSP\x10\x16\x12\x0e\n\nDI_QH_GZQH\x10\x17\x12\t\n\x05\x44I_SJ\x10\x18\x12\n\n\x06\x44I_LDJ\x10\x19\x12\t\n\x05\x44I_TJ\x10\x1a\x12\n\n\x06\x44I_BTB\x10\x1b\x12\n\n\x06\x44I_KZZ\x10\x1c\x12\x10\n\x0c\x44I_ALL_BLOCK\x10\x1e\x12\x0b\n\x07\x44I_AREA\x10\x1f\x12\x0f\n\x0b\x44I_INDUSTRY\x10 \x12\x0e\n\nDI_CONCEPT\x10!\x12\x14\n\x10\x44I_RM_BLOCK_ALL_\x10\"\x12\x15\n\x11\x44I_RM_BLOCK_AREA_\x10#\x12\x19\n\x15\x44I_RM_BLOCK_INDUSTRY_\x10$\x12\x18\n\x14\x44I_RM_BLOCK_CONCEPT_\x10%\x12\t\n\x05\x44I_HX\x10&\x12\x0f\n\x0b\x44I_HX_YSZB_\x10\'\x12\x0f\n\x0b\x44I_HX_YSXB_\x10(\x12\x0f\n\x0b\x44I_HX_YBSC_\x10)\x12\x0f\n\x0b\x44I_HX_QBSC_\x10*\x12\x0f\n\x0b\x44I_HX_PSJK_\x10+\x12\x0f\n\x0b\x44I_HX_CCQB_\x10,\x12\x0f\n\x0b\x44I_SZ_BLOCK\x10-\x12\x0f\n\x0b\x44I_FG_BLOCK\x10.\x12\x0f\n\x0b\x44I_HY_BLOCK\x10/\x12\x0c\n\x08\x44I_QH_QL\x10\x32\x12\x0e\n\nDI_QH_HQXG\x10\x33\x12\x11\n\rDI_QH_ALL_LDJ\x10\x34\x12\x13\n\x0f\x44I_FUND_PENGHUA\x10\x35\x12\n\n\x06\x44I_TEA\x10\x36\x12\r\n\tDI_TEA_ZS\x10\x37\x12\x10\n\x0c\x44I_TEA_GOODS\x10\x38\x12\n\n\x06\x44I_DPT\x10\x39\x12\t\n\x05\x44I_HK\x10:\x12\x0e\n\nDI_AG_HQBJ\x10\x46\x12\x0e\n\nDI_AG_ZJJC\x10G\x12\x0e\n\nDI_AG_CWSJ\x10H\x12\x0e\n\nDI_AG_HQTJ\x10I\x12\x19\n\x15\x44I_SCIENCE_TECHNOLOGY\x10J\x12\n\n\x06\x44I_ETF\x10K\x12\n\n\x06\x44I_OIL\x10P\x12\x0b\n\x07\x44I_RMQL\x10Q\x12\x0b\n\x07\x44I_DTXG\x10R\x12\x0b\n\x07\x44I_DDTJ\x10Z\x12\x0b\n\x07\x44I_JZTZ\x10[\x12\x17\n\x13\x44I_FIRST_AREA_BLOCK\x10\x64\x12\x17\n\x12\x44I_LAST_AREA_BLOCK\x10\xc7\x01\x12\x1c\n\x17\x44I_FIRST_INDUSTRY_BLOCK\x10\xc8\x01\x12\x1b\n\x16\x44I_LAST_INDUSTRY_BLOCK\x10\xab\x02\x12\x1b\n\x16\x44I_FIRST_CONCEPT_BLOCK\x10\xac\x02\x12\x1a\n\x15\x44I_LAST_CONCEPT_BLOCK\x10\x94\n\x12\x1a\n\x15\x44I_FULLSCREEN_BHGOODS\x10\xdd\x0b\x12\x17\n\x12\x44I_FULLSCREEN_FIVE\x10\xde\x0b\x12\x1a\n\x15\x44I_FULLSCREEN_CURRDAY\x10\xdf\x0b\x12\x1c\n\x17\x44I_FULLSCREEN_CURRDAYBK\x10\xe0\x0b\x12\x17\n\x12\x44I_FULLSCREEN_SHAG\x10\xe1\x0b\x12\x17\n\x12\x44I_FULLSCREEN_SZAG\x10\xe2\x0b\x12\x17\n\x12\x44I_FULLSCREEN_AREA\x10\xe3\x0b\x12\x1b\n\x16\x44I_FULLSCREEN_INDUSTRY\x10\xe4\x0b\x12\x1a\n\x15\x44I_FULLSCREEN_CONCEPT\x10\xe5\x0b\x12\x16\n\x11\x44I_FULLSCREEN_ZXG\x10\xe6\x0b\x12\x1f\n\x1a\x44I_FULLSCREEN_CURRDAYBK_GG\x10\xe8\x07\x12%\n DI_FULLSCREEN_CURRDAYBK_GG_FIRST\x10\xcc\x08\x12$\n\x1f\x44I_FULLSCREEN_CURRDAYBK_GG_LAST\x10\xfc\x11\x12\x16\n\x11\x44I_FUND_HOMEPAGE_\x10\xa0\x1f\x12\x15\n\x10\x44I_FUND_SUBJECT_\x10\xa1\x1f\x12\x0f\n\nDI_FUND_B_\x10\xa2\x1f\x12\x19\n\x14\x44I_FUND_SUBJECT_HOT_\x10\xa3\x1f\x12\x18\n\x13\x44I_FUND_HOMEPAGE_A_\x10\xa4\x1f\x12\x18\n\x13\x44I_FUND_HOMEPAGE_M_\x10\xa5\x1f\x12\x1c\n\x17\x44I_FUND_HOMEPAGE_FJ_T2_\x10\xa6\x1f\x12\x1c\n\x17\x44I_FUND_HOMEPAGE_FJ_T1_\x10\xa7\x1f\x12\x1c\n\x17\x44I_FUND_HOMEPAGE_FJ_T0_\x10\xa8\x1f\x12\x16\n\x11\x44I_FUND_MONEY_T0_\x10\xa9\x1f\x12\x16\n\x11\x44I_FUND_MONEY_T1_\x10\xaa\x1f\x12\x16\n\x11\x44I_FIRST_SZ_BLOCK\x10\xfc*\x12\x16\n\x11\x44I_FIRST_FG_BLOCK\x10\xf0.\x12\x10\n\x0b\x44I_SORT_MAX\x10\xa8\x46\x12\x13\n\x0e\x44I_SYSTEMBLOCK\x10\xa9\x46\x12\x0e\n\tDI_CUSTOM\x10\xaa\x46\x12\x13\n\x0e\x44I_CUSTOM_ZJJC\x10\xab\x46\x12\x13\n\x0e\x44I_CUSTOM_CWSH\x10\xac\x46\x12\x13\n\x0e\x44I_CUSTOM_HQTJ\x10\xad\x46\x12\x0c\n\x07\x44I_SHHG\x10\xaf\x46\x12\x0c\n\x07\x44I_SZHG\x10\xb0\x46\x12\x0c\n\x07\x44I_ZBXG\x10\xb3\x46\x12\x0e\n\tDI_FUND_A\x10\xb4\x46\x12\r\n\x08\x44I_IK_UP\x10\x8cG\x12\r\n\x08\x44I_IK_ZY\x10\x8dG\x12\r\n\x08\x44I_IK_XS\x10\x8eG\x12\r\n\x08\x44I_GZ_HY\x10\x8fG\x12\r\n\x08\x44I_GZ_ZS\x10\x90G\x12\x11\n\x0c\x44I_GZ_CUSTOM\x10\x91G\x12\x14\n\x0f\x44I_CUSTOM_HQCAL\x10\xba\x46\x12\x11\n\x0c\x44I_CXQN_SCDK\x10\xf0G\x12\x11\n\x0c\x44I_CXQN_CGHF\x10\xf1G\x12\x11\n\x0c\x44I_CXQN_YZLB\x10\xf2G\x12\x11\n\x0c\x44I_CXQN_ZCXG\x10\xf3G\x12\x11\n\x0c\x44I_CXQN_JDCD\x10\xf4G\x12\x11\n\x0c\x44I_CXQN_DJQL\x10\xf5G\x12\x11\n\x0c\x44I_JHJJ_ZTSP\x10\xd4H\x12\x11\n\x0c\x44I_JHJJ_DTSP\x10\xd5H\x12\x11\n\x0c\x44I_JHJJ_JJKD\x10\xd6H\x12\x11\n\x0c\x44I_JHJJ_JJKK\x10\xd7H\x12\x11\n\x0c\x44I_JHJJ_DKHZ\x10\xd8H\x12\x0c\n\x07\x44I_ZTZY\x10\xb8I\x12\x11\n\x0c\x44I_CLZX_JRTC\x10\xc2I\x12\x11\n\x0c\x44I_CLZX_DQCC\x10\xc3I\x12\x11\n\x0c\x44I_CLZX_CLNG\x10\xc4I\x12\x0c\n\x07\x44I_SCRD\x10\xeaI\x12\x12\n\rDI_AG_ZSOTHER\x10\x9cJ\x12\x0e\n\tDI_FILTER\x10\xf4J\x12\r\n\x08\x44I_GPCXG\x10\x80K\x12\x0f\n\nDI_AG_GLPZ\x10\xe4K\x12\x1c\n\x17\x44I_FIRST_FORMULAR_BLOCK\x10\xc8L\x12\x1b\n\x16\x44I_LAST_FORMULAR_BLOCK\x10\xacM\x12\x11\n\x0c\x44I_CONDITION\x10\x8fN\x12\x1a\n\x15\x44I_FIRST_CUSTOM_BLOCK\x10\x90N*.\n\x0blbsbdb_type\x12\x07\n\x03\x41LL\x10\x00\x12\x06\n\x02SB\x10\x01\x12\x06\n\x02LB\x10\x02\x12\x06\n\x02\x44\x42\x10\x03*\xe1\x34\n\x0b\x63oltype_idx\x12\x08\n\x04ZQDM\x10\x00\x12\x08\n\x04ZQJC\x10\x01\x12\x08\n\x04ZRSP\x10\x02\x12\x08\n\x04JRKP\x10\x03\x12\x08\n\x04ZGCJ\x10\x04\x12\x08\n\x04ZDCJ\x10\x05\x12\x08\n\x04ZJCJ\x10\x06\x12\x08\n\x04ZGJM\x10\x07\x12\x08\n\x04ZDJM\x10\x08\x12\x07\n\x03\x43JL\x10\t\x12\x08\n\x04\x43JJE\x10\n\x12\x06\n\x02XS\x10\x0b\x12\x08\n\x04QRSD\x10\x0c\x12\x08\n\x04QBSD\x10\r\x12\x07\n\x03ZAF\x10\x0e\x12\x07\n\x03ZEF\x10\x0f\x12\x08\n\x04JUNJ\x10\x10\x12\x07\n\x03SYL\x10\x11\x12\x07\n\x03WTB\x10\x12\x12\x06\n\x02NP\x10\x13\x12\x06\n\x02WP\x10\x14\x12\x07\n\x03LWB\x10\x15\x12\x07\n\x03WLC\x10\x16\x12\x08\n\x04\x42JL1\x10\x17\x12\x08\n\x04SJL1\x10\x18\x12\x07\n\x03\x42J1\x10\x19\x12\x07\n\x03SJ1\x10\x1a\x12\x07\n\x03\x42J2\x10\x1b\x12\x08\n\x04\x42JL2\x10\x1c\x12\x07\n\x03SJ2\x10\x1d\x12\x08\n\x04SJL2\x10\x1e\x12\x07\n\x03\x42J3\x10\x1f\x12\x08\n\x04\x42JL3\x10 \x12\x07\n\x03SJ3\x10!\x12\x08\n\x04SJL3\x10\"\x12\n\n\x06LIANGB\x10#\x12\t\n\x05J_HSL\x10$\x12\n\n\x06J_LTGB\x10%\x12\n\n\x06J_LTSZ\x10&\x12\t\n\x05J_ZSZ\x10\'\x12\x08\n\x04\x44KPH\x10(\x12\x08\n\x04\x44THL\x10)\x12\x08\n\x04\x44TZS\x10*\x12\x08\n\x04KTHB\x10+\x12\x08\n\x04KTZS\x10,\x12\x07\n\x03QRD\x10-\x12\n\n\x06ZANGSU\x10.\x12\x07\n\x03HYD\x10/\x12\x08\n\x04MBZL\x10\x30\x12\t\n\x05MBHSL\x10\x31\x12\n\n\x06J_GXRQ\x10\x32\x12\x0b\n\x07J_START\x10\x33\x12\t\n\x05J_ZGB\x10\x34\x12\t\n\x05J_GJG\x10\x35\x12\x0c\n\x08J_FQRFRG\x10\x36\x12\t\n\x05J_FRG\x10\x37\x12\x08\n\x04J_BG\x10\x38\x12\x08\n\x04J_HG\x10\x39\x12\t\n\x05J_ZGG\x10:\x12\t\n\x05J_ZZC\x10;\x12\n\n\x06J_LDZC\x10<\x12\n\n\x06J_GDZC\x10=\x12\n\n\x06J_WXZC\x10>\x12\n\n\x06J_CQTZ\x10?\x12\n\n\x06J_LDFZ\x10@\x12\n\n\x06J_CQFZ\x10\x41\x12\x0b\n\x07J_ZBGJJ\x10\x42\x12\t\n\x05J_JZC\x10\x43\x12\n\n\x06J_ZYSY\x10\x44\x12\n\n\x06J_ZYLY\x10\x45\x12\n\n\x06J_QTLY\x10\x46\x12\n\n\x06J_YYLY\x10G\x12\n\n\x06J_TZSY\x10H\x12\n\n\x06J_BTSY\x10I\x12\x0b\n\x07J_YYWSZ\x10J\x12\x0c\n\x08J_SNSYTZ\x10K\x12\n\n\x06J_LYZE\x10L\x12\n\n\x06J_SHLY\x10M\x12\t\n\x05J_JLY\x10N\x12\x0b\n\x07J_WFPLY\x10O\x12\x0c\n\x08J_TZMGJZ\x10P\x12\t\n\x05J_JYL\x10Q\x12\x0b\n\x07J_MGWFP\x10R\x12\n\n\x06J_MGSY\x10S\x12\x0b\n\x07J_MGGJJ\x10T\x12\x0b\n\x07J_MGJZC\x10U\x12\x0b\n\x07J_GDQYB\x10V\x12\t\n\x05ZBCOL\x10W\x12\x0e\n\nSPELL_CODE\x10X\x12\n\n\x06QH_JSJ\x10Y\x12\x0b\n\x07QH_YJSJ\x10Z\x12\n\n\x06SPREAD\x10[\x12\n\n\x06\x42SUNIT\x10\\\x12\x10\n\x0c\x43URRENCYNAME\x10]\x12\r\n\tAVERPRICE\x10^\x12\x0c\n\x08YIELDVAL\x10_\x12\x0c\n\x08HIS_HIGH\x10`\x12\x0b\n\x07HIS_LOW\x10\x61\x12\x07\n\x03IEP\x10\x62\x12\x07\n\x03IEV\x10\x63\x12\n\n\x06MRKCAP\x10\x64\x12\x0c\n\x08PREMINUM\x10\x66\x12\x0b\n\x07GEARING\x10g\x12\r\n\tEXECPRICE\x10h\x12\r\n\tCONVRATIO\x10i\x12\x0e\n\nEXPIREDATE\x10j\x12\x0e\n\nNOTAXPRCIE\x10k\x12\x10\n\x0c\x44\x45POSITMONEY\x10l\x12\x13\n\x0f\x44\x41YDEPOSITMONEY\x10m\x12\x0f\n\x0b\x41VGTAXPRICE\x10n\x12\x0b\n\x07ZXGTIME\x10u\x12\x08\n\x04SSBK\x10v\x12\x08\n\x04ZLJZ\x10w\x12\x08\n\x04ZLZB\x10x\x12\x08\n\x04SMJZ\x10y\x12\x08\n\x04SMZB\x10z\x12\x0b\n\x06HTMLXX\x10\x81\x01\x12\r\n\x08URGENTXX\x10\x82\x01\x12\x0e\n\tNEWSGATE1\x10\x83\x01\x12\x0e\n\tNEWSGATE2\x10\x84\x01\x12\x0c\n\x07I_ZJJLR\x10\x85\x01\x12\x0e\n\tI_ZJJLRBL\x10\x86\x01\x12\x0c\n\x07I_ZLJLR\x10\x87\x01\x12\x0e\n\tI_ZLJLRBL\x10\x88\x01\x12\r\n\x08I_ZLZJLR\x10\x89\x01\x12\x0f\n\nI_ZLZJLRBL\x10\x8a\x01\x12\r\n\x08I_ZLZJLC\x10\x8b\x01\x12\x0f\n\nI_ZLZJLCBL\x10\x8c\x01\x12\x10\n\x0bI_INOUTZJBL\x10\x8d\x01\x12\r\n\x08I_ZLCJBL\x10\x8e\x01\x12\x0b\n\x06\x42H_DHL\x10\x8f\x01\x12\x0b\n\x06\x42H_RZC\x10\x90\x01\x12\x08\n\x03\x43\x43L\x10\x91\x01\x12\x08\n\x03ZCC\x10\x92\x01\x12\t\n\x04\x43\x43ZJ\x10\x93\x01\x12\x0b\n\x06S_ZLLD\x10\x94\x01\x12\x0b\n\x06S_ZLQD\x10\x95\x01\x12\x0b\n\x06S_ZJDL\x10\x96\x01\x12\x0b\n\x06ROWNUM\x10\x97\x01\x12\x0e\n\tSEPARATOR\x10\x98\x01\x12\n\n\x05ZS_LZ\x10\x99\x01\x12\n\n\x05ZS_LD\x10\x9a\x01\x12\x0b\n\x06ZS_CFG\x10\x9b\x01\x12\x0b\n\x06ZS_ZDP\x10\x9c\x01\x12\x0c\n\x07ZS_SZJS\x10\x9d\x01\x12\x0c\n\x07ZS_XDJS\x10\x9e\x01\x12\x0b\n\x06ZS_PJS\x10\x9f\x01\x12\x0f\n\nFUND_BCODE\x10\xa0\x01\x12\x0f\n\nFUND_BNAME\x10\xa1\x01\x12\x10\n\x0b\x46UND_BPRICE\x10\xa2\x01\x12\x0e\n\tFUND_BZAF\x10\xa3\x01\x12\x12\n\rFUND_BJINGZHI\x10\xa4\x01\x12\x0e\n\tFUND_BYJL\x10\xa5\x01\x12\x10\n\x0b\x46UND_BFEBHL\x10\xa6\x01\x12\x10\n\x0b\x46UND_SGTLKJ\x10\xa7\x01\x12\x10\n\x0b\x46UND_SHTLKJ\x10\xa8\x01\x12\x11\n\x0c\x46UND_ABPRICE\x10\xa9\x01\x12\x0f\n\nFUND_MCODE\x10\xaa\x01\x12\x0f\n\nFUND_MNAME\x10\xab\x01\x12\x12\n\rFUND_MJINGZHI\x10\xac\x01\x12\x10\n\x0b\x46UND_ZSCODE\x10\xad\x01\x12\x10\n\x0b\x46UND_ZSNAME\x10\xae\x01\x12\x0f\n\nFUND_ZSZAF\x10\xaf\x01\x12\x0f\n\nFUND_ACODE\x10\xb0\x01\x12\x0f\n\nFUND_ANAME\x10\xb1\x01\x12\x10\n\x0b\x46UND_APRICE\x10\xb2\x01\x12\x0e\n\tFUND_AZAF\x10\xb3\x01\x12\x12\n\rFUND_AJINGZHI\x10\xb4\x01\x12\x0e\n\tFUND_AYJL\x10\xb5\x01\x12\r\n\x08\x46UND_BFE\x10\xb6\x01\x12\x10\n\x0b\x46UND_BJZBHL\x10\xb7\x01\x12\x10\n\x0b\x46UND_FOLDUP\x10\xb8\x01\x12\x12\n\rFUND_FOLDDOWN\x10\xb9\x01\x12\x12\n\rFUND_JZFOLDUP\x10\xba\x01\x12\x14\n\x0f\x46UND_JZFOLDDOWN\x10\xbb\x01\x12\x11\n\x0c\x46UND_BNOTICE\x10\xbc\x01\x12\x11\n\x0c\x46UND_SUBJECT\x10\xbd\x01\x12\x11\n\x0c\x46UND_SGPRICE\x10\xbe\x01\x12\x11\n\x0c\x46UND_SHPRICE\x10\xbf\x01\x12\x0f\n\nFUND_ABYJL\x10\xc0\x01\x12\x10\n\x0b\x46UND_MJZBHL\x10\xc1\x01\x12\x11\n\x0c\x46UND_MNOTICE\x10\xc2\x01\x12\x0e\n\tFUND_FEZB\x10\xc3\x01\x12\x10\n\x0b\x46UND_AJZBHL\x10\xc4\x01\x12\x14\n\x0f\x46UND_AFOLDPRICE\x10\xc5\x01\x12\x11\n\x0c\x46UND_APROFIT\x10\xc6\x01\x12\r\n\x08\x46UND_AFE\x10\xc7\x01\x12\x10\n\x0b\x46UND_AFEBHL\x10\xc8\x01\x12\x12\n\rFUND_ARESTDAY\x10\xc9\x01\x12\x12\n\rFUND_AENDDATE\x10\xca\x01\x12\x13\n\x0e\x46UND_AYDPROFIT\x10\xcb\x01\x12\x11\n\x0c\x46UND_ANOTICE\x10\xcc\x01\x12\x12\n\rMONEY_ZLJME_1\x10\xcd\x01\x12\x12\n\rMONEY_ZLJME_2\x10\xce\x01\x12\x12\n\rMONEY_ZLJME_3\x10\xcf\x01\x12\x12\n\rMONEY_ZLJME_4\x10\xd0\x01\x12\x12\n\rMONEY_ZLJME_5\x10\xd1\x01\x12\x12\n\rMONEY_ZLJZB_1\x10\xd2\x01\x12\x12\n\rMONEY_ZLJZB_2\x10\xd3\x01\x12\x12\n\rMONEY_ZLJZB_3\x10\xd4\x01\x12\x12\n\rMONEY_ZLJZB_4\x10\xd5\x01\x12\x12\n\rMONEY_ZLJZB_5\x10\xd6\x01\x12\x12\n\rMONEY_SHJME_1\x10\xd7\x01\x12\x12\n\rMONEY_SHJME_2\x10\xd8\x01\x12\x12\n\rMONEY_SHJME_3\x10\xd9\x01\x12\x12\n\rMONEY_SHJME_4\x10\xda\x01\x12\x12\n\rMONEY_SHJME_5\x10\xdb\x01\x12\x12\n\rMONEY_SHJZB_1\x10\xdc\x01\x12\x12\n\rMONEY_SHJZB_2\x10\xdd\x01\x12\x12\n\rMONEY_SHJZB_3\x10\xde\x01\x12\x12\n\rMONEY_SHJZB_4\x10\xdf\x01\x12\x12\n\rMONEY_SHJZB_5\x10\xe0\x01\x12\x11\n\x0c\x46UND_JINGZHI\x10\xe1\x01\x12\r\n\x08\x46UND_YJL\x10\xe2\x01\x12\x14\n\x0f\x46UND_UPDATEDATE\x10\xe3\x01\x12\x12\n\rFUND_POSITION\x10\xe4\x01\x12\x13\n\x0e\x46UND_B_REAL_JZ\x10\xe5\x01\x12\x14\n\x0f\x46UND_PRICELEVEL\x10\xe6\x01\x12\x0f\n\nFUND_LEVEL\x10\xe7\x01\x12\x14\n\x0f\x46UND_REAL_LEVEL\x10\xe8\x01\x12\x11\n\x0c\x46UND_FINANCE\x10\xe9\x01\x12\x12\n\rFUND_ABYJL_T1\x10\xea\x01\x12\x12\n\rFUND_ABYJL_T2\x10\xeb\x01\x12\x11\n\x0c\x46UND_AFE_INC\x10\xec\x01\x12\x13\n\x0e\x46UND_RATE_RULE\x10\xed\x01\x12\x0e\n\tFUND_RATE\x10\xee\x01\x12\x13\n\x0e\x46UND_RATE_NEXT\x10\xef\x01\x12\x12\n\rFUND_M_REL_JZ\x10\xf0\x01\x12\x10\n\x0b\x46UND_CREATE\x10\xf1\x01\x12\x10\n\x0b\x46UND_A_CJJE\x10\xf2\x01\x12\x10\n\x0b\x46UND_B_CJJE\x10\xf3\x01\x12\x0f\n\nFUND_A_HSL\x10\xf4\x01\x12\x0f\n\nFUND_B_HSL\x10\xf5\x01\x12\x11\n\x0c\x46UND_BFE_INC\x10\xf6\x01\x12\x10\n\x0b\x46UND_MPRICE\x10\xf7\x01\x12\x0e\n\tFUND_MZAF\x10\xf8\x01\x12\x0e\n\tFUND_MYJL\x10\xf9\x01\x12\x10\n\x0b\x46UND_MABYJL\x10\xfa\x01\x12\x11\n\x0c\x46UND_ENDDATE\x10\xfb\x01\x12\x10\n\x0b\x46UND_M_TYPE\x10\xfc\x01\x12\x0b\n\x06TIPDIS\x10\xfd\x01\x12\x0b\n\x06ZGCODE\x10\xfe\x01\x12\x0b\n\x06ZGNAME\x10\xff\x01\x12\x0e\n\tZGPRICE_0\x10\x80\x02\x12\n\n\x05ZGZAF\x10\x81\x02\x12\r\n\x08ZGASSETS\x10\x82\x02\x12\x0b\n\x06ZGRATE\x10\x83\x02\x12\n\n\x05ZGQSR\x10\x84\x02\x12\x0e\n\tZGPRICE_1\x10\x85\x02\x12\r\n\x08ZGEVALUE\x10\x86\x02\x12\x0b\n\x06YJRATE\x10\x87\x02\x12\n\n\x05HSCFB\x10\x88\x02\x12\x0e\n\tHSCFPRICE\x10\x89\x02\x12\x0c\n\x07HSPRICE\x10\x8a\x02\x12\n\n\x05HSQSR\x10\x8b\x02\x12\n\n\x05QSCFB\x10\x8c\x02\x12\x0e\n\tQSCFPRICE\x10\x8d\x02\x12\x0c\n\x07QSPRICE\x10\x8e\x02\x12\n\n\x05QSQSR\x10\x8f\x02\x12\x0c\n\x07\x42ONDAMT\x10\x90\x02\x12\x0c\n\x07RESTAMT\x10\x91\x02\x12\x0b\n\x06ZZRATE\x10\x92\x02\x12\x10\n\x0bZZSTARTDATE\x10\x93\x02\x12\x0e\n\tZZENDDATE\x10\x94\x02\x12\x0c\n\x07RESTDAY\x10\x95\x02\x12\r\n\x08INTEREST\x10\x96\x02\x12\t\n\x04SQSY\x10\x97\x02\x12\t\n\x04SHSY\x10\x98\x02\x12\t\n\x04ZTJG\x10\x99\x02\x12\t\n\x04\x44TJG\x10\x9a\x02\x12\x0b\n\x06GG_F10\x10\x9b\x02\x12\x0c\n\x07GPCTIME\x10\x9c\x02\x12\r\n\x08GPCPRICE\x10\x9d\x02\x12\x0b\n\x06GPCZAF\x10\x9e\x02\x12\x0f\n\nGPC5DAYZAF\x10\x9f\x02\x12\r\n\x08SECZFCOL\x10\xa0\x02\x12\x0b\n\x06IKSZGL\x10\xa1\x02\x12\x0b\n\x06IKPPGG\x10\xa2\x02\x12\r\n\x08IKPPTIME\x10\xa3\x02\x12\n\n\x05IKPPD\x10\xa4\x02\x12\r\n\x08ZAF_5MIN\x10\xa5\x02\x12\x0e\n\tZAF_3DAYS\x10\xa6\x02\x12\x0e\n\tZAF_5DAYS\x10\xa7\x02\x12\x0f\n\nZAF_20DAYS\x10\xa8\x02\x12\x0e\n\tZANG_DAYS\x10\xa9\x02\x12\x10\n\x0bHIGH_20DAYS\x10\xaa\x02\x12\x0f\n\nLOW_20DAYS\x10\xab\x02\x12\r\n\x08HIGH_HIS\x10\xac\x02\x12\x0c\n\x07LOW_HIS\x10\xad\x02\x12\x0f\n\nZAF_90DAYS\x10\xae\x02\x12\x10\n\x0bZAF_180DAYS\x10\xaf\x02\x12\x10\n\x0bZAF_365DAYS\x10\xb0\x02\x12\x10\n\x0bMONEY_BIGIN\x10\xb1\x02\x12\x11\n\x0cMONEY_BIGOUT\x10\xb2\x02\x12\x12\n\rMONEY_PUREBIG\x10\xb3\x02\x12\x10\n\x0bMONEY_MIDIN\x10\xb4\x02\x12\x11\n\x0cMONEY_MIDOUT\x10\xb5\x02\x12\x12\n\rMONEY_PUREMID\x10\xb6\x02\x12\x12\n\rMONEY_SMALLIN\x10\xb7\x02\x12\x13\n\x0eMONEY_SMALLOUT\x10\xb8\x02\x12\x14\n\x0fMONEY_PURESMALL\x10\xb9\x02\x12\r\n\x08\x44IE_DAYS\x10\xbb\x02\x12\x0e\n\tZB_STATE0\x10\xbc\x02\x12\x0e\n\tZB_STATE1\x10\xbd\x02\x12\x10\n\x0bJHJJ_SPTIME\x10\xbe\x02\x12\x0e\n\tJHJJ_JJDK\x10\xbf\x02\x12\x0f\n\nZTZY_PRICE\x10\xc0\x02\x12\x0c\n\x07ZTZY_ZF\x10\xc1\x02\x12\x10\n\x0bZTZY_5DAYZF\x10\xc2\x02\x12\x0e\n\tZTZY_TIME\x10\xc3\x02\x12\x0c\n\x07JRTC_TC\x10\xc4\x02\x12\x0e\n\tJRTC_TIME\x10\xc5\x02\x12\x0f\n\nJRTC_PRICE\x10\xc6\x02\x12\x11\n\x0cJRTC_ZYPRICE\x10\xc7\x02\x12\x11\n\x0cJRTC_ZSPRICE\x10\xc8\x02\x12\x0e\n\tDQCC_TIME\x10\xc9\x02\x12\x0f\n\nDQCC_PRICE\x10\xca\x02\x12\x0e\n\tDQCC_DQSY\x10\xcb\x02\x12\x11\n\x0c\x44QCC_ZYPRICE\x10\xcc\x02\x12\x11\n\x0c\x44QCC_ZSPRICE\x10\xcd\x02\x12\x10\n\x0b\x43LNG_INTIME\x10\xce\x02\x12\x0f\n\nCLNG_PRICE\x10\xcf\x02\x12\x11\n\x0c\x43LNG_OUTTIME\x10\xd0\x02\x12\x12\n\rCLNG_OUTPRICE\x10\xd1\x02\x12\r\n\x08\x43LNG_ZAF\x10\xd2\x02\x12\x0f\n\nCLNG_INZAF\x10\xd3\x02\x12\x0b\n\x06IK_ZAF\x10\xd4\x02\x12\r\n\x08\x43XQN_LBS\x10\xd5\x02\x12\x0e\n\tCXQN_LJZF\x10\xd6\x02\x12\x0e\n\tCXQN_SSJG\x10\xd7\x02\x12\x11\n\x0c\x42IGDATA_GPDM\x10\xd8\x02\x12\x0e\n\tZB_STATE2\x10\xd9\x02\x12\x0e\n\tZB_STATE3\x10\xda\x02\x12\x0e\n\tZB_STATE4\x10\xdb\x02\x12\x0e\n\tJDTJ_QSPJ\x10\xe0\x02\x12\r\n\x08JDTJ_KPJ\x10\xe1\x02\x12\r\n\x08JDTJ_ZGJ\x10\xe2\x02\x12\r\n\x08JDTJ_ZDJ\x10\xe3\x02\x12\r\n\x08JDTJ_SPJ\x10\xe4\x02\x12\r\n\x08JDTJ_CJL\x10\xe5\x02\x12\r\n\x08JDTJ_CJE\x10\xe6\x02\x12\x0e\n\tJDTJ_JQJJ\x10\xe7\x02\x12\x0e\n\tJDTJ_QJZF\x10\xe8\x02\x12\x12\n\rJDTJ_QJZHENGF\x10\xe9\x02\x12\x0f\n\nJDTJ_QJHSL\x10\xea\x02\x12\x10\n\x0bJDTJ_QJLBFD\x10\xeb\x02\x12\x0e\n\tLevelGain\x10\xed\x02\x12\x0e\n\tLevelGrow\x10\xf0\x02\x12\x0e\n\tLevelSafe\x10\xf1\x02\x12\x0e\n\tLevelMain\x10\xf2\x02\x12\x0e\n\tLevelCost\x10\xf3\x02\x12\x0f\n\nLevelBonus\x10\xf4\x02\x12\r\n\x08PJ_SHORT\x10\x83\x03\x12\x0e\n\tPJ_MIDDLE\x10\x84\x03\x12\x0b\n\x06PJ_ALL\x10\x86\x03\x12\x10\n\x0b\x42LOCK_ZTNUM\x10\x87\x03\x12\x10\n\x0b\x42LOCK_DTNUM\x10\x88\x03\x12\x0f\n\nZAF_10DAYS\x10\x89\x03\x12\x11\n\x0c\x45NGLISH_NAME\x10\x8a\x03\x12\x11\n\x0c\x45NGLISH_ABBR\x10\x8b\x03\x12\r\n\x08STK_NKEY\x10\x8c\x03\x12\n\n\x05J_SJL\x10\x8d\x03\x12\x0f\n\nZAF_60DAYS\x10\x8e\x03\x12\r\n\x08SSHYNAME\x10\x8f\x03\x12\x0b\n\x06OPEN_P\x10\x90\x03\x12\x0b\n\x06HIGH_P\x10\x91\x03\x12\n\n\x05LOW_P\x10\x92\x03\x12\x0b\n\x06ZF_New\x10\x93\x03\x12\r\n\x08UNDERTOW\x10\x94\x03\x12\x0f\n\nATTACKWAVE\x10\x95\x03\x12\x0e\n\tZXG_PRICE\x10\x96\x03\x12\x0f\n\nZXG_PROFIT\x10\x97\x03\x12\x0f\n\nCLOSE_LAST\x10\xa0\x03\x12\x12\n\rMONEYJLR_5DAY\x10\xa4\x03\x12\x13\n\x0eMONEYJLR_20DAY\x10\xa5\x03\x12\x13\n\x0eMONEYJLR_60DAY\x10\xa6\x03\x12\x13\n\x0eMONEY_5DAYRATE\x10\xa7\x03\x12\x14\n\x0fMONEY_20DAYRATE\x10\xa8\x03\x12\x14\n\x0fMONEY_60DAYRATE\x10\xa9\x03\x12\x12\n\rMONEYJLR_3DAY\x10\xaa\x03\x12\x13\n\x0eMONEY_3DAYRATE\x10\xab\x03\x12\x0f\n\nVOLJLR_DAY\x10\xac\x03\x12\x10\n\x0bVOLJLR_3DAY\x10\xad\x03\x12\x10\n\x0bVOLJLR_5DAY\x10\xae\x03\x12\x11\n\x0cVOLJLR_20DAY\x10\xaf\x03\x12\x11\n\x0cVOLJLR_60DAY\x10\xb0\x03\x12\x13\n\x0eMONEYJLR_10DAY\x10\xb1\x03\x12\x10\n\x0bZAF_CURYEAR\x10\xb2\x03\x12\r\n\x08SSDYNAME\x10\xb3\x03\x12\x16\n\x11MONEY_PUREIN_3DAY\x10\xc2\x03\x12\x15\n\x10MONEY_SUPER_3DAY\x10\xc3\x03\x12\x13\n\x0eMONEY_BIG_3DAY\x10\xc4\x03\x12\x13\n\x0eMONEY_MID_3DAY\x10\xc5\x03\x12\x15\n\x10MONEY_SMALL_3DAY\x10\xc6\x03\x12\x16\n\x11MONEY_PUREIN_5DAY\x10\xc7\x03\x12\x15\n\x10MONEY_SUPER_5DAY\x10\xc8\x03\x12\x13\n\x0eMONEY_BIG_5DAY\x10\xc9\x03\x12\x13\n\x0eMONEY_MID_5DAY\x10\xca\x03\x12\x15\n\x10MONEY_SMALL_5DAY\x10\xcb\x03\x12\x17\n\x12MONEY_PUREIN_10DAY\x10\xcc\x03\x12\x16\n\x11MONEY_SUPER_10DAY\x10\xcd\x03\x12\x14\n\x0fMONEY_BIG_10DAY\x10\xce\x03\x12\x14\n\x0fMONEY_MID_10DAY\x10\xcf\x03\x12\x16\n\x11MONEY_SMALL_10DAY\x10\xd0\x03\x12\x19\n\x14MONEY_SHPUERIN_10DAY\x10\xd1\x03\x12\x15\n\x10MONEY_RATE_10DAY\x10\xd2\x03\x12\x10\n\x0bMONEY_ZL_ZC\x10\xd3\x03\x12\x15\n\x10MONEY_ZL_ZC_3DAY\x10\xd4\x03\x12\x15\n\x10MONEY_ZL_ZC_5DAY\x10\xd5\x03\x12\x16\n\x11MONEY_ZL_ZC_10DAY\x10\xd6\x03\x12\x10\n\x0bMONEY_DK_ZC\x10\xd7\x03\x12\x15\n\x10MONEY_DK_ZC_3DAY\x10\xd8\x03\x12\x15\n\x10MONEY_DK_ZC_5DAY\x10\xd9\x03\x12\x16\n\x11MONEY_DK_ZC_10DAY\x10\xda\x03\x12\x10\n\x0bMONEY_SH_ZC\x10\xdb\x03\x12\x15\n\x10MONEY_SH_ZC_3DAY\x10\xdc\x03\x12\x15\n\x10MONEY_SH_ZC_5DAY\x10\xdd\x03\x12\x16\n\x11MONEY_SH_ZC_10DAY\x10\xde\x03\x12\x16\n\x11MONEY_SUPER_PURIN\x10\xdf\x03\x12\x14\n\x0fMONEY_BIG_PURIN\x10\xe0\x03\x12\x0e\n\tNDAYS_NZT\x10\xe1\x03\x12\x0e\n\tKZZ_ZGZAF\x10\xe2\x03\x12\x10\n\x0bKZZ_ZGPRICE\x10\xe3\x03\x12\x0f\n\nKZZ_ZGNKEY\x10\xe4\x03\x12\x10\n\x0b\x42LOCK_JQZAF\x10\xe5\x03\x12\r\n\x08KZZ_ZGJZ\x10\xe6\x03\x12\x0f\n\nEXDATA_FDE\x10\xb3\x04\x12\x10\n\x0b\x42LOCK_SCORE\x10\xbc\x05*\xc4\x03\n\x10\x65num_factor_type\x12\x1a\n\x16\x45NUM_FACTOR_TYPE_START\x10\x00\x12!\n\x1d\x45NUM_FACTOR_TYPE_INDEX_AMOUNT\x10\x01\x12\x1e\n\x1a\x45NUM_FACTOR_TYPE_ZT_COUNTS\x10\x02\x12\x1e\n\x1a\x45NUM_FACTOR_TYPE_LB_COUNTS\x10\x03\x12\x1b\n\x17\x45NUM_FACTOR_TYPE_GREEN5\x10\x04\x12\x1a\n\x16\x45NUM_FACTOR_TYPE_KQXYB\x10\x05\x12\x1b\n\x17\x45NUM_FACTOR_TYPE_SB_RED\x10\x06\x12\x19\n\x15\x45NUM_FACTOR_TYPE_JRDM\x10\x07\x12\x1b\n\x17\x45NUM_FACTOR_TYPE_LB_RED\x10\x08\x12\x1e\n\x1a\x45NUM_FACTOR_TYPE_LB_PERCET\x10\t\x12\x1a\n\x16\x45NUM_FACTOR_TYPE_LB_DM\x10\n\x12\x1d\n\x19\x45NUM_FACTOR_TYPE_LB_GREEN\x10\x0b\x12#\n\x1f\x45NUM_FACTOR_TYPE_ZT_NOST_COUNTS\x10\x0c\x12#\n\x1f\x45NUM_FACTOR_TYPE_DT_NOST_COUNTS\x10\r*\xdc\x01\n\nPeriod_idx\x12\x0f\n\x0bPERIOD_MIN5\x10\x00\x12\x10\n\x0cPERIOD_MIN15\x10\x01\x12\x10\n\x0cPERIOD_MIN30\x10\x02\x12\x0f\n\x0bPERIOD_HOUR\x10\x03\x12\x0e\n\nPERIOD_DAY\x10\x04\x12\x0f\n\x0bPERIOD_WEEK\x10\x05\x12\x10\n\x0cPERIOD_MONTH\x10\x06\x12\x0f\n\x0bPERIOD_MIN1\x10\x07\x12\x0f\n\x0bPERIOD_MINN\x10\x08\x12\x0f\n\x0bPERIOD_DAYN\x10\t\x12\x11\n\rPERIOD_SEASON\x10\n\x12\x0f\n\x0bPERIOD_YEAR\x10\x0b*\xa4\x01\n\x10RECAL_FORMU_TYPE\x12\n\n\x06P_F_ZB\x10\x00\x12\n\n\x06P_F_TJ\x10\x01\x12\n\n\x06P_F_JY\x10\x02\x12\n\n\x06P_F_WC\x10\x03\x12\n\n\x06P_F_FC\x10\x04\x12\n\n\x06P_F_VB\x10\x05\x12\x0b\n\x07P_F_BIN\x10\x06\x12\n\n\x06P_F_AL\x10\x07\x12\x13\n\x0fP_SEPCLINE_TYPE\x10\x08\x12\x0b\n\x07P_F_BZB\x10\t\x12\r\n\tP_F_COMBO\x10\nb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'JCLBean_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_REPORT_REQNO']._serialized_start=60014
  _globals['_REPORT_REQNO']._serialized_end=64281
  _globals['_DOMAIN_IDX']._serialized_start=64284
  _globals['_DOMAIN_IDX']._serialized_end=66927
  _globals['_LBSBDB_TYPE']._serialized_start=66929
  _globals['_LBSBDB_TYPE']._serialized_end=66975
  _globals['_COLTYPE_IDX']._serialized_start=66978
  _globals['_COLTYPE_IDX']._serialized_end=73731
  _globals['_ENUM_FACTOR_TYPE']._serialized_start=73734
  _globals['_ENUM_FACTOR_TYPE']._serialized_end=74186
  _globals['_PERIOD_IDX']._serialized_start=74189
  _globals['_PERIOD_IDX']._serialized_end=74409
  _globals['_RECAL_FORMU_TYPE']._serialized_start=74412
  _globals['_RECAL_FORMU_TYPE']._serialized_end=74576
  _globals['_REPORT_PACKAGE']._serialized_start=63
  _globals['_REPORT_PACKAGE']._serialized_end=198
  _globals['_TAGREPORTPACKAGEARRAY']._serialized_start=200
  _globals['_TAGREPORTPACKAGEARRAY']._serialized_end=277
  _globals['_TAGINT32']._serialized_start=279
  _globals['_TAGINT32']._serialized_end=300
  _globals['_TAGINT64']._serialized_start=302
  _globals['_TAGINT64']._serialized_end=323
  _globals['_TAGFLOAT']._serialized_start=325
  _globals['_TAGFLOAT']._serialized_end=346
  _globals['_TAGDOUBLE']._serialized_start=348
  _globals['_TAGDOUBLE']._serialized_end=370
  _globals['_TAGINT32ARRAY']._serialized_start=372
  _globals['_TAGINT32ARRAY']._serialized_end=398
  _globals['_TAGINT64ARRAY']._serialized_start=400
  _globals['_TAGINT64ARRAY']._serialized_end=426
  _globals['_TAGFLOATARRAY']._serialized_start=428
  _globals['_TAGFLOATARRAY']._serialized_end=454
  _globals['_TAGDOUBLEARRAY']._serialized_start=456
  _globals['_TAGDOUBLEARRAY']._serialized_end=483
  _globals['_TAGSTRING']._serialized_start=485
  _globals['_TAGSTRING']._serialized_end=507
  _globals['_TAGCODEWITHNKEY']._serialized_start=509
  _globals['_TAGCODEWITHNKEY']._serialized_end=571
  _globals['_TAGCODEWITHNKEYARRAY']._serialized_start=573
  _globals['_TAGCODEWITHNKEYARRAY']._serialized_end=644
  _globals['_BSQUEUE']._serialized_start=647
  _globals['_BSQUEUE']._serialized_end=781
  _globals['_NEWBROKER']._serialized_start=783
  _globals['_NEWBROKER']._serialized_end=823
  _globals['_NEWMDHKBROKERQUEUE']._serialized_start=825
  _globals['_NEWMDHKBROKERQUEUE']._serialized_end=932
  _globals['_CURRSTOCKDATABEX']._serialized_start=935
  _globals['_CURRSTOCKDATABEX']._serialized_end=2411
  _globals['_PRICEVOLTABLE']._serialized_start=2413
  _globals['_PRICEVOLTABLE']._serialized_end=2507
  _globals['_MINUTEINFO']._serialized_start=2510
  _globals['_MINUTEINFO']._serialized_end=2855
  _globals['_ANALYDATAB']._serialized_start=2858
  _globals['_ANALYDATAB']._serialized_end=3068
  _globals['_PKDATA']._serialized_start=3070
  _globals['_PKDATA']._serialized_end=3125
  _globals['_TICKINFO']._serialized_start=3128
  _globals['_TICKINFO']._serialized_end=3289
  _globals['_CDPINFO']._serialized_start=3292
  _globals['_CDPINFO']._serialized_end=3424
  _globals['_ERROR_ANS']._serialized_start=3426
  _globals['_ERROR_ANS']._serialized_end=3472
  _globals['_MONITORINFO_ANS']._serialized_start=3475
  _globals['_MONITORINFO_ANS']._serialized_end=4052
  _globals['_CQCXCWINFO']._serialized_start=4054
  _globals['_CQCXCWINFO']._serialized_end=4153
  _globals['_CWINFO']._serialized_start=4155
  _globals['_CWINFO']._serialized_end=4243
  _globals['_BASECWINFO']._serialized_start=4246
  _globals['_BASECWINFO']._serialized_end=4985
  _globals['_AUTOGBBQ_REQ']._serialized_start=4987
  _globals['_AUTOGBBQ_REQ']._serialized_end=5051
  _globals['_AUTOGBBQ_ANS']._serialized_start=5053
  _globals['_AUTOGBBQ_ANS']._serialized_end=5111
  _globals['_AUTOBASE_REQ']._serialized_start=5113
  _globals['_AUTOBASE_REQ']._serialized_end=5177
  _globals['_AUTOBASE_ANS']._serialized_start=5179
  _globals['_AUTOBASE_ANS']._serialized_end=5238
  _globals['_HOSTMORE_REQ']._serialized_start=5240
  _globals['_HOSTMORE_REQ']._serialized_end=5288
  _globals['_HOSTMORE_INFO']._serialized_start=5291
  _globals['_HOSTMORE_INFO']._serialized_end=5825
  _globals['_HOSTMORE_ANS']._serialized_start=5827
  _globals['_HOSTMORE_ANS']._serialized_end=5889
  _globals['_SUB_UNSUB_HQ_REQ']._serialized_start=5891
  _globals['_SUB_UNSUB_HQ_REQ']._serialized_end=5923
  _globals['_UNSUB_HQ_ANS']._serialized_start=5925
  _globals['_UNSUB_HQ_ANS']._serialized_end=5955
  _globals['_STR_UNSUB_HQ_REQ']._serialized_start=5957
  _globals['_STR_UNSUB_HQ_REQ']._serialized_end=5989
  _globals['_STKNUM_REQ']._serialized_start=5991
  _globals['_STKNUM_REQ']._serialized_end=6032
  _globals['_STKNUM_ANS']._serialized_start=6034
  _globals['_STKNUM_ANS']._serialized_end=6062
  _globals['_STKINFONEW']._serialized_start=6065
  _globals['_STKINFONEW']._serialized_end=6385
  _globals['_CODE_REQ']._serialized_start=6387
  _globals['_CODE_REQ']._serialized_end=6431
  _globals['_CODE_ANS']._serialized_start=6433
  _globals['_CODE_ANS']._serialized_end=6487
  _globals['_ZHSORT_ITEM']._serialized_start=6489
  _globals['_ZHSORT_ITEM']._serialized_end=6564
  _globals['_ZHSORT_REQ']._serialized_start=6566
  _globals['_ZHSORT_REQ']._serialized_end=6607
  _globals['_ZHSORT_ANS']._serialized_start=6609
  _globals['_ZHSORT_ANS']._serialized_end=6668
  _globals['_FJB_REQ']._serialized_start=6670
  _globals['_FJB_REQ']._serialized_end=6728
  _globals['_FJB_ANS']._serialized_start=6730
  _globals['_FJB_ANS']._serialized_end=6833
  _globals['_ANALY_RANGE_REQ']._serialized_start=6835
  _globals['_ANALY_RANGE_REQ']._serialized_end=6947
  _globals['_ANALY_RANGE_ANS']._serialized_start=6949
  _globals['_ANALY_RANGE_ANS']._serialized_end=7072
  _globals['_ANALY_OFFSET_REQ']._serialized_start=7075
  _globals['_ANALY_OFFSET_REQ']._serialized_end=7203
  _globals['_ANALY_OFFSET_ANS']._serialized_start=7205
  _globals['_ANALY_OFFSET_ANS']._serialized_end=7279
  _globals['_MINUTE_REQ']._serialized_start=7281
  _globals['_MINUTE_REQ']._serialized_end=7356
  _globals['_MINUTE_ANS']._serialized_start=7359
  _globals['_MINUTE_ANS']._serialized_end=7495
  _globals['_ANALY_TQ_REQ']._serialized_start=7497
  _globals['_ANALY_TQ_REQ']._serialized_end=7621
  _globals['_ANALY_TQ_ANS']._serialized_start=7623
  _globals['_ANALY_TQ_ANS']._serialized_end=7706
  _globals['_SORT_HQ_REQ']._serialized_start=7708
  _globals['_SORT_HQ_REQ']._serialized_end=7809
  _globals['_MULTI_HQ_REQ']._serialized_start=7811
  _globals['_MULTI_HQ_REQ']._serialized_end=7875
  _globals['_MULTI_HQ_ANS']._serialized_start=7877
  _globals['_MULTI_HQ_ANS']._serialized_end=7940
  _globals['_MULTI_EX_HQ_REQ']._serialized_start=7942
  _globals['_MULTI_EX_HQ_REQ']._serialized_end=8040
  _globals['_MULTI_EX_HQ_ANS']._serialized_start=8043
  _globals['_MULTI_EX_HQ_ANS']._serialized_end=8172
  _globals['_FULL_MMP_REQ']._serialized_start=8174
  _globals['_FULL_MMP_REQ']._serialized_end=8237
  _globals['_FULL_MMP_ANS']._serialized_start=8239
  _globals['_FULL_MMP_ANS']._serialized_end=8340
  _globals['_LV2TICK_REQ']._serialized_start=8342
  _globals['_LV2TICK_REQ']._serialized_end=8433
  _globals['_LV2TICK_ANS']._serialized_start=8435
  _globals['_LV2TICK_ANS']._serialized_end=8504
  _globals['_SORT_CODE_REQ']._serialized_start=8506
  _globals['_SORT_CODE_REQ']._serialized_end=8575
  _globals['_SORT_CODE_ANS']._serialized_start=8577
  _globals['_SORT_CODE_ANS']._serialized_end=8607
  _globals['_MMP_TICK_REQ']._serialized_start=8609
  _globals['_MMP_TICK_REQ']._serialized_end=8699
  _globals['_MMP_TICK_ANS']._serialized_start=8701
  _globals['_MMP_TICK_ANS']._serialized_end=8746
  _globals['_BLOCK_SORT_HQ_REQ']._serialized_start=8749
  _globals['_BLOCK_SORT_HQ_REQ']._serialized_end=8921
  _globals['_BLOCK_SORT_HQ_ANS']._serialized_start=8924
  _globals['_BLOCK_SORT_HQ_ANS']._serialized_end=9054
  _globals['_SORT_EX_HQ_REQ']._serialized_start=9057
  _globals['_SORT_EX_HQ_REQ']._serialized_end=9194
  _globals['_HQ_WITH_FIELDS']._serialized_start=9196
  _globals['_HQ_WITH_FIELDS']._serialized_end=9298
  _globals['_SORT_EX_HQ_ANS']._serialized_start=9301
  _globals['_SORT_EX_HQ_ANS']._serialized_end=9477
  _globals['_SORT_BLOCK_HQ_REQ']._serialized_start=9480
  _globals['_SORT_BLOCK_HQ_REQ']._serialized_end=9635
  _globals['_SORT_CONBLOCK_REQ']._serialized_start=9637
  _globals['_SORT_CONBLOCK_REQ']._serialized_end=9740
  _globals['_SORT_LBSBDB_HQ_REQ']._serialized_start=9742
  _globals['_SORT_LBSBDB_HQ_REQ']._serialized_end=9863
  _globals['_SORT_LBSBDB_HQ_ANS']._serialized_start=9866
  _globals['_SORT_LBSBDB_HQ_ANS']._serialized_end=10000
  _globals['_STOCK_BLOCK_FIELDS']._serialized_start=10003
  _globals['_STOCK_BLOCK_FIELDS']._serialized_end=10146
  _globals['_SORT_STOCK_BLOCK_ANS']._serialized_start=10148
  _globals['_SORT_STOCK_BLOCK_ANS']._serialized_end=10224
  _globals['_SORT_SHQ_EX_REQ']._serialized_start=10226
  _globals['_SORT_SHQ_EX_REQ']._serialized_end=10291
  _globals['_SORT_NEW_SHQ_EX_REQ']._serialized_start=10293
  _globals['_SORT_NEW_SHQ_EX_REQ']._serialized_end=10379
  _globals['_SORT_ZDY_REQ']._serialized_start=10382
  _globals['_SORT_ZDY_REQ']._serialized_end=10530
  _globals['_HIS_MINUTE_REQ']._serialized_start=10532
  _globals['_HIS_MINUTE_REQ']._serialized_end=10611
  _globals['_HIS_MINUTE_ANS']._serialized_start=10613
  _globals['_HIS_MINUTE_ANS']._serialized_end=10705
  _globals['_TICK_REQ']._serialized_start=10707
  _globals['_TICK_REQ']._serialized_end=10810
  _globals['_TICK_ANS']._serialized_start=10812
  _globals['_TICK_ANS']._serialized_end=10879
  _globals['_HIS_TICK_REQ']._serialized_start=10881
  _globals['_HIS_TICK_REQ']._serialized_end=11003
  _globals['_HIS_TICK_ANS']._serialized_start=11005
  _globals['_HIS_TICK_ANS']._serialized_end=11091
  _globals['_GSRESULT']._serialized_start=11093
  _globals['_GSRESULT']._serialized_end=11150
  _globals['_CLOUDCALCRESULT']._serialized_start=11153
  _globals['_CLOUDCALCRESULT']._serialized_end=11286
  _globals['_CLOUD_CALC_SORT_REQ']._serialized_start=11288
  _globals['_CLOUD_CALC_SORT_REQ']._serialized_end=11323
  _globals['_CLOUD_CALC_SORT_ANS']._serialized_start=11326
  _globals['_CLOUD_CALC_SORT_ANS']._serialized_end=11456
  _globals['_AUCTIONDATA_REQ']._serialized_start=11458
  _globals['_AUCTIONDATA_REQ']._serialized_end=11538
  _globals['_AUCTIONDATA_ANS']._serialized_start=11540
  _globals['_AUCTIONDATA_ANS']._serialized_end=11614
  _globals['_AFTER_TRADE_REQ']._serialized_start=11616
  _globals['_AFTER_TRADE_REQ']._serialized_end=11682
  _globals['_AFTER_TRADE_ANS']._serialized_start=11684
  _globals['_AFTER_TRADE_ANS']._serialized_end=11751
  _globals['_JSON_REQ_ANS']._serialized_start=11753
  _globals['_JSON_REQ_ANS']._serialized_end=11781
  _globals['_STRATEGYCENTERLIST_REQ']._serialized_start=11784
  _globals['_STRATEGYCENTERLIST_REQ']._serialized_end=11985
  _globals['_STRATEGYMMONEKEY_REQ']._serialized_start=11987
  _globals['_STRATEGYMMONEKEY_REQ']._serialized_end=12040
  _globals['_STRATEGYGS_RESULT']._serialized_start=12042
  _globals['_STRATEGYGS_RESULT']._serialized_end=12091
  _globals['_STRATEGYGS_ANSINFO']._serialized_start=12093
  _globals['_STRATEGYGS_ANSINFO']._serialized_end=12165
  _globals['_CALCSTATICS_AGFD_ANSINFO']._serialized_start=12167
  _globals['_CALCSTATICS_AGFD_ANSINFO']._serialized_end=12259
  _globals['_STRATEGYBS_LIST_ANSINFO']._serialized_start=12262
  _globals['_STRATEGYBS_LIST_ANSINFO']._serialized_end=12476
  _globals['_STRATEGYONEKEY_RESULT']._serialized_start=12479
  _globals['_STRATEGYONEKEY_RESULT']._serialized_end=12622
  _globals['_STRATEGYIMG']._serialized_start=12624
  _globals['_STRATEGYIMG']._serialized_end=12667
  _globals['_STRATEGYONEKEY_PROFIT']._serialized_start=12669
  _globals['_STRATEGYONEKEY_PROFIT']._serialized_end=12722
  _globals['_STRATEGYBS_PROFIT']._serialized_start=12724
  _globals['_STRATEGYBS_PROFIT']._serialized_end=12791
  _globals['_STRATEGYBS_RESULT']._serialized_start=12793
  _globals['_STRATEGYBS_RESULT']._serialized_end=12862
  _globals['_STRATEGYBS_XG_DATA']._serialized_start=12865
  _globals['_STRATEGYBS_XG_DATA']._serialized_end=13094
  _globals['_STRATEGYPOOLCXQNJHJJZTQD_LIST']._serialized_start=13097
  _globals['_STRATEGYPOOLCXQNJHJJZTQD_LIST']._serialized_end=13301
  _globals['_STRATEGYPOOLCXQNJHJJZTQD_RESULT']._serialized_start=13304
  _globals['_STRATEGYPOOLCXQNJHJJZTQD_RESULT']._serialized_end=13454
  _globals['_STRATEGY_HISSIGNAL_DATA']._serialized_start=13456
  _globals['_STRATEGY_HISSIGNAL_DATA']._serialized_end=13520
  _globals['_STRATEGY_HISSIGNAL_ANS']._serialized_start=13523
  _globals['_STRATEGY_HISSIGNAL_ANS']._serialized_end=13656
  _globals['_STRATEGYMIXPOOL_REQ']._serialized_start=13658
  _globals['_STRATEGYMIXPOOL_REQ']._serialized_end=13729
  _globals['_STRATEGYMIXPOOL_DATA']._serialized_start=13732
  _globals['_STRATEGYMIXPOOL_DATA']._serialized_end=13991
  _globals['_STRATEGYANY_ANS']._serialized_start=13993
  _globals['_STRATEGYANY_ANS']._serialized_end=14048
  _globals['_STRATEGY_SGZH']._serialized_start=14050
  _globals['_STRATEGY_SGZH']._serialized_end=14139
  _globals['_JOBIMPORT_DATA']._serialized_start=14141
  _globals['_JOBIMPORT_DATA']._serialized_end=14188
  _globals['_INCOMELINE']._serialized_start=14190
  _globals['_INCOMELINE']._serialized_end=14307
  _globals['_INCOMESTATISTICS']._serialized_start=14310
  _globals['_INCOMESTATISTICS']._serialized_end=14472
  _globals['_STOCK_CALC_INFO']._serialized_start=14474
  _globals['_STOCK_CALC_INFO']._serialized_end=14553
  _globals['_STRATEGYREMARK']._serialized_start=14556
  _globals['_STRATEGYREMARK']._serialized_end=14826
  _globals['_STRATEGYSCORE']._serialized_start=14828
  _globals['_STRATEGYSCORE']._serialized_end=14923
  _globals['_STOCKTRADEINFO']._serialized_start=14926
  _globals['_STOCKTRADEINFO']._serialized_end=15168
  _globals['_CHANGEPOSITION']._serialized_start=15171
  _globals['_CHANGEPOSITION']._serialized_end=15339
  _globals['_TRANSSTATISTICS']._serialized_start=15342
  _globals['_TRANSSTATISTICS']._serialized_end=15585
  _globals['_KCBDATA']._serialized_start=15588
  _globals['_KCBDATA']._serialized_end=15776
  _globals['_KCBDATAEX']._serialized_start=15779
  _globals['_KCBDATAEX']._serialized_end=15926
  _globals['_KCBDATAANS']._serialized_start=15928
  _globals['_KCBDATAANS']._serialized_end=15984
  _globals['_AUCTIONDATAB']._serialized_start=15986
  _globals['_AUCTIONDATAB']._serialized_end=16048
  _globals['_AUCTIONDATABEX']._serialized_start=16050
  _globals['_AUCTIONDATABEX']._serialized_end=16161
  _globals['_BINARY_DATA_ANS']._serialized_start=16163
  _globals['_BINARY_DATA_ANS']._serialized_end=16194
  _globals['_MONEYFLOW_REQ']._serialized_start=16196
  _globals['_MONEYFLOW_REQ']._serialized_end=16289
  _globals['_MONEYFLOW_DATA']._serialized_start=16292
  _globals['_MONEYFLOW_DATA']._serialized_end=16457
  _globals['_MONEYFLOW_ANS']._serialized_start=16460
  _globals['_MONEYFLOW_ANS']._serialized_end=16616
  _globals['_DXJL_DATA']._serialized_start=16619
  _globals['_DXJL_DATA']._serialized_end=16849
  _globals['_DXJL_PUSH']._serialized_start=16851
  _globals['_DXJL_PUSH']._serialized_end=16905
  _globals['_DXJL_REQ']._serialized_start=16907
  _globals['_DXJL_REQ']._serialized_end=16960
  _globals['_RSPDATA']._serialized_start=16962
  _globals['_RSPDATA']._serialized_end=17065
  _globals['_DXJL_ANS']._serialized_start=17067
  _globals['_DXJL_ANS']._serialized_end=17119
  _globals['_ZTFX_DATA']._serialized_start=17121
  _globals['_ZTFX_DATA']._serialized_end=17194
  _globals['_ZAFX_ANS']._serialized_start=17196
  _globals['_ZAFX_ANS']._serialized_end=17250
  _globals['_BKYD_TOP_DATA']._serialized_start=17252
  _globals['_BKYD_TOP_DATA']._serialized_end=17296
  _globals['_BKYD_SINGLE_DATA']._serialized_start=17299
  _globals['_BKYD_SINGLE_DATA']._serialized_end=17440
  _globals['_BKYD_ALL_ANS']._serialized_start=17442
  _globals['_BKYD_ALL_ANS']._serialized_end=17507
  _globals['_BKYD_DATE_REQ']._serialized_start=17509
  _globals['_BKYD_DATE_REQ']._serialized_end=17538
  _globals['_BKFB_STOCK']._serialized_start=17540
  _globals['_BKFB_STOCK']._serialized_end=17581
  _globals['_BKFB_AREA']._serialized_start=17583
  _globals['_BKFB_AREA']._serialized_end=17654
  _globals['_BKFB_ALL_ANS']._serialized_start=17656
  _globals['_BKFB_ALL_ANS']._serialized_end=17732
  _globals['_BKFB_NKEY_REQ']._serialized_start=17734
  _globals['_BKFB_NKEY_REQ']._serialized_end=17763
  _globals['_ZD_NUMBER_ANS']._serialized_start=17765
  _globals['_ZD_NUMBER_ANS']._serialized_end=17823
  _globals['_FINANCE_DOWN_REQ']._serialized_start=17825
  _globals['_FINANCE_DOWN_REQ']._serialized_end=17922
  _globals['_FINANCE_DOWN_DATA']._serialized_start=17925
  _globals['_FINANCE_DOWN_DATA']._serialized_end=20175
  _globals['_FINVALUE_DOWN_DATA']._serialized_start=20178
  _globals['_FINVALUE_DOWN_DATA']._serialized_end=35526
  _globals['_FINGPJY_DOWN_DATA']._serialized_start=35529
  _globals['_FINGPJY_DOWN_DATA']._serialized_end=36521
  _globals['_FINSCJY_DOWN_DATA']._serialized_start=36524
  _globals['_FINSCJY_DOWN_DATA']._serialized_end=37843
  _globals['_FINGPONEDATE_DOWN_DATA']._serialized_start=37846
  _globals['_FINGPONEDATE_DOWN_DATA']._serialized_end=38930
  _globals['_FINANCE_DOWN_ANS']._serialized_start=38932
  _globals['_FINANCE_DOWN_ANS']._serialized_end=39002
  _globals['_FINVALUE_DOWN_ANS']._serialized_start=39004
  _globals['_FINVALUE_DOWN_ANS']._serialized_end=39076
  _globals['_FINGPJY_DOWN_ANS']._serialized_start=39078
  _globals['_FINGPJY_DOWN_ANS']._serialized_end=39148
  _globals['_FINSCJY_DOWN_ANS']._serialized_start=39150
  _globals['_FINSCJY_DOWN_ANS']._serialized_end=39220
  _globals['_FINGPONEDATE_DOWN_ANS']._serialized_start=39222
  _globals['_FINGPONEDATE_DOWN_ANS']._serialized_end=39302
  _globals['_FINANCE_ZIP_REQ']._serialized_start=39304
  _globals['_FINANCE_ZIP_REQ']._serialized_end=39351
  _globals['_FINANCE_ZIP_DATA']._serialized_start=39353
  _globals['_FINANCE_ZIP_DATA']._serialized_end=39399
  _globals['_FINANCE_ZIP_ANS']._serialized_start=39401
  _globals['_FINANCE_ZIP_ANS']._serialized_end=39471
  _globals['_PZYD_ANS']._serialized_start=39473
  _globals['_PZYD_ANS']._serialized_end=39526
  _globals['_OLDNAME_DATA']._serialized_start=39528
  _globals['_OLDNAME_DATA']._serialized_end=39584
  _globals['_OLDNAME_ANS']._serialized_start=39586
  _globals['_OLDNAME_ANS']._serialized_end=39646
  _globals['_SNMONEYFLOW_REQ']._serialized_start=39648
  _globals['_SNMONEYFLOW_REQ']._serialized_end=39724
  _globals['_SNMONEY_REQ']._serialized_start=39726
  _globals['_SNMONEY_REQ']._serialized_end=39767
  _globals['_SNMONEYHOLD_REQ']._serialized_start=39769
  _globals['_SNMONEYHOLD_REQ']._serialized_end=39859
  _globals['_HSIZS_REQ']._serialized_start=39861
  _globals['_HSIZS_REQ']._serialized_end=39917
  _globals['_HSIZS_DATA']._serialized_start=39919
  _globals['_HSIZS_DATA']._serialized_end=39960
  _globals['_HSIZS_ANS']._serialized_start=39962
  _globals['_HSIZS_ANS']._serialized_end=40017
  _globals['_SNMONEYFLOW_DATA']._serialized_start=40019
  _globals['_SNMONEYFLOW_DATA']._serialized_end=40064
  _globals['_SNMONEYFLOW_ANS']._serialized_start=40066
  _globals['_SNMONEYFLOW_ANS']._serialized_end=40133
  _globals['_SNMONEYBASE_ANS']._serialized_start=40136
  _globals['_SNMONEYBASE_ANS']._serialized_end=40317
  _globals['_SNRANKINFO_DATA']._serialized_start=40319
  _globals['_SNRANKINFO_DATA']._serialized_end=40432
  _globals['_SNRANKINFO_ANS']._serialized_start=40434
  _globals['_SNRANKINFO_ANS']._serialized_end=40499
  _globals['_SNMONEYHOLD_DATA']._serialized_start=40501
  _globals['_SNMONEYHOLD_DATA']._serialized_end=40592
  _globals['_SNMONEYHOLD_ANS']._serialized_start=40594
  _globals['_SNMONEYHOLD_ANS']._serialized_end=40661
  _globals['_HQEXDATA_ANS']._serialized_start=40663
  _globals['_HQEXDATA_ANS']._serialized_end=40734
  _globals['_LTLBDATA_ANS']._serialized_start=40736
  _globals['_LTLBDATA_ANS']._serialized_end=40839
  _globals['_MINLTLB']._serialized_start=40841
  _globals['_MINLTLB']._serialized_end=40913
  _globals['_MINLTLB_ANS']._serialized_start=40915
  _globals['_MINLTLB_ANS']._serialized_end=40969
  _globals['_LTLBARR']._serialized_start=40971
  _globals['_LTLBARR']._serialized_end=41026
  _globals['_LTLBARR_ANS']._serialized_start=41028
  _globals['_LTLBARR_ANS']._serialized_end=41082
  _globals['_LTBJ_ANS']._serialized_start=41084
  _globals['_LTBJ_ANS']._serialized_end=41192
  _globals['_BDQR_REQ']._serialized_start=41194
  _globals['_BDQR_REQ']._serialized_end=41250
  _globals['_BDQR_DATA']._serialized_start=41252
  _globals['_BDQR_DATA']._serialized_end=41289
  _globals['_BDQR_ANS']._serialized_start=41291
  _globals['_BDQR_ANS']._serialized_end=41344
  _globals['_RECALCPARAMINFOEX']._serialized_start=41346
  _globals['_RECALCPARAMINFOEX']._serialized_end=41459
  _globals['_RFORMULARSELFINDEX']._serialized_start=41462
  _globals['_RFORMULARSELFINDEX']._serialized_end=41651
  _globals['_RFORMULARGROUPLIST']._serialized_start=41653
  _globals['_RFORMULARGROUPLIST']._serialized_end=41745
  _globals['_RFORMULARSELFINDEX_REQ']._serialized_start=41747
  _globals['_RFORMULARSELFINDEX_REQ']._serialized_end=41800
  _globals['_RFORMULARSET_REQ']._serialized_start=41802
  _globals['_RFORMULARSET_REQ']._serialized_end=41834
  _globals['_RFORMULARSET_ANS']._serialized_start=41836
  _globals['_RFORMULARSET_ANS']._serialized_end=41930
  _globals['_RECALCFOMULAR_REQ']._serialized_start=41933
  _globals['_RECALCFOMULAR_REQ']._serialized_end=42115
  _globals['_RECALCOUTVARINFO']._serialized_start=42118
  _globals['_RECALCOUTVARINFO']._serialized_end=42362
  _globals['_RECALCFOMULAR_ANS']._serialized_start=42365
  _globals['_RECALCFOMULAR_ANS']._serialized_end=42585
  _globals['_MRFP_HQINFO']._serialized_start=42588
  _globals['_MRFP_HQINFO']._serialized_end=42814
  _globals['_MRFP_HQINFO_ANS']._serialized_start=42816
  _globals['_MRFP_HQINFO_ANS']._serialized_end=42878
  _globals['_MRFP_AGTJ_ANS']._serialized_start=42880
  _globals['_MRFP_AGTJ_ANS']._serialized_end=43005
  _globals['_QSJG_SORT_REQ']._serialized_start=43007
  _globals['_QSJG_SORT_REQ']._serialized_end=43123
  _globals['_QSJG_STRATEGY']._serialized_start=43125
  _globals['_QSJG_STRATEGY']._serialized_end=43165
  _globals['_QSJG_STRATEGY_ANS']._serialized_start=43167
  _globals['_QSJG_STRATEGY_ANS']._serialized_end=43233
  _globals['__PLOYLINE']._serialized_start=43235
  _globals['__PLOYLINE']._serialized_end=43348
  _globals['__POLYLINE']._serialized_start=43350
  _globals['__POLYLINE']._serialized_end=43463
  _globals['__DRAWLINE']._serialized_start=43466
  _globals['__DRAWLINE']._serialized_end=43735
  _globals['__DRAWKLINE']._serialized_start=43738
  _globals['__DRAWKLINE']._serialized_end=43942
  _globals['__DRAWNUMBER']._serialized_start=43945
  _globals['__DRAWNUMBER']._serialized_end=44163
  _globals['__DRAWNUMBER_FIX']._serialized_start=44166
  _globals['__DRAWNUMBER_FIX']._serialized_end=44322
  _globals['__DRAWTEXTEX']._serialized_start=44324
  _globals['__DRAWTEXTEX']._serialized_end=44440
  _globals['__DRAWTEXT']._serialized_start=44442
  _globals['__DRAWTEXT']._serialized_end=44569
  _globals['__DRAWTEXT_FIX']._serialized_start=44571
  _globals['__DRAWTEXT_FIX']._serialized_end=44689
  _globals['__DRAWBMP']._serialized_start=44692
  _globals['__DRAWBMP']._serialized_end=44821
  _globals['__DRAWBAND']._serialized_start=44824
  _globals['__DRAWBAND']._serialized_end=44963
  _globals['__DRAWICON']._serialized_start=44966
  _globals['__DRAWICON']._serialized_end=45173
  _globals['__DRAWTEXTABS']._serialized_start=45175
  _globals['__DRAWTEXTABS']._serialized_end=45225
  _globals['__DRAWTEXTREL']._serialized_start=45227
  _globals['__DRAWTEXTREL']._serialized_end=45277
  _globals['__FILLRGN']._serialized_start=45280
  _globals['__FILLRGN']._serialized_end=45492
  _globals['__FLOATRGN']._serialized_start=45495
  _globals['__FLOATRGN']._serialized_end=45708
  _globals['__FILLBACK']._serialized_start=45711
  _globals['__FILLBACK']._serialized_end=45910
  _globals['__FILLALLBACK']._serialized_start=45913
  _globals['__FILLALLBACK']._serialized_end=46075
  _globals['__PARTLINE']._serialized_start=46078
  _globals['__PARTLINE']._serialized_end=46240
  _globals['__DRAWGBK']._serialized_start=46243
  _globals['__DRAWGBK']._serialized_end=46530
  _globals['__DRAWSL']._serialized_start=46533
  _globals['__DRAWSL']._serialized_end=46790
  _globals['__STICKLINE']._serialized_start=46793
  _globals['__STICKLINE']._serialized_end=47056
  _globals['__STICKLINE3D']._serialized_start=47059
  _globals['__STICKLINE3D']._serialized_end=47324
  _globals['__STICKLINE3DEX']._serialized_start=47327
  _globals['__STICKLINE3DEX']._serialized_end=47609
  _globals['__DRAWOX']._serialized_start=47612
  _globals['__DRAWOX']._serialized_end=47821
  _globals['__STRIP']._serialized_start=47823
  _globals['__STRIP']._serialized_end=47872
  _globals['__DRAWRECTREL']._serialized_start=47874
  _globals['__DRAWRECTREL']._serialized_end=47961
  _globals['__EXPLAIN']._serialized_start=47963
  _globals['__EXPLAIN']._serialized_end=48040
  _globals['__EXPLAINEX']._serialized_start=48043
  _globals['__EXPLAINEX']._serialized_end=48275
  _globals['__EXPLAINICON']._serialized_start=48277
  _globals['__EXPLAINICON']._serialized_end=48392
  _globals['__DRAWFUNCS']._serialized_start=48395
  _globals['__DRAWFUNCS']._serialized_end=49943
  _globals['_GS_OUTVARINFO']._serialized_start=49946
  _globals['_GS_OUTVARINFO']._serialized_end=50327
  _globals['_GS_PARAM']._serialized_start=50329
  _globals['_GS_PARAM']._serialized_end=50368
  _globals['_GSCALC_REQ']._serialized_start=50371
  _globals['_GSCALC_REQ']._serialized_end=50537
  _globals['_GSCALC_ANS']._serialized_start=50539
  _globals['_GSCALC_ANS']._serialized_end=50600
  _globals['_CALCZAFDATA']._serialized_start=50603
  _globals['_CALCZAFDATA']._serialized_end=50959
  _globals['_CALCZAFDATA_REQ']._serialized_start=50961
  _globals['_CALCZAFDATA_REQ']._serialized_end=50995
  _globals['_CALCZAFDATA_ANS']._serialized_start=50997
  _globals['_CALCZAFDATA_ANS']._serialized_end=51059
  _globals['_CALCZDFAREAD_ANS']._serialized_start=51061
  _globals['_CALCZDFAREAD_ANS']._serialized_end=51097
  _globals['_CALCZDFRESULT_REQ']._serialized_start=51099
  _globals['_CALCZDFRESULT_REQ']._serialized_end=51131
  _globals['_CALCZDFRESULT_ANS']._serialized_start=51133
  _globals['_CALCZDFRESULT_ANS']._serialized_end=51201
  _globals['_ZDFPROFIT']._serialized_start=51203
  _globals['_ZDFPROFIT']._serialized_end=51247
  _globals['_CALCZDFPROFIT_ANS']._serialized_start=51249
  _globals['_CALCZDFPROFIT_ANS']._serialized_end=51310
  _globals['_ZDFZDT']._serialized_start=51312
  _globals['_ZDFZDT']._serialized_end=51371
  _globals['_CALCZDFZDTNUM_ANS']._serialized_start=51373
  _globals['_CALCZDFZDTNUM_ANS']._serialized_end=51431
  _globals['_CALCZDTDATA_REQ']._serialized_start=51433
  _globals['_CALCZDTDATA_REQ']._serialized_end=51464
  _globals['_CALCZDTDATA_ANS']._serialized_start=51466
  _globals['_CALCZDTDATA_ANS']._serialized_end=51532
  _globals['_CALCYESONELINECODE_ANS']._serialized_start=51534
  _globals['_CALCYESONELINECODE_ANS']._serialized_end=51607
  _globals['_HISCHIP_REQ']._serialized_start=51609
  _globals['_HISCHIP_REQ']._serialized_end=51686
  _globals['_HISCHIPDATA']._serialized_start=51689
  _globals['_HISCHIPDATA']._serialized_end=51918
  _globals['_HISCHIP_ANS']._serialized_start=51920
  _globals['_HISCHIP_ANS']._serialized_end=51979
  _globals['_MONEYRANK_DATA']._serialized_start=51981
  _globals['_MONEYRANK_DATA']._serialized_end=52042
  _globals['_MONEYRANK_ANS']._serialized_start=52044
  _globals['_MONEYRANK_ANS']._serialized_end=52107
  _globals['_LBDATA_ANS']._serialized_start=52109
  _globals['_LBDATA_ANS']._serialized_end=52185
  _globals['_LBDATADATA_ANS']._serialized_start=52187
  _globals['_LBDATADATA_ANS']._serialized_end=52247
  _globals['_FINANCEVALUE']._serialized_start=52249
  _globals['_FINANCEVALUE']._serialized_end=52330
  _globals['_FINANCEDATEVALUE']._serialized_start=52333
  _globals['_FINANCEDATEVALUE']._serialized_end=52667
  _globals['_FINANCE']._serialized_start=52669
  _globals['_FINANCE']._serialized_end=52743
  _globals['_STRATEGYSYSTEMPOOLRESULT']._serialized_start=52745
  _globals['_STRATEGYSYSTEMPOOLRESULT']._serialized_end=52805
  _globals['_STRATEGYSYSTEMPOOLREQ']._serialized_start=52807
  _globals['_STRATEGYSYSTEMPOOLREQ']._serialized_end=52847
  _globals['_STRATEGYSYSTEMPOOLLIST']._serialized_start=52849
  _globals['_STRATEGYSYSTEMPOOLLIST']._serialized_end=52931
  _globals['_FINANCENOW_REQ']._serialized_start=52933
  _globals['_FINANCENOW_REQ']._serialized_end=52998
  _globals['_FINANCENOW_ANS']._serialized_start=53000
  _globals['_FINANCENOW_ANS']._serialized_end=53057
  _globals['_NEWFINANCE_REQ']._serialized_start=53059
  _globals['_NEWFINANCE_REQ']._serialized_end=53142
  _globals['_NEWFINANCE_ANS']._serialized_start=53144
  _globals['_NEWFINANCE_ANS']._serialized_end=53201
  _globals['_STATICTISTRADENUM']._serialized_start=53203
  _globals['_STATICTISTRADENUM']._serialized_end=53282
  _globals['_STATICTISTRADENUM_ANS']._serialized_start=53284
  _globals['_STATICTISTRADENUM_ANS']._serialized_end=53358
  _globals['_STATICTISTRADENUM_REQ']._serialized_start=53361
  _globals['_STATICTISTRADENUM_REQ']._serialized_end=53494
  _globals['_MINUTE_NEW_REQ']._serialized_start=53496
  _globals['_MINUTE_NEW_REQ']._serialized_end=53589
  _globals['_MINUTE_NEW_ANS']._serialized_start=53591
  _globals['_MINUTE_NEW_ANS']._serialized_end=53701
  _globals['_KLINE_NEW_REQ']._serialized_start=53704
  _globals['_KLINE_NEW_REQ']._serialized_end=53859
  _globals['_KLINE_NEW_ANS']._serialized_start=53861
  _globals['_KLINE_NEW_ANS']._serialized_end=53966
  _globals['_MULTI_TODAY_MONEYFLOW_REQ']._serialized_start=53968
  _globals['_MULTI_TODAY_MONEYFLOW_REQ']._serialized_end=54010
  _globals['_NKEY_MONEYFLOW_DATA']._serialized_start=54013
  _globals['_NKEY_MONEYFLOW_DATA']._serialized_end=54206
  _globals['_MULTI_TODAY_MONEYFLOW_ANS']._serialized_start=54208
  _globals['_MULTI_TODAY_MONEYFLOW_ANS']._serialized_end=54288
  _globals['_CFGDATA']._serialized_start=54290
  _globals['_CFGDATA']._serialized_end=54377
  _globals['_BIGBLOCK']._serialized_start=54379
  _globals['_BIGBLOCK']._serialized_end=54444
  _globals['_BLOCKXMLDATA']._serialized_start=54446
  _globals['_BLOCKXMLDATA']._serialized_end=54524
  _globals['_ZSCFGXMLDATA']._serialized_start=54526
  _globals['_ZSCFGXMLDATA']._serialized_end=54601
  _globals['_STRATEGYONEKEY_COMMENTEX']._serialized_start=54603
  _globals['_STRATEGYONEKEY_COMMENTEX']._serialized_end=54694
  _globals['_CALC_ZDQJ_REQ']._serialized_start=54697
  _globals['_CALC_ZDQJ_REQ']._serialized_end=54829
  _globals['_CALC_ZDQJ_ANS']._serialized_start=54831
  _globals['_CALC_ZDQJ_ANS']._serialized_end=54910
  _globals['_STATICS_EXDATA_REQ']._serialized_start=54912
  _globals['_STATICS_EXDATA_REQ']._serialized_end=54979
  _globals['_STATICS_EXDATA_REQS']._serialized_start=54981
  _globals['_STATICS_EXDATA_REQS']._serialized_end=55054
  _globals['_STATICS_EXDATA']._serialized_start=55057
  _globals['_STATICS_EXDATA']._serialized_end=55286
  _globals['_STATICS_EXDATA_ANS']._serialized_start=55288
  _globals['_STATICS_EXDATA_ANS']._serialized_end=55370
  _globals['_STATICS_EXDATA_ANSS']._serialized_start=55372
  _globals['_STATICS_EXDATA_ANSS']._serialized_end=55444
  _globals['_MANY_MINUTE_REQ']._serialized_start=55446
  _globals['_MANY_MINUTE_REQ']._serialized_end=55477
  _globals['_MINUTE_DATA']._serialized_start=55479
  _globals['_MINUTE_DATA']._serialized_end=55552
  _globals['_MANY_MINUTE_ANS']._serialized_start=55554
  _globals['_MANY_MINUTE_ANS']._serialized_end=55616
  _globals['_BFDAY_DATA_REQ']._serialized_start=55618
  _globals['_BFDAY_DATA_REQ']._serialized_end=55665
  _globals['_BFDAY_DATA_ANS']._serialized_start=55667
  _globals['_BFDAY_DATA_ANS']._serialized_end=55740
  _globals['_RANKINFO']._serialized_start=55742
  _globals['_RANKINFO']._serialized_end=55780
  _globals['_RANKINFOARR']._serialized_start=55782
  _globals['_RANKINFOARR']._serialized_end=55872
  _globals['_RANKREQ']._serialized_start=55874
  _globals['_RANKREQ']._serialized_end=55918
  _globals['_SORT_RANKEX_REQ']._serialized_start=55921
  _globals['_SORT_RANKEX_REQ']._serialized_end=56112
  _globals['_SORT_RANKEX_ANS']._serialized_start=56115
  _globals['_SORT_RANKEX_ANS']._serialized_end=56302
  _globals['_KLINELABEL_REQ']._serialized_start=56304
  _globals['_KLINELABEL_REQ']._serialized_end=56385
  _globals['_KLINELABEL_ARR_REQ']._serialized_start=56387
  _globals['_KLINELABEL_ARR_REQ']._serialized_end=56472
  _globals['_KLINELABEL_DATA']._serialized_start=56474
  _globals['_KLINELABEL_DATA']._serialized_end=56535
  _globals['_KLINELABEL_ANS']._serialized_start=56537
  _globals['_KLINELABEL_ANS']._serialized_end=56602
  _globals['_KLINELABELWITHNKEY_DATA']._serialized_start=56604
  _globals['_KLINELABELWITHNKEY_DATA']._serialized_end=56690
  _globals['_KLINELABEL_ARR_ANS']._serialized_start=56692
  _globals['_KLINELABEL_ARR_ANS']._serialized_end=56772
  _globals['_LV2ORDERTICK']._serialized_start=56775
  _globals['_LV2ORDERTICK']._serialized_end=56909
  _globals['_LV2ORDERTICK_REQ']._serialized_start=56912
  _globals['_LV2ORDERTICK_REQ']._serialized_end=57052
  _globals['_LV2ORDERTICK_ANS']._serialized_start=57054
  _globals['_LV2ORDERTICK_ANS']._serialized_end=57132
  _globals['_LV2SIGORDERTICK_REQ']._serialized_start=57134
  _globals['_LV2SIGORDERTICK_REQ']._serialized_end=57249
  _globals['_LV2MULPK_REQ']._serialized_start=57251
  _globals['_LV2MULPK_REQ']._serialized_end=57314
  _globals['_LV2MULPK_DATA']._serialized_start=57316
  _globals['_LV2MULPK_DATA']._serialized_end=57398
  _globals['_LV2MULPK_ANS']._serialized_start=57401
  _globals['_LV2MULPK_ANS']._serialized_end=57532
  _globals['_LV2SIGPK_REQ']._serialized_start=57534
  _globals['_LV2SIGPK_REQ']._serialized_end=57624
  _globals['_LV2SIGPK_ANS']._serialized_start=57626
  _globals['_LV2SIGPK_ANS']._serialized_end=57694
  _globals['_LV2KCBPH_REQ']._serialized_start=57696
  _globals['_LV2KCBPH_REQ']._serialized_end=57759
  _globals['_MULSTK_ADAY_REQ']._serialized_start=57761
  _globals['_MULSTK_ADAY_REQ']._serialized_end=57825
  _globals['_MULSTK_ADAY_DATA']._serialized_start=57827
  _globals['_MULSTK_ADAY_DATA']._serialized_end=57902
  _globals['_MULSTK_ADAY_ANS']._serialized_start=57904
  _globals['_MULSTK_ADAY_ANS']._serialized_end=57971
  _globals['_SORT_FILTER_REQ']._serialized_start=57974
  _globals['_SORT_FILTER_REQ']._serialized_end=58225
  _globals['_BLOCK_SCORE_DATA']._serialized_start=58227
  _globals['_BLOCK_SCORE_DATA']._serialized_end=58309
  _globals['_BLOCK_SCORE_ANS']._serialized_start=58311
  _globals['_BLOCK_SCORE_ANS']._serialized_end=58378
  _globals['_JTJB_DATA']._serialized_start=58380
  _globals['_JTJB_DATA']._serialized_end=58419
  _globals['_JTJB_DATA_ANS']._serialized_start=58421
  _globals['_JTJB_DATA_ANS']._serialized_end=58479
  _globals['_FACTOR_DATA']._serialized_start=58481
  _globals['_FACTOR_DATA']._serialized_end=58523
  _globals['_STATICS_FACTOR_REQ']._serialized_start=58525
  _globals['_STATICS_FACTOR_REQ']._serialized_end=58559
  _globals['_STATICS_FACTOR_ANS']._serialized_start=58561
  _globals['_STATICS_FACTOR_ANS']._serialized_end=58626
  _globals['_LBDATA']._serialized_start=58628
  _globals['_LBDATA']._serialized_end=58670
  _globals['_HISLB_STOCK']._serialized_start=58672
  _globals['_HISLB_STOCK']._serialized_end=58748
  _globals['_HIS_LB_DATE_LINE']._serialized_start=58751
  _globals['_HIS_LB_DATE_LINE']._serialized_end=58888
  _globals['_HISLBDATA_REQ']._serialized_start=58890
  _globals['_HISLBDATA_REQ']._serialized_end=58941
  _globals['_HISLBDATA_ANS']._serialized_start=58943
  _globals['_HISLBDATA_ANS']._serialized_end=59017
  _globals['_STATIC_BLOCKS_ZT']._serialized_start=59019
  _globals['_STATIC_BLOCKS_ZT']._serialized_end=59084
  _globals['_STATICS_BLOCK_ZT_ANS']._serialized_start=59086
  _globals['_STATICS_BLOCK_ZT_ANS']._serialized_end=59158
  _globals['_HOT_BLOCK_SORT_REQ']._serialized_start=59160
  _globals['_HOT_BLOCK_SORT_REQ']._serialized_end=59267
  _globals['_HOT_BLOCK_SORT_ANS']._serialized_start=59269
  _globals['_HOT_BLOCK_SORT_ANS']._serialized_end=59387
  _globals['_JJYZB_STOCK']._serialized_start=59389
  _globals['_JJYZB_STOCK']._serialized_end=59465
  _globals['_JJYZB_STOCKS_ANS']._serialized_start=59467
  _globals['_JJYZB_STOCKS_ANS']._serialized_end=59532
  _globals['_LBSBDB_STOCKS']._serialized_start=59534
  _globals['_LBSBDB_STOCKS']._serialized_end=59577
  _globals['_STATICSDATA_LBDBSB_STOCKS_REQ']._serialized_start=59579
  _globals['_STATICSDATA_LBDBSB_STOCKS_REQ']._serialized_end=59624
  _globals['_STATICSDATA_LBDBSB_STOCKS_ANS']._serialized_start=59626
  _globals['_STATICSDATA_LBDBSB_STOCKS_ANS']._serialized_end=59706
  _globals['_STATICSDATA_ZTLB_STOCKINFO_REQ']._serialized_start=59708
  _globals['_STATICSDATA_ZTLB_STOCKINFO_REQ']._serialized_end=59789
  _globals['_STATICSDATA_ZTLB_STOCKINFO_ANS']._serialized_start=59791
  _globals['_STATICSDATA_ZTLB_STOCKINFO_ANS']._serialized_end=59903
  _globals['_RANK_DATA']._serialized_start=59905
  _globals['_RANK_DATA']._serialized_end=59944
  _globals['_RANK_ARR']._serialized_start=59946
  _globals['_RANK_ARR']._serialized_end=60011
# @@protoc_insertion_point(module_scope)
