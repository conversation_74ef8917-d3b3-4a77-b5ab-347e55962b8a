import pytest
import time
from common.assert_response import assert_round_response
from common.yaml_loader import load_test_params, load_test_config, get_param_ids


@pytest.mark.parametrize(
    "code, setcode, offset, num",
    load_test_params('test_money_day_23201_params.yaml'),
    ids=get_param_ids('test_money_day_23201_params.yaml')
)
class TestMoneyFlowDay:
    """测试资金流向日周期接口(23201)，校验响应时间和内容一致性"""

    def test_money_flow_day(self, api_helper, logger, code, setcode, offset, num):
        """
        测试资金流向日周期接口，比较两个接口响应内容的一致性

        Args:
            api_helper: 并发API调用助手
            logger: 日志记录器
            code: 股票代码
            setcode: 市场代码
            offset: 数据偏移量
            num: 返回数据条数
        """
        # 加载配置
        config = load_test_config('test_money_day_23201_params.yaml')
        test_fields = config['test_fields']
        test_config = config['test_config']

        # 生成测试用例ID
        case_id = f"{code}-{setcode}-{offset}-{num}"

        # 记录开始时间
        start_time = time.time()

        # 记录测试开始
        try:
            # 并发调用新旧接口
            response, new_response = api_helper.call_method_concurrently(
                test_config['method_name'],
                code=code, setcode=setcode, offset=offset, num=num
            )

            # 获取响应时间和服务器信息
            response_times = api_helper.get_last_response_times()
            server_info = api_helper.get_last_server_info()

            # 记录响应时间
            elapsed_time = time.time() - start_time

            # 执行响应验证和断言测试
            all_errors = self._validate_and_assert_response(
                response, new_response, test_fields, test_config,
                case_id, code, logger
            )

            # 创建请求参数字典
            params = {
                'code': code, 'setcode': setcode, 'offset': offset, 'num': num, 'case_id': case_id
            }

            # 使用新的日志记录规则
            logger.log_test_result(
                server_info=server_info, response_times=response_times,
                request_params=params, has_errors=bool(all_errors), error_details=all_errors
            )

            # 记录测试结果
            if all_errors:
                combined_error = f"测试用例 {case_id} 共发现 {len(all_errors)} 处断言失败:\n" + "\n".join(all_errors)
                raise AssertionError(combined_error)
            else:
                print(f"✅ 测试用例 {case_id} 通过")

        except Exception as e:
            if "跳过测试" not in str(e):  # 不记录跳过测试的错误
                logger.log_error(f"测试用例 {case_id} 执行失败: {str(e)}")
            raise

    def _validate_and_assert_response(self, response, new_response, test_fields, test_config,
                                    case_id, code, logger):
        """验证响应并执行断言测试"""
        # 检查API调用是否成功
        if response is None and new_response is None:
            logger.log_warning(f"测试用例 {case_id}: 新旧接口都返回None，可能股票代码 {code} 无数据或不存在")
            pytest.skip(f"测试用例 {case_id}: 股票代码 {code} 无资金流向日周期数据，跳过测试")
            return []

        if response is None:
            logger.log_error(f"测试用例 {case_id}: 旧接口返回None")
            pytest.fail(f"测试用例 {case_id}: 旧接口调用失败，返回None")

        if new_response is None:
            logger.log_error(f"测试用例 {case_id}: 新接口返回None")
            pytest.fail(f"测试用例 {case_id}: 新接口调用失败，返回None")

        # 检查响应对象是否有必要的属性
        required_attrs = test_config['required_attributes']
        for attr in required_attrs:
            if not hasattr(response, attr):
                logger.log_error(f"测试用例 {case_id}: 旧接口响应缺少属性: {attr}")
                pytest.fail(f"测试用例 {case_id}: 旧接口响应缺少必要属性: {attr}")
            if not hasattr(new_response, attr):
                logger.log_error(f"测试用例 {case_id}: 新接口响应缺少属性: {attr}")
                pytest.fail(f"测试用例 {case_id}: 新接口响应缺少必要属性: {attr}")

        # 执行数据断言
        return self._assert_data_sets(response, new_response, test_fields, test_config)

    def _assert_data_sets(self, response, new_response, test_fields, test_config):
        """断言三个数据集的内容"""
        all_errors = []

        # 校验 amt, vol, cnt 三个数据集
        for data_type in test_config['data_types']:
            response_data = getattr(response, data_type)
            new_response_data = getattr(new_response, data_type)

            for i, (resp, new_resp) in enumerate(zip(response_data, new_response_data)):
                try:
                    assert_round_response(
                        resp, new_resp,
                        test_fields['money_flow_fields'],
                        decimal_places=test_config['decimal_places']
                    )
                except AssertionError as e:
                    error_msg = f"{data_type} 数据第{i+1}条断言失败: {str(e)}"
                    all_errors.append(error_msg)

        return all_errors


