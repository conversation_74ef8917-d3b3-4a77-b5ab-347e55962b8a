import logging
import os
import inspect
from datetime import datetime


class LogManager:
    _instance = None
    _logged_files = set()
    _logged_error_files = set()

    def __new__(cls, *args, **kwargs):
        """单例模式"""
        if cls._instance is None:
            cls._instance = super(LogManager, cls).__new__(cls)
        return cls._instance

    def __init__(self, log_level=logging.INFO):
        """初始化日志管理器"""
        if not hasattr(self, '_initialized'):
            self.log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'logs')
            self.log_level = log_level
            self.logger = logging.getLogger(__name__)
            self.logger.setLevel(self.log_level)
            self.log_file = self._generate_log_file_name()
            self._set_up_handler()
            self.logger.propagate = False
            self._initialized = True
            self._test_stats = {}

    def _generate_log_file_name(self):
        """生成日志文件名"""
        current_time = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
        return os.path.join(self.log_dir, f"log-{current_time}.log")

    def _set_up_handler(self):
        """设置日志处理器"""
        file_handler = logging.FileHandler(self.log_file, mode='a', encoding='utf-8')
        file_handler.setLevel(self.log_level)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)

    def log_info(self, message):
        """记录INFO级别日志"""
        self.logger.info(message)
        for handler in self.logger.handlers:
            handler.flush()

    def log_warning(self, message):
        """记录WARNING级别日志"""
        self.logger.warning(message)

    def log_error(self, message):
        """记录ERROR级别日志"""
        self.logger.error(message)

    def _extract_interface_number(self, filename):
        """从测试文件名中提取接口编号"""
        import re
        match = re.search(r'_(\d+)\.py$', filename)
        if match:
            return match.group(1)
        return None



    def _log_test_file_name_once(self):
        """记录测试接口编号(INFO级别,只记录一次)"""
        try:
            # 使用更激进的方法直接从调用栈查找测试文件
            stack = inspect.stack()
            test_file_basename = None

            for frame_info in stack[:25]:  # 检查前25个栈帧
                filename = frame_info.filename
                basename = os.path.basename(filename)

                # 检查是否是tests_pz或tests_ph目录下的测试文件
                if (basename.startswith('test_') and
                    filename.endswith('.py') and
                    ('tests_pz' in filename or 'tests_ph' in filename) and
                    basename != 'log_handle.py'):  # 排除自身

                    test_file_basename = basename
                    break

            # 如果找到了测试文件，记录接口信息
            if test_file_basename and test_file_basename not in self._logged_files:
                self._logged_files.add(test_file_basename)
                # 直接记录完整的测试文件名
                self.log_info(f"📁测试接口: {test_file_basename}")
                # 初始化测试统计
                self._test_stats[test_file_basename] = {
                    'interface_number': test_file_basename,
                    'total_tests': 0,
                    'passed_tests': 0,
                    'failed_tests': 0
                }

        except Exception as e:
            # 如果获取文件名失败，不影响主要功能
            pass

    def _find_test_file_from_stack(self):
        """从调用栈中查找测试文件"""
        try:
            stack = inspect.stack()
            for frame_info in stack:
                filename = frame_info.filename
                basename = os.path.basename(filename)

                # 只记录tests目录下的以test_开头的Python文件
                if (filename.endswith('.py') and
                    basename.startswith('test_') and
                    ('tests_ph' in filename or 'tests_pz' in filename) and  # 必须在tests目录中
                    basename != 'log_handle.py'):  # 排除自身
                    return basename
        except:
            pass
        return None

    def _find_test_file_from_pytest(self):
        """从pytest环境中获取当前测试文件"""
        try:
            # 尝试从pytest的内部状态获取当前测试项
            import _pytest
            if hasattr(_pytest, '_current_item') and _pytest._current_item:
                test_file_path = str(_pytest._current_item.fspath)
                basename = os.path.basename(test_file_path)
                if (basename.startswith('test_') and
                   ('tests_ph' in test_file_path or 'tests_pz' in test_file_path)):
                    return basename
        except:
            pass

        try:
            # 尝试从pytest的request fixture获取
            import pytest
            if hasattr(pytest, '_current_request') and pytest._current_request:
                test_file_path = str(pytest._current_request.node.fspath)
                basename = os.path.basename(test_file_path)
                if (basename.startswith('test_') and
                   ('tests_ph' in test_file_path or 'tests_pz' in test_file_path)):
                    return basename
        except:
            pass

        return None

    def _update_test_stats(self, failed=False):
        """
        更新测试统计信息
        """
        try:
            # 获取当前测试文件
            test_file_basename = self._find_test_file_from_stack()
            if not test_file_basename:
                test_file_basename = self._find_test_file_from_pytest()

            # 如果还是没找到，尝试从调用栈获取
            if not test_file_basename:
                stack = inspect.stack()
                for frame_info in stack[:15]:
                    filename = frame_info.filename
                    basename = os.path.basename(filename)
                    if basename.startswith('test_') and filename.endswith('.py'):
                        test_file_basename = basename
                        break
                    elif ('tests_ph' in filename or 'tests_pz' in filename) and basename.startswith('test_'):
                        test_file_basename = basename
                        break

            if test_file_basename and test_file_basename in self._test_stats:
                stats = self._test_stats[test_file_basename]
                stats['total_tests'] += 1
                if failed:
                    stats['failed_tests'] += 1
                else:
                    stats['passed_tests'] += 1
        except Exception as e:
            # 如果更新失败，不影响主要功能
            pass

    def _log_test_file_name_error_once(self):
        """记录测试接口编号(ERROR级别,只记录一次)"""
        try:
            # 使用激进的方法直接从调用栈查找测试文件
            stack = inspect.stack()
            test_file_basename = None

            for frame_info in stack[:25]:  # 检查前25个栈帧
                filename = frame_info.filename
                basename = os.path.basename(filename)

                # 检查是否是tests_pz或tests_ph目录下的测试文件
                if (basename.startswith('test_') and
                    filename.endswith('.py') and
                    ('tests_pz' in filename or 'tests_ph' in filename) and
                    basename != 'log_handle.py'):  # 排除自身

                    test_file_basename = basename
                    break

            if test_file_basename and test_file_basename not in self._logged_error_files:
                self._logged_error_files.add(test_file_basename)
                # 直接记录完整的测试文件名
                self.log_error(f"📁测试接口: {test_file_basename}")
        except Exception as e:
            # 如果获取文件名失败，不影响主要功能
            pass

    def _check_response_time(self, response_times):
        """检查响应时间,超过1秒记录ERROR级别日志"""
        if not response_times:
            return False

        has_time_errors = False
        old_time = response_times.get("old", 0)
        new_time = response_times.get("new", 0)

        # 检查是否有响应时间超过1000ms(1秒)
        if old_time > 1000:
            self.log_error(f"⚠️ 基准服务器响应时间超过1秒: {old_time:.2f}ms")
            has_time_errors = True

        if new_time > 1000:
            self.log_error(f"⚠️ 新服务器响应时间超过1秒: {new_time:.2f}ms")
            has_time_errors = True

        return has_time_errors



    def _get_current_test_file_stats(self):
        """
        获取当前测试文件的统计信息
        只获取tests目录下的测试文件统计
        """
        try:
            stack = inspect.stack()
            for frame_info in stack:
                filename = frame_info.filename
                basename = os.path.basename(filename)
                # 只获取tests目录下的以test_开头的Python文件统计
                if (filename.endswith('.py') and
                    basename.startswith('test_') and
                    ('tests_ph' in filename or 'tests_pz' in filename) and  # 必须在tests目录中
                    basename != 'log_handle.py' and
                    basename in self._test_stats):
                    return self._test_stats[basename]
        except Exception as e:
            pass
        return None

    def log_test_result(self, server_info, response_times, request_params, has_errors=False, error_details=None):
        """根据测试结果记录日志"""
        if error_details is None:
            error_details = []

        # 自动记录测试接口编号(只记录一次)
        try:
            self._log_test_file_name_once()
        except Exception as e:
            # 如果记录测试接口失败,不影响主要功能
            pass

        # 更新测试统计
        self._update_test_stats(failed=has_errors)

        # 自动检查响应时间(超过1秒记录ERROR)
        response_time_errors = self._check_response_time(response_times)

        # 判断是否有任何错误（包括响应时间错误）
        has_any_errors = has_errors or response_time_errors

        # 获取服务器配置信息
        try:
            from config import SERVER_CONFIG
            old_server_desc = SERVER_CONFIG["old_server"]["description"]
            new_server_desc = SERVER_CONFIG["new_server"]["description"]
            old_server_addr = f"{SERVER_CONFIG['old_server']['host']}:{SERVER_CONFIG['old_server']['port']}"
            new_server_addr = f"{SERVER_CONFIG['new_server']['host']}:{SERVER_CONFIG['new_server']['port']}"
        except ImportError:
            # 如果无法导入config，使用默认描述
            old_server_desc = "基准服务器"
            new_server_desc = "新服务器"
            old_server_addr = server_info.get("old", "unknown") if server_info else "unknown"
            new_server_addr = server_info.get("new", "unknown") if server_info else "unknown"

        # 记录基本测试信息(所有测试都记录)
        self.log_info(f"🔗 服务器信息: 新服务器={new_server_addr}, 基准服务器={old_server_addr}")

        # 记录响应时间对比(所有测试都记录)
        if response_times and "old" in response_times and "new" in response_times:
            old_time = response_times["old"]
            new_time = response_times["new"]
            time_diff = new_time - old_time
            time_diff_percent = (time_diff / old_time * 100) if old_time > 0 else 0
            self.log_info(f"⏱️  响应时间: 新服务器={new_time:.2f}ms, 基准服务器={old_time:.2f}ms (差异: {time_diff:+.2f}ms, {time_diff_percent:+.1f}%)")

        # 记录请求参数(所有测试都记录)
        if request_params:
            self.log_info(f"📋 请求参数: {request_params}")

        # 如果有错误，记录错误详情
        if has_any_errors and (error_details or response_time_errors):
            # 在ERROR日志前记录接口编号(ERROR级别,只记录一次)
            self._log_test_file_name_error_once()

            # 记录错误详情
            for error in error_details:
                self.log_error(error)
        else:
            self.log_info("✅ 测试通过")

    def log_test_file_summary(self, test_file=None):
        """记录测试文件的汇总结果"""
        if test_file is None:
            stats = self._get_current_test_file_stats()
        else:
            stats = self._test_stats.get(test_file)
        if stats and stats['total_tests'] > 0:
            if stats['failed_tests'] == 0 and stats['passed_tests'] == stats['total_tests']:
                self.log_info("✅测试结果: 测试全部通过")

    def log_debug(self, message):
        """记录DEBUG级别日志"""
        self.logger.debug(message)

    def log_server_comparison_start(self, server1_info, server2_info, method_name, request_params=None):
        """记录服务器对比开始"""
        separator = "=" * 80
        self.logger.info(separator)
        self.logger.info(f"🔄 开始服务器对比")
        self.logger.info(f"📡 服务器1: {server1_info['id']} ({server1_info['address']}) - {server1_info['description']}")
        self.logger.info(f"📡 服务器2: {server2_info['id']} ({server2_info['address']}) - {server2_info['description']}")
        self.logger.info(f"🔧 调用方法: {method_name}")
        if request_params:
            self.logger.info(f"📋 请求参数: {request_params}")
        self.logger.info(separator)

    def log_server_comparison_success(self, server1_info, server2_info, method_name):
        """记录服务器对比成功"""
        self.logger.info(f"✅ 对比成功: {server1_info['id']} vs {server2_info['id']} - {method_name}")

    def log_server_comparison_difference(self, server1_info, server2_info, method_name, differences, request_params=None):
        """记录服务器对比差异"""
        separator = "⚠" * 80
        self.logger.error(separator)
        self.logger.error(f"❌ 发现服务器对比差异")
        self.logger.error(f"📡 服务器1: {server1_info['id']} ({server1_info['address']}) - {server1_info['description']}")
        self.logger.error(f"📡 服务器2: {server2_info['id']} ({server2_info['address']}) - {server2_info['description']}")
        self.logger.error(f"🔧 调用方法: {method_name}")
        if request_params:
            self.logger.error(f"📋 请求参数: {request_params}")

        self.logger.error(f"🔍 发现 {len(differences)} 处差异:")
        for i, diff in enumerate(differences, 1):
            self.logger.error(f"  {i}. 字段: {diff.get('field', 'Unknown')}")
            self.logger.error(f"     {server1_info['id']}: {diff.get('server1_value', 'N/A')}")
            self.logger.error(f"     {server2_info['id']}: {diff.get('server2_value', 'N/A')}")
            if diff.get('processing_type'):
                self.logger.error(f"     处理方式: {diff['processing_type']}")
            if diff.get('additional_info'):
                self.logger.error(f"     附加信息: {diff['additional_info']}")

        self.logger.error(separator)

    def log_multi_server_comparison_summary(self, comparison_results, total_differences=0):
        """记录多服务器对比汇总信息"""
        separator = "📊" * 80
        self.logger.info(separator)
        self.logger.info(f"📊 多服务器对比汇总")
        self.logger.info(f"🔢 总对比组数: {len(comparison_results)}")

        for i, result in enumerate(comparison_results, 1):
            status = "✅ 成功" if result.get('success', False) else "❌ 失败"

            # 兼容新旧字段名
            server1_id = result.get('server1_id') or result.get('baseline_id', 'unknown')
            server2_id = result.get('server2_id') or result.get('candidate_id', 'unknown')

            self.logger.info(f"  {i}. {server1_id} vs {server2_id}: {status}")
            if not result.get('success', False) and result.get('difference_count', 0) > 0:
                self.logger.info(f"     差异数量: {result['difference_count']}")

        if total_differences > 0:
            self.logger.error(f"⚠️  总计发现 {total_differences} 处差异")
        else:
            self.logger.info(f"🎉 所有对比均通过，无差异")

        self.logger.info(separator)

    def clear_logs(self):
        """清空日志文件"""
        if os.path.exists(self.log_file):
            os.remove(self.log_file)
            self.logger.info("日志文件已清空")
        else:
            self.logger.warning("日志文件不存在")