# =============================================================================
# Python 相关
# =============================================================================

# 字节码文件
__pycache__/
*.py[cod]
*$py.class

# C 扩展
*.so

# 分发/打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 安装日志
pip-log.txt
pip-delete-this-directory.txt

# =============================================================================
# 测试和覆盖率
# =============================================================================

# 单元测试/覆盖率报告
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# 测试报告
reports/
_TestReport.html
*.html

# =============================================================================
# 开发环境
# =============================================================================

# 虚拟环境
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE 配置
.vscode/
.idea/
*.swp
*.swo
*~

# Python 版本管理
.python-version

# =============================================================================
# 操作系统
# =============================================================================

# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes

# Windows
ehthumbs.db
Thumbs.db

# =============================================================================
# 项目特定文件
# =============================================================================

# 日志文件
logs/
*.log

# 备份文件
tests_backup_*/
*.bak
*.backup

# 临时文件
temp/
*.tmp
*.temp

# 调试文件
debug_*

# 测试临时文件
test_*_directly.py
simple_*_test.py

# 配置文件（如果包含敏感信息）
# config_local.py

# 数据文件
*.txt
*.csv
*.json
*.xml

# 其他不需要版本控制的文件
*.orig
