import pytest
from common.assert_response import assert_fieldids

# 常量定义 (保持不变)
MAX_FIELD_ID = 600
DEFINED_FIELD_IDS = (
        list(range(0, 160)) +
        list(range(205, 225)) +
        [281, 282, 293, 391, 392, 396, 397, 399] +
        list(range(400, 406)) + [408, 414, 415, 416, 417, 435] +
        list(range(482, 487)) +
        [133, 134, 136, 137, 138, 305, 308, 311, 315] +
        [294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 393, 398, 434] +
        list(range(419, 435)) +
        [450, 455, 460, 465, 470, 475, 476, 477, 478, 479, 480, 481] +
        list(range(533, 564)) + [565, 567, 568, 569] +
        list(range(572, 582)) +
        list(range(585, 588)) +
        list(range(592, 598)) +
        [600]
)


@pytest.mark.parametrize("key", [6582227395838651953])
@pytest.mark.parametrize("fieldids", [
    [i] for i in DEFINED_FIELD_IDS
])
class TestSortShqExReq:
    def test_sort_shq_ex(self, api_helper, key, fieldids, logger):
        """测试 sort_shq_ex 接口，比较旧接口和新接口的响应一致性"""

        # 请求参数字典，用于日志记录
        params = {"key": key, "fieldids": fieldids}
        print(f"请求参数: key={key}, fieldids={fieldids}")

        # 并发调用新旧接口
        response, new_response = api_helper.call_method_concurrently(
            'get_sort_shq_ex_req', keys=[key], fieldids=fieldids
        )

        all_errors = []

        # 获取响应时间和服务器信息
        response_times = api_helper.get_last_response_times()
        server_info = api_helper.get_last_server_info()

        for response, new_response in zip(response.fields[0].exdata, new_response.fields[0].exdata):

            try:
                assert_fieldids(
                    response,
                    new_response,
                    ['value'],
                    request_params=params,
                    server_info=server_info,
                    logger=logger,
                    response_times=response_times
                )
                print("测试通过")
            except Exception as e:
                # 收集错误但不立即记录日志
                all_errors.append(str(e))  # 保存错误信息

        # 使用新的日志记录规则
        logger.log_test_result(
            server_info=server_info,
            response_times=response_times,
            request_params=params,
            has_errors=bool(all_errors),
            error_details=all_errors
        )

        if all_errors:
            # 合并所有错误信息
            combined_error = "\n".join([f"共发现 {len(all_errors)} 处断言失败:"] + all_errors)
            raise AssertionError(combined_error)
