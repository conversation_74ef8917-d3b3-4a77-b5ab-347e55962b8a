# 竞价数据测试参数配置
# 格式: [code, setcode, nkey, type_id]

kline_test_params:
  # 深市股票测试场景 (setcode=0)
  shenzhen_stocks:
    - name: "SZ-000001-auction"
      params: ["000001", 0, 0, 1]
      description: "深市主板-平安银行"
    - name: "SZ-300001-auction"
      params: ["300001", 0, 0, 1]
      description: "创业板-特锐德"
    - name: "SZ-300879-auction"
      params: ["300879", 0, 0, 1]
      description: "创业板-上海谊众"
    - name: "SZ-002490-auction"
      params: ["002490", 0, 0, 1]
      description: "中小板-山东墨龙"
    - name: "SZ-300338-auction"
      params: ["300338", 0, 0, 1]
      description: "创业板-开元股份"

  # 沪市股票测试场景 (setcode=1)
  shanghai_stocks:
    - name: "SH-600000-auction"
      params: ["600000", 1, 0, 1]
      description: "沪市主板-浦发银行-开盘竞价"
    - name: "SH-601318-auction"
      params: ["601318", 1, 0, 1]
      description: "沪市主板-中国平安-尾盘竞价"
    - name: "SH-688981-auction"
      params: ["688981", 1, 0, 1]
      description: "科创板-中芯国际-开盘竞价"
    - name: "SH-603259-auction"
      params: ["603259", 1, 0, 1]
      description: "沪市主板-药明康德-尾盘竞价"
    - name: "SH-601888-auction"
      params: ["601888", 1, 0, 1]
      description: "沪市主板-中国中免-尾盘竞价"

# 测试字段配置
test_fields:
  quote_fields: ['quoteTime']
  volume_fields: ['vol', 'unSuitVol']
  float_fields: ['price']

# 测试配置
test_config:
  method_name: 'get_auction_data'
  quote_time_length: 10
  decimal_places: 2
  
# 字段处理配置
field_processing:
  quote_time_truncate: 10  # quoteTime字段截断长度
  price_decimal_places: 2  # price字段保留小数位数
