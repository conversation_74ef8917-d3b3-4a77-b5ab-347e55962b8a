# 历史分钟线数据测试参数配置
# 格式: [code, setcode, date]

kline_test_params:
  # 沪市股票测试场景
  shanghai_stocks:
    - name: "SH-600000-recent"
      params: ["600000", 1, 20230315]
      description: "沪市股票-近期日期"
    - name: "SH-601318-history"
      params: ["601318", 1, 20220104]
      description: "沪市股票-历史日期"
    - name: "SH-000001-recent"
      params: ["000001", 1, 20250505]
      description: "上证指数-近期日期"
    - name: "SH-600887-old"
      params: ["600887", 1, 20200715]
      description: "沪市其他股票-更早历史日期"

  # 深市股票测试场景
  shenzhen_stocks:
    - name: "SZ-000001-yearend"
      params: ["000001", 0, 20221230]
      description: "深市股票-年末日期(已调整)"
    - name: "SZ-300750-normal"
      params: ["300750", 0, 20211008]
      description: "创业板股票-正常日期"
    - name: "SZ-002001-newyear"
      params: ["002001", 0, 20250102]
      description: "深市主板股票-年初日期"
    - name: "SZ-300059-specific"
      params: ["300059", 0, 20231110]
      description: "创业板其他股票-特定历史日期"

  # 科创板和北交所
  special_boards:
    - name: "STAR-688001-recent"
      params: ["688001", 1, 20250415]
      description: "科创板股票-近期日期"
    - name: "BJ-830001-history"
      params: ["830001", 2, 20241108]
      description: "北交所股票-历史日期"

  # 指数测试场景
  indices:
    - name: "SZ-000300-normal"
      params: ["000300", 0, 20230601]
      description: "沪深300指数-正常日期"
    - name: "SZ-399001-normal"
      params: ["399001", 0, 20230515]
      description: "深证成指-正常日期"
    - name: "SZ-399006-history"
      params: ["399006", 0, 20240301]
      description: "创业板指-历史日期"

# 测试字段配置
test_fields:
  float_fields: ["openPrice", "highPrice", "nowPrice", "lowPrice"]
  int_fields: ["jclTime", "amount", "volume", "buyvol", "sellvol"]
  special_fields: ["avePrice"]  # 需要特殊处理的字段

# 测试配置
test_config:
  method_name: 'get_his_minute_data'
  response_attribute: 'minutes'
  decimal_places: 2
  ave_price_tolerance: 2  # avePrice字段允许的容差
  
# 字段处理配置
field_processing:
  ave_price_tolerance: 2  # avePrice字段整数部分允许±2的容差
  decimal_places: 2  # 浮点数字段保留小数位数
