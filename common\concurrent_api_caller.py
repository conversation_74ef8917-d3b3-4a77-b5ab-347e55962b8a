"""并发API调用工具"""

import time
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Callable, Any, Tu<PERSON>, Optional
from dataclasses import dataclass


@dataclass
class ApiCallResult:
    """API调用结果"""
    success: bool
    result: Any = None
    error: Optional[Exception] = None
    execution_time: float = 0.0
    call_type: str = ""
    server_info: str = ""
    api_name: str = ""
    request_time: float = 0.0
    response_time: float = 0.0
    network_latency: float = 0.0


class ConcurrentApiCaller:
    """并发API调用器"""

    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)

    def call_apis_concurrently(self, old_api_func: Callable, new_api_func: Callable,
                              *args, **kwargs) -> Tuple[ApiCallResult, ApiCallResult]:
        """并发执行新旧两个API调用"""
        def execute_api_call(api_func: Callable, call_type: str, api_args: tuple, api_kwargs: dict) -> ApiCallResult:
            """执行单个API调用"""
            start_time = time.time()
            request_time = time.time()

            try:
                result = api_func(*api_args, **api_kwargs)
                response_time = time.time()
                execution_time = (response_time - start_time) * 1000  # 转换为毫秒
                
                # 获取服务器信息
                server_info = ""
                if hasattr(api_func, '__self__') and hasattr(api_func.__self__, 'server_info'):
                    server_info = api_func.__self__.server_info
                
                return ApiCallResult(
                    success=True,
                    result=result,
                    execution_time=execution_time,
                    call_type=call_type,
                    api_name=api_func.__name__ if hasattr(api_func, '__name__') else str(api_func),
                    request_time=request_time,
                    response_time=response_time,
                    network_latency=execution_time,
                    server_info=server_info
                )
            except Exception as e:
                response_time = time.time()
                execution_time = (response_time - start_time) * 1000
                
                return ApiCallResult(
                    success=False,
                    error=e,
                    execution_time=execution_time,
                    call_type=call_type,
                    api_name=api_func.__name__ if hasattr(api_func, '__name__') else str(api_func),
                    request_time=request_time,
                    response_time=response_time,
                    network_latency=execution_time
                )
        
        # 使用线程池并发执行
        with ThreadPoolExecutor(max_workers=2) as executor:
            # 提交任务
            old_future = executor.submit(execute_api_call, old_api_func, "old", args, kwargs)
            new_future = executor.submit(execute_api_call, new_api_func, "new", args, kwargs)
            
            # 等待结果
            old_result = old_future.result()
            new_result = new_future.result()
        
        return old_result, new_result


class ApiCallHelper:
    """API调用助手类，提供便捷的调用方法"""

    def __init__(self, old_client, new_client, logger=None):
        self.old_client = old_client
        self.new_client = new_client
        self.caller = ConcurrentApiCaller(logger)
        self.logger = logger or logging.getLogger(__name__)
    
    def call_method_concurrently(
        self,
        method_name: str,
        *args,
        old_method: str = None,
        new_method: str = None,
        **kwargs
    ) -> Tuple[Any, Any]:
        """
        并发调用新旧客户端的方法

        Args:
            method_name: 同名方法名（当新旧接口方法名相同时使用）
            old_method: 旧接口方法名（当新旧接口方法名不同时使用）
            new_method: 新接口方法名（当新旧接口方法名不同时使用）
            *args: 位置参数
            **kwargs: 关键字参数

        Returns:
            Tuple[Any, Any]: (旧接口结果, 新接口结果)

        Raises:
            Exception: 如果任一接口调用失败
        """
        # 确定方法名
        if method_name:
            # 使用同名方法
            old_method_name = new_method_name = method_name
        elif old_method and new_method:
            # 使用不同方法名
            old_method_name = old_method
            new_method_name = new_method
        else:
            raise ValueError("必须提供 method_name 或者同时提供 old_method 和 new_method")

        old_api_method = getattr(self.old_client, old_method_name)
        new_api_method = getattr(self.new_client, new_method_name)

        old_result, new_result = self.caller.call_apis_concurrently(
            old_api_method, new_api_method, *args, **kwargs
        )

        # 检查调用结果
        if not old_result.success:
            raise old_result.error or Exception(f"旧接口{old_method_name}调用失败")

        if not new_result.success:
            raise new_result.error or Exception(f"新接口{new_method_name}调用失败")

        # 存储响应时间信息，供断言函数使用
        self._last_response_times = {
            "old": old_result.execution_time,
            "new": new_result.execution_time
        }
        self._last_server_info = {
            "old": f"{self.old_client.host}:{self.old_client.port}",
            "new": f"{self.new_client.host}:{self.new_client.port}"
        }

        return old_result.result, new_result.result

    def get_last_response_times(self):
        """获取最后一次调用的响应时间"""
        return getattr(self, '_last_response_times', None)

    def get_last_server_info(self):
        """获取最后一次调用的服务器信息"""
        return getattr(self, '_last_server_info', None)
