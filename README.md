# 🚀 金融行情API并发测试框架

一个专门用于对比测试新旧行情接口数据一致性和性能的自动化测试框架。

## ✨ 核心特性

- **🔄 并发执行**: 同时调用新旧接口，确保时间同步
- **📊 数据对比**: 自动对比响应数据的一致性
- **⏱️ 性能分析**: 记录和对比接口响应时间
- **📝 详细日志**: 记录所有差异和错误信息
- **📋 HTML报告**: 生成美观的测试报告
- **🔧 Jenkins集成**: 支持CI/CD自动化部署

## 📁 项目结构

```
request_newapi_app/
├── common/                        # 核心功能模块
│   ├── request_api_new.py        # 主要的API客户端
│   ├── concurrent_api_caller.py  # 并发调用工具
│   ├── assert_response.py        # 断言工具集
│   ├── log_handle.py             # 统一日志管理器
│   ├── test_base.py              # 测试基类
│   ├── test_utils.py             # 测试工具函数
│   └── unified_assert.py         # 统一断言模块
├── tests/                         # 测试用例
│   ├── test_multi_hq_1728.py     # 多品种行情测试
│   ├── test_kline_data_22010.py  # K线数据测试
│   └── ...                       # 更多测试文件

├── logs/                          # 日志文件目录
├── reports/                       # 测试报告目录
├── base.proto                     # Protocol Buffers定义
├── JCLBean.proto                 # 业务数据结构定义
├── config.py                     # 配置文件
├── conftest.py                   # pytest配置
├── runner.py                     # 测试运行器
├── Jenkinsfile                   # Jenkins Pipeline配置
├── requirements.txt              # 依赖包列表
└── TEST_OPTIMIZATION_SUMMARY.md  # 测试优化总结
```

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置服务器

编辑 `config.py` 文件，配置新旧服务器地址：

```python
SERVER_CONFIG = {
    "old_server": {
        "host": "*************",
        "port": 5066,
        "description": "旧接口服务器"
    },
    "new_server": {
        "host": "**************", 
        "port": 5066,
        "description": "新接口服务器"
    }
}
```

### 3. 运行测试

```bash
# 运行所有测试并生成HTML报告
python runner_pz.py

# 或者直接使用pytest命令
pytest --report=_TestReport.html --title=测试报告 --tester=测试员 --desc=项目描述 --template=1

# 运行特定测试
pytest tests_pz/ -k multi_hq -v --tb=short
```

### 4. 查看测试报告

运行测试后，会在项目根目录生成 `_TestReport.html` 文件，双击打开即可查看详细的测试报告。

报告包含：
- 📊 测试统计信息（通过/失败/跳过）
- 📝 详细的测试结果
- ⏱️ 测试执行时间
- 🔍 失败测试的详细错误信息

## 📋 支持的API接口

- **多品种行情 (get_multi_hq)**: 获取多个证券的实时行情
- **K线数据 (get_kline_data)**: 获取K线历史数据
- **代码链 (get_code_list)**: 获取市场代码信息
- **分时数据 (get_minute_data)**: 获取分时行情数据
- **资金流向 (get_money_flow)**: 获取资金流向数据
- **排行榜 (get_sorted_hq)**: 获取排序行情数据
- **逐笔成交 (get_tick_data)**: 获取逐笔成交数据

## 🔧 测试用例编写

### 基本测试结构

```python
def test_api_comparison(api_helper, logger):
    """测试API对比"""
    # 并发调用新旧接口
    response, new_response = api_helper.call_method_concurrently(
        'get_multi_hq', codes=[{"nkey": 0, "setcode": 0, "code": "000001"}]
    )
    
    # 断言对比
    assert_response_equal(response, new_response, ['field1', 'field2'])
```

### 自定义断言

```python
# 基础断言
assert_response_equal(response, new_response, ['field1', 'field2'])

# 浮点数精度断言
assert_round_response(response, new_response, fields, decimal_places=3)

# 容差断言
assert_tolerance_equal(response, new_response, fields, tolerance=10)
```

## 📊 测试报告

运行测试后会生成HTML报告：

```bash
# 报告位置
reports/report.html
```

## ⚙️ 配置说明

### 服务器配置

在 `config.py` 中配置服务器地址：

```python
SERVER_CONFIG = {
    "old_server": {
        "host": "*************",
        "port": 5066,
        "description": "旧接口服务器"
    },
    "new_server": {
        "host": "**************", 
        "port": 5066,
        "description": "新接口服务器"
    }
}
```

### 日志配置

日志会自动保存到 `logs/` 目录，支持不同级别的日志输出。

## 🔧 Jenkins部署

### 1. 自动部署

项目包含完整的Jenkins Pipeline配置，支持：

- **定时任务**: 每天8点和18点自动执行
- **代码检查**: 每15分钟检查Git变更
- **邮件通知**: 自动发送测试结果
- **HTML报告**: 生成详细的测试报告

### 2. 部署步骤

详细部署步骤请参考：[Jenkins部署指南](JENKINS_DEPLOYMENT_GUIDE.md)

## 🛠️ 开发指南

### 添加新的测试

1. 在 `tests/` 目录创建新的测试文件
2. 使用 `api_helper` fixture 进行并发调用
3. 使用相应的断言方法验证结果

### 扩展断言方法

在 `common/assert_response.py` 中添加新的断言方法。

## 📈 性能优势

- **时间同步精度**: < 1ms
- **测试效率提升**: ~50%
- **数据对比准确性**: 消除网络延迟差异
- **并发处理能力**: 支持多种API同时测试

## 🔍 故障排除

### 常见问题

1. **连接超时**: 检查网络连接和服务器状态
2. **认证失败**: 验证服务器地址和端口
3. **数据不一致**: 检查字段映射和精度设置

### 调试技巧

```bash
# 详细日志输出
python main.py --run --verbose

# 运行单个测试
python -m pytest tests_pz/test_multi_1728.py -v
```

## 📞 技术支持

如有问题，请联系：
- **开发者**: weizhen
- **邮箱**: <EMAIL>

## 📄 许可证

本项目仅供内部使用。
