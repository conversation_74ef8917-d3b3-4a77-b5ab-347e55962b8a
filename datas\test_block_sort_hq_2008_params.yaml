# 板块、指数成分股接口测试参数配置 (2008)
# 格式: [code, setcode, coltype, startxh, wantnum, sorttype, fieldids]

kline_test_params:
  # 沪深300指数测试
  csi300_tests:
    - name: "CSI300-000300-coltype14-sort2"
      params: ["000300", 0, 14, 1, 20, 2, [14, 39, 46, 48]]
      description: "沪深300指数-coltype14-排序2-20条"
    - name: "CSI300-000300-coltype10-sort1"
      params: ["000300", 0, 10, 1, 50, 1, [10, 12, 14, 39]]
      description: "沪深300指数-coltype10-排序1-50条"

  # 板块测试
  sector_tests:
    - name: "SECTOR-600000-coltype46-sort1"
      params: ["600000", 7, 46, 1, 7, 1, [10, 46, 48]]
      description: "板块600000-coltype46-排序1-7条"

# 测试字段配置
test_fields:
  hq_fields:
    - 'nkey'
    - 'Status'
    - 'InOutFlag'
    - 'TickCount'
    - 'PreClosePrice'
    - 'OpenPrice'
    - 'HighPrice'
    - 'LowPrice'
    - 'NowPrice'
    - 'zangsu'
    - 'AveragePrice'
    - 'LimitUpPrice'
    - 'LimitDownPrice'
    - 'PERatio'
    - 'Volume'
    - 'NowVol'
    - 'Amount'
    - 'NowAmount'
    - 'Inside'
    - 'Outside'
    - 'AllBuyPriceCount'
    - 'AllSellPriceCount'
    - 'BuyPrice'
    - 'BuyVolume'
    - 'SellPrice'
    - 'SellVolume'

# 测试配置
test_config:
  method_name: 'get_block_sort_hq'
  response_structure: 'fields'
  assert_function: 'assert_fieldids'
  
# 字段处理配置
field_processing:
  # TickCount字段10以内的差异忽略
  tickcount_tolerance: 10
  comparison_mode: 'fieldids'  # 使用fieldids专用断言
  
# 接口说明
interface_info:
  description: "2008 板块、指数成分股接口"
  msg_id: 2008
  parameters: "code, setcode, coltype, startxh, wantnum, sorttype, fieldids"
  response_structure: "fields[].hq"
  notes: "TickCount 10以内的差异忽略，使用assert_fieldids进行比较"
